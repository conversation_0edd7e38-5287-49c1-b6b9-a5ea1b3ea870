import type { AppProps } from "next/app";
import "@fontsource/open-sans";
import "@fontsource/open-sans/600.css";
import "@fontsource/open-sans/700.css";
import "antd/dist/antd.css";
import "react-toastify/dist/ReactToastify.css";
import "@assets/main.css";
import { UIProvider } from "@contexts/ui.context";
import { SettingsProvider } from "@contexts/settings.context";

import { ToastContainer } from "react-toastify";
import { QueryClient, QueryClientProvider } from "react-query";
import { Hydrate } from "react-query/hydration";
import { useEffect, useRef } from "react";

import { ReactQueryDevtools } from "react-query/devtools";
import { appWithTranslation } from "next-i18next";
import { ModalProvider } from "@components/ui/modal/modal.context";
import DefaultSeo from "@components/ui/default-seo";
import PrivateRoute from "@utils/private-route";
import ManagedModal from "@components/ui/modal/managed-modal";
import { BranchProvider } from "@contexts/branch.context";
import { queryClientConfig } from "@settings/query.settings";
import { useMeQuery } from "@data/user/use-me.graphql.query";
import { getAuthCredentials, setAuthCredentials } from "@utils/auth-utils";

const Noop: React.FC = ({ children }) => <>{children}</>;

const AppSettings: React.FC = (props) => {
  const { token } = getAuthCredentials();
  const { data: meData, isLoading } = useMeQuery();

  useEffect(() => {
    if (!isLoading && meData) {
      const {
        data: { me },
      } = meData;
      if (me) {
        const { permissions, roles: rolesData } = me;
        const roles = rolesData.map(({ name }) => name);
        setAuthCredentials(token!, roles, permissions);
      }
    }
  }, [meData, isLoading]);

  return <SettingsProvider initialValue={{}} {...props} />;
};

const CustomApp = ({ Component, pageProps }: AppProps) => {
  const queryClientRef = useRef<any>(null);
  if (!queryClientRef.current) {
    queryClientRef.current = new QueryClient(queryClientConfig);
  }
  const Layout = (Component as any).Layout || Noop;
  const authProps = (Component as any).authenticate;

  return (
    <QueryClientProvider client={queryClientRef.current}>
      <Hydrate state={pageProps.dehydratedState}>
        <AppSettings>
          <UIProvider>
            <BranchProvider>
              <ModalProvider>
                <>
                  <DefaultSeo />
                  {authProps ? (
                    <PrivateRoute authProps={authProps}>
                      <Layout {...pageProps}>
                        <Component {...pageProps} />
                      </Layout>
                    </PrivateRoute>
                  ) : (
                    <Layout {...pageProps}>
                      <Component {...pageProps} />
                    </Layout>
                  )}
                  <ToastContainer autoClose={2000} hideProgressBar={true} />
                  <ManagedModal />
                </>
              </ModalProvider>
            </BranchProvider>
          </UIProvider>
        </AppSettings>
        <ReactQueryDevtools />
      </Hydrate>
    </QueryClientProvider>
  );
};

export default appWithTranslation(CustomApp);
