const withPWA = require('next-pwa');
const runtimeCaching = require('next-pwa/cache');
const { withPlugins } = require('next-compose-plugins');
module.exports = withPlugins(
  [],
  withPWA({
    pwa: {
      disable: process.env.NEXT_PUBLIC_NODE_ENV === 'development',
      dest: 'public',
      runtimeCaching,
    },
    images: {
      domains: ['picsum.photos'],
    },
    typescript: {
      ignoreBuildErrors: true,
    },
    eslint: {
      ignoreDuringBuilds: true,
    },

    webpack5: true,
    async rewrites() {
      return [
        {
          source: '/track-order/:order_id', // Dynamic route pattern
          destination: '/track-order/view?order_id=:order_id', // Query parameter
        },
      ];
    },
  })
);
