import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'my.supershoppe.app',
  appName: 'Super Shoppe',
  webDir: 'out',
  bundledWebRuntime: false,
  ios: {
    scheme: 'Super Shoppe',
  },
  plugins: {
    CapacitorCookies: {
      enabled: true,
    },
    PrivacyScreen: {
      enable: false,
    },
    PushNotifications: {
      presentationOptions: ['badge', 'sound', 'alert'],
    },
  },
};

export default config;
