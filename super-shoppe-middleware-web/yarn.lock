# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.1.0":
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/@ampproject/remapping/-/remapping-2.2.0.tgz"
  integrity "sha1-VsEzgkeA3jF0rtWraDTzAmeQFU0= sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w=="
  dependencies:
    "@jridgewell/gen-mapping" "^0.1.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@apideck/better-ajv-errors@^0.3.1":
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/@apideck/better-ajv-errors/-/better-ajv-errors-0.3.3.tgz"
  integrity "sha1-qwsemB4XSb9Zc2z36+Jc/J+UnBU= sha512-9o+HO2MbJhJHjDYZaDxJmSDckvDpiuItEsrIShV0DXeCshXWRHhqYyU/PKHMkuClOmFnZhRd6wzv4vpDu/dRKg=="
  dependencies:
    json-schema "^0.4.0"
    jsonpointer "^5.0.0"
    leven "^3.1.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/code-frame/-/code-frame-7.16.7.tgz"
  integrity "sha1-REFra9diS5mPWxr11HCFbEATh4k= sha512-iAXqUn8IIeBTNd72xsFlgaXHkMBMt6y4HJp1tIaK465CWLT/fG1aqB7ykr95gHHmlBdGbFeWWfyB4NJJ0nmeIg=="
  dependencies:
    "@babel/highlight" "^7.16.7"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.17.10":
  version "7.17.10"
  resolved "https://registry.yarnpkg.com/@babel/compat-data/-/compat-data-7.17.10.tgz"
  integrity "sha1-cR3HJqSS38i+giACixuSSCNiuqs= sha512-GZt/TCsG70Ms19gfZO1tM4CVnXsPgEPBCpJu+Qz3L0LUDsY5nZqFZglIoPC1kIYOtNBZlrnFT+klg12vFGZXrw=="

"@babel/core@^7.11.1":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/core/-/core-7.18.0.tgz"
  integrity "sha1-xY0E18b7+1jqdoHiuRRc+2JyZ1Y= sha512-Xyw74OlJwDijToNi0+6BBI5mLLR5+5R3bcSH80LXzjzEGEUlvNzujEE71BaD/ApEZHAvFI/Mlmp4M5lIkdeeWw=="
  dependencies:
    "@ampproject/remapping" "^2.1.0"
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.18.0"
    "@babel/helper-compilation-targets" "^7.17.10"
    "@babel/helper-module-transforms" "^7.18.0"
    "@babel/helpers" "^7.18.0"
    "@babel/parser" "^7.18.0"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.18.0"
    "@babel/types" "^7.18.0"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.1"
    semver "^6.3.0"

"@babel/generator@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/generator/-/generator-7.18.0.tgz"
  integrity "sha1-RtKOihj8c3sCjvslqxBddEc69D8= sha512-81YO9gGx6voPXlvYdZBliFXAZU8vZ9AZ6z+CjlmcnaeOcYSFbMTpdeDUO9xD9dh/68Vq03I8ZspfUTPfitcDHg=="
  dependencies:
    "@babel/types" "^7.18.0"
    "@jridgewell/gen-mapping" "^0.3.0"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.7.tgz"
  integrity "sha1-uyM5p1NKnBKOMQICTGB2Cjp/OGI= sha512-s6t2w/IPQVTAET1HitoowRGXooX8mCgtuP5195wD/QJPV6wYjpujCGF7JuMODVX2ZAJOf1GT6DT9MHEZvLOFSw=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.16.7.tgz"
  integrity "sha1-ONE4Vh6iB/D2nrFiakGOT35qWAs= sha512-C6FdbRaxYjwVu/geKW4ZeQ0Q31AftgRcdSnZ5/jsH6BzCJbtvXvhpfkbkThYSuutZA7nCXpPR6AD9zd1dprMkA=="
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.7", "@babel/helper-compilation-targets@^7.17.10":
  version "7.17.10"
  resolved "https://registry.yarnpkg.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.17.10.tgz"
  integrity "sha1-CcYxBtR6+TzzGAPba8Sf7zVOLr4= sha512-gh3RxjWbauw/dFiU/7whjd0qN9K6nPJMqe6+Er7rOavFh0CQUSwhAE3IcTho2rywPJFxej6TUUHDkWcYI6gGqQ=="
  dependencies:
    "@babel/compat-data" "^7.17.10"
    "@babel/helper-validator-option" "^7.16.7"
    browserslist "^4.20.2"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.17.12", "@babel/helper-create-class-features-plugin@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.18.0.tgz"
  integrity "sha1-+sQwkSYGMxywdeqNgvmkwUWk2hk= sha512-Kh8zTGR9de3J63e5nS0rQUdRs/kbtwoeQQ0sriS0lItjC96u8XXZN6lKpuyWd2coKSU13py/y+LTmThLuVX0Pg=="
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.17.9"
    "@babel/helper-member-expression-to-functions" "^7.17.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"

"@babel/helper-create-regexp-features-plugin@^7.16.7", "@babel/helper-create-regexp-features-plugin@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.17.12.tgz"
  integrity "sha1-uzfKRn+WlLvlW4hK56XMHgCE5P0= sha512-b2aZrV4zvutr9AIa6/gA3wsZKRwTKYoDxYiFKcESS3Ug2GTXzwBEvMuuFLhCQpEnRXs1zng4ISAXSUxxKBIcxw=="
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    regexpu-core "^5.0.1"

"@babel/helper-define-polyfill-provider@^0.3.1":
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.1.tgz"
  integrity "sha1-UkEbRFvbLmdoaeWnSWDS04JtJmU= sha512-J9hGMpJQmtWmj46B3kBHmL38UhJGhYX7eqkcq+2gsstyYt341HmPeWspihX43yVRA0mS+8GGk2Gckc7bY/HCmA=="
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.16.7.tgz"
  integrity "sha1-/0hAlKg5venYnNY8ugF9eq6A7Nc= sha512-SLLb0AAn6PkUeAfKJCCOl9e1R53pQlGAfc4y4XuMRZfqeMYLE0dM1LMhqbGAlGQY0lfw5/ohoYWAe9V1yibRag=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-explode-assignable-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.16.7.tgz"
  integrity "sha1-EqbYUi/dg08ZToaK9jVOhlAkK3o= sha512-KyUenhWMC8VrxzkGP0Jizjo4/Zx+1nNZhgocs+gLzyZyB8SHidhoq9KK/8Ato4anhwsivfkBLftky7gvzbZMtQ=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-function-name@^7.16.7", "@babel/helper-function-name@^7.17.9":
  version "7.17.9"
  resolved "https://registry.yarnpkg.com/@babel/helper-function-name/-/helper-function-name-7.17.9.tgz"
  integrity "sha1-E2/NVLwdqC/LR1Zc8W/Y5ESx/xI= sha512-7cRisGlVtiVqZ0MW0/yFB4atgpGLWEHUVYnb448hZK4x+vih0YO5UoS11XIYtZYqHd0dIPMdUSv8q5K4LdMnIg=="
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/types" "^7.17.0"

"@babel/helper-hoist-variables@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.7.tgz"
  integrity "sha1-hryxmnelCce3fQ4iMj71iPpYwkY= sha512-m04d/0Op34H5v7pbZw6pSKP7weA6lsMvfiIAMeIvkY/R4xQtBSMFEigu9QTZ2qB/9l22vsxtM8a+Q8CzD255fg=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-member-expression-to-functions@^7.16.7", "@babel/helper-member-expression-to-functions@^7.17.7":
  version "7.17.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.17.7.tgz"
  integrity "sha1-o0ATtX2FQqjE/4uj90fAJFKk2MQ= sha512-thxXgnQ8qQ11W2wVUObIqDL4p148VMxkt5T/qpN5k2fboRyzFGFmKsTGViquyM5QHKUy48OZoca8kw4ajaDPyw=="
  dependencies:
    "@babel/types" "^7.17.0"

"@babel/helper-module-imports@^7.10.4", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz"
  integrity "sha1-JWEqgJGpmXBEYciiItDv7F0JFDc= sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-module-transforms@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/helper-module-transforms/-/helper-module-transforms-7.18.0.tgz"
  integrity "sha1-uvBd7HpYdfuSNb00yhi61OISIc0= sha512-kclUYSUBIjlvnzN2++K9f2qzYKFgjmnmjwL4zlmU5f8ZtzgWe8s0rUPSTGy2HmK4P8T52MQsS+HTQAgZd3dMEA=="
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-simple-access" "^7.17.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/helper-validator-identifier" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.18.0"
    "@babel/types" "^7.18.0"

"@babel/helper-optimise-call-expression@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.7.tgz"
  integrity "sha1-o041YGBau9MaGFRr0qrT5tmhdPI= sha512-EtgBhg7rd/JcnpZFXpBy0ze1YRfdm7BnBX4uKMBd3ixa3RGAE002JZB66FJyNH7g0F38U05pXmA5P8cBh7z+1w=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.17.12", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.17.12.tgz"
  integrity "sha1-hsI0faWsv1WDugoQrtTJv52pz5Y= sha512-JDkf04mqtN3y4iAbO1hv9U2ARpPyPL1zqyWs/2WG1pgSq9llHFjStX5jdxb84himgJm+8Ng+x0oiWF/nw/XQKA=="

"@babel/helper-remap-async-to-generator@^7.16.8":
  version "7.16.8"
  resolved "https://registry.yarnpkg.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.8.tgz"
  integrity "sha1-Kf+q3mijZ+LtCckJAZhpGNJeV+M= sha512-fm0gH7Flb8H51LqJHy3HJ3wnE1+qtYR2A99K06ahwrawLdOFsCEWjZOrYricXJHoPSudNKxrMBUPEIPxiIIvBw=="
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-wrap-function" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helper-replace-supers@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-replace-supers/-/helper-replace-supers-7.16.7.tgz"
  integrity "sha1-6fX18yrJBCnBpL3sDyMe8MKDirE= sha512-y9vsWilTNaVnVh6xiJfABzsNpgDPKev9HnAgz6Gb1p6UUwf9NepdlsV7VXGCftJM+jqD5f7JIEubcpLjZj5dBw=="
  dependencies:
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-member-expression-to-functions" "^7.16.7"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/traverse" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/helper-simple-access@^7.17.7":
  version "7.17.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-simple-access/-/helper-simple-access-7.17.7.tgz"
  integrity "sha1-qqRz3pK3mHxt+nzpp9lnRySCM2c= sha512-txyMCGroZ96i+Pxr3Je3lzEJjqwaRC9buMUgtomcrLe5Nd0+fk1h0LLA+ixUF5OW7AhHuQ7Es1WcQJZmZsz2XA=="
  dependencies:
    "@babel/types" "^7.17.0"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  version "7.16.0"
  resolved "https://registry.yarnpkg.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz"
  integrity "sha1-DuM4gHAUfDrgUeSH7KPrsOLouwk= sha512-+il1gTy0oHwUsBQZyJvukbB4vPMdcYBrFHa0Uc4AizLxbq6BOYC51Rv4tWocX9BLBDLZ4kc6qUFpQ6HRgL+3zw=="
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.7.tgz"
  integrity "sha1-C2SMDELanTkg2FrVhfJ3hiC4cms= sha512-xbWoy/PFoxSWazIToT9Sif+jJTlrMcndIsaOKvTA6u7QEo7ilkRZpjew18/W3c7nm8fXdUDXh02VXTbZ0pGDNw=="
  dependencies:
    "@babel/types" "^7.16.7"

"@babel/helper-validator-identifier@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz"
  integrity "sha1-6MYCQ4xKgZV1EkPakDHRYH0kfK0= sha512-hsEnFemeiW4D08A5gUAZxLBTXpZ39P+a+DGDsHw1yxqyQ/jzFEnxf5uTEGp+3bzAbNOxU1paTgYS4ECU/IgfDw=="

"@babel/helper-validator-option@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz"
  integrity "sha1-sgPOYs5f4VOJm2F8CJV96GDeTSM= sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ=="

"@babel/helper-wrap-function@^7.16.8":
  version "7.16.8"
  resolved "https://registry.yarnpkg.com/@babel/helper-wrap-function/-/helper-wrap-function-7.16.8.tgz"
  integrity "sha1-WK/aCHxM0jXekvfO7evKLEEnQgA= sha512-8RpyRVIAW1RcDDGTA+GpPAwV22wXCfKOoM9bet6TLkGIFTkRQSkH1nMQ5Yet4MpoXe1ZwHPVtNasc2w0uZMqnw=="
  dependencies:
    "@babel/helper-function-name" "^7.16.7"
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.16.8"
    "@babel/types" "^7.16.8"

"@babel/helpers@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/helpers/-/helpers-7.18.0.tgz"
  integrity "sha1-r/N8NZDeQhArVIQkRhRtAgWUY3A= sha512-AE+HMYhmlMIbho9nbvicHyxFwhrO+xhKB6AhRxzl8w46Yj0VXTZjEsAoBVC7rB2I0jzX+yWyVybnO08qkfx6kg=="
  dependencies:
    "@babel/template" "^7.16.7"
    "@babel/traverse" "^7.18.0"
    "@babel/types" "^7.18.0"

"@babel/highlight@^7.16.7":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/highlight/-/highlight-7.17.12.tgz"
  integrity "sha1-JX3lbuWvvSBFGsCnVoa2tAQlc1E= sha512-7yykMVF3hfZY2jsHZEEgLc+3x4o1O+fYyULu11GynEUQNwB6lua+IIQn1FiJxNucd5UlyJryrwsOh8PL9Sn8Qg=="
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.16.7", "@babel/parser@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/parser/-/parser-7.18.0.tgz"
  integrity "sha1-EKjU5la8ARKNKZp4eqAGzhqR4RI= sha512-AqDccGC+m5O/iUStSJy3DGRIUFu7WbY/CppZYwrEUB4N0tZlnI8CSTsgL7v5fHVFmUbRv2sd+yy27o8Ydt4MGg=="

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.17.12.tgz"
  integrity "sha1-HcozjKrvyjaGOcn/sJWvvU1CCx4= sha512-xCJQXl4EeQ3J9C4yOmpTrtVGmzpm2iSzyxbkZHw7UCnZBftHpF/hpII80uWVyVrc40ytIClHjgWGTG1g/yB+aw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.17.12.tgz"
  integrity "sha1-DUmOyPA3Sx4utUucssTHhxTHd1M= sha512-/vt0hpIw0x4b6BLKUkwlvEoiGZYYLNZ96CzyHYPbtG2jZGz6LBe7/V+drYrc/d+ovrF9NBi0pmtvmNb/FsWtRQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.17.12"

"@babel/plugin-proposal-async-generator-functions@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.17.12.tgz"
  integrity "sha1-CUpBfjHOfmkthLqwbI4qYHy+7wM= sha512-RWVvqD1ooLKP6IqWTA5GyFVX2isGEgC5iFxKzfYOIy/QEFdxYyCybBDtIGjipHpb9bDWHzcqGqFakf+mVmBTdQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-remap-async-to-generator" "^7.16.8"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.17.12.tgz"
  integrity "sha1-hPZcDMJH1G9AptqZqt1kODFdgKQ= sha512-U0mI9q8pW5Q9EaTHFPwSVusPMV/DV9Mm8p7csqROFLtIE9rBF5piLqyrBGigftALrBcsBGu4m38JneAe7ZDLXw=="
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.17.12"
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-proposal-class-static-block@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.18.0.tgz"
  integrity "sha1-fQIlMVbjw3k7258vqsOhwF8LpxA= sha512-t+8LsRMMDE74c6sV7KShIw13sqbqd58tlqNrsWoWBTIMw7SVQ0cZ905wLNS/FBCy/3PyooRHLFFlfrUNyyz5lA=="
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.0"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-dynamic-import@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.16.7.tgz"
  integrity "sha1-wZyJfqpGsnY0oA/un7fYKRWHBLI= sha512-I8SW9Ho3/8DRSdmDdH3gORdyUuYnk1m4cMxUAdu5oy4n3OfN8flDEH+d60iG7dUfi0KkYwSvoalHzzdRzpWHTg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.17.12.tgz"
  integrity "sha1-sihkzNZi25YG7bIofqX9FwnwU3g= sha512-j7Ye5EWdwoXOpRmo5QmRyHPsDIe6+u70ZYZrd7uz+ebPYFKfRcLcNu3Ro0vOlJ5zuv8rU7xa+GttNiRzX56snQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.17.12.tgz"
  integrity "sha1-9GQpUXkkNyMyFtjBrzcLsPv/RmQ= sha512-rKJ+rKBoXwLnIn7n6o6fulViHMrOThz99ybH+hKHcOZbnN14VuMnH9fo2eHE69C8pO4uX1Q7t2HYYIDmv8VYkg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.17.12.tgz"
  integrity "sha1-xkobyysKbQ7S/2dP0SD5DuS4iiM= sha512-EqFo2s1Z5yy+JeJu7SFfbIUtToJTVlC61/C7WLKDntSw4Sz6JNAIfL7zQ74VvirxpjB5kz/kIx0gCcb+5OEo2Q=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.17.12.tgz"
  integrity "sha1-HpMHm7wsvHVvbbahklFXxKkrlL4= sha512-ws/g3FSGVzv+VH86+QvgtuJL/kR67xaEIF2x0iPqdDfYW6ra6JF3lKVBkWynRLcNtIC1oCTfDRVxmm2mKzy+ag=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.16.7.tgz"
  integrity "sha1-1rafSvY/s4tsolWEQqf7GRI266k= sha512-vQgPMknOIgiuVqbokToyXbkY/OmmjAzr/0lhSIbG/KmnzXPGwW/AdhdKpi+O4X/VkWiWjnkKOBiqJrTaC98VKw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.18.0.tgz"
  integrity "sha1-efI5DIkroqaOwRLrDYlc+9ERVeg= sha512-nbTv371eTrFabDfHLElkn9oyf9VG+VKK6WMzhY2o4eHKaG19BToD9947zzGMO6I/Irstx9d8CwX6njPNIAR/yw=="
  dependencies:
    "@babel/compat-data" "^7.17.10"
    "@babel/helper-compilation-targets" "^7.17.10"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.17.12"

"@babel/plugin-proposal-optional-catch-binding@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.16.7.tgz"
  integrity "sha1-xiOkMGdP/Eq3Mv0KCudyK2fLdM8= sha512-eMOH/L4OvWSZAE1VkHbr1vckLG1WUcHGJSLqqQwl2GaUqG6QjddvrOaTUMNYiv77H5IKPMZ9U9P7EaHwvAShfA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.17.12.tgz"
  integrity "sha1-+WlJ6brKzjqQZjI6XPkM+53mcXQ= sha512-7wigcOs/Z4YWlK7xxjkvaIw84vGhDv/P1dFGQap0nHkc8gFKY/r+hXc8Qzf5k1gY7CvGIcHqAnOagVKJJ1wVOQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.17.12.tgz"
  integrity "sha1-wso6gL63U5KJk42gBa1SWgOKgZw= sha512-SllXoxo19HmxhDWm3luPz+cPhtoTSKLJE9PXshsfrOzBqs60QP0r8OaJItrPhAj0d7mZMnNF0Y1UUggCDgMz1A=="
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.17.12"
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-proposal-private-property-in-object@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.17.12.tgz"
  integrity "sha1-sC77fxBtVEZn2RrpdAWp/YyTlS0= sha512-/6BtVi57CJfrtDNKfK5b66ydK2J5pXUKBKSPD2G1whamMuEnZWgoOIfO8Vf9F/DoD4izBLD/Au4NMQfruzzykg=="
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-create-class-features-plugin" "^7.17.12"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.17.12", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.17.12.tgz"
  integrity "sha1-Pb16Z71/lMgjizlNoRLYaq8yrU0= sha512-Wb9qLjXf3ZazqXA7IvI7ozqRIXIGPtSo+L5coFmEkhTQK18ao4UDDD0zdTGAarmbLj2urpRwrc6893cu5Bfh0A=="
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.17.12"
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0= sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA= sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY= sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM= sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  integrity "sha1-AolkqbqA28CUyRXEh618TnpmRlo= sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.17.12.tgz"
  integrity "sha1-WAlqkrEbLk5UskxqDMDl5gerzt0= sha512-n/loy2zkq9ZEM8tEOwON9wTQSTNDTDEz6NujPtJGLU7qObzT1N4c4YZZf8E6ATB2AjNQg/Ib2AIpO03EZaCehw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo= sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.12.13":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.17.12.tgz"
  integrity "sha1-g0A1tFBhmDpJH2AJb2Gi58VnSkc= sha512-spyY3E3AURfxh/RHtjx5j6hs8am5NbUBGfcZ2vB3uShSpZdQyXSf5rR5Mk76vbtlAZOelyVQ71Fg0x9SG4fsog=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity "sha1-ypHvRjA1MESLkGZSusLp/plB9pk= sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak= sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c= sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE= sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE= sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io= sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0= sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://registry.yarnpkg.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw= sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.17.12.tgz"
  integrity "sha1-3d14O0c7GxU370ZCPjlE/ySJjEU= sha512-PHln3CNi/49V+mza4xMwrg+WGYevSF1oaiXaC2EQfdp4HWlSjRsrDXWJiQBKpP7749u6vQ9mcry2uuFOv5CXvA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-async-to-generator@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.17.12.tgz"
  integrity "sha1-2+VRHmsB7uFJbJRONc3+P1gFCDI= sha512-J8dbrWIOO3orDzir57NRsjg4uxucvhby0L/KZuGsWDj0g7twWK3g7JhJhOrXtuXiw8MeiSdJ3E0OW9H8LYEzLQ=="
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-remap-async-to-generator" "^7.16.8"

"@babel/plugin-transform-block-scoped-functions@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.16.7.tgz"
  integrity "sha1-TQ1X2WMu9gYs3zVLtxcQLuBCpiA= sha512-JUuzlzmF40Z9cXyytcbZEZKckgrQzChbQJw/5PuEHYeqzCsvebDx0K0jWnIIVcmmDOAVctCgnYs0pMcrYj2zJg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-block-scoping@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.17.12.tgz"
  integrity "sha1-aPw8Szu339gJ2Xt+0ZpYQFKiclw= sha512-jw8XW/B1i7Lqwqj2CbrViPcZijSxfguBWZP2aN59NHgxUyO/OcO1mfdCxH13QhN5LbWhPkX+f+brKGhZTiqtZQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-classes@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.17.12.tgz"
  integrity "sha1-2oieiaTTg3XuskmFIY7eq5OvTyk= sha512-cvO7lc7pZat6BsvH6l/EGaI8zpl8paICaoGk+7x7guvtfak/TbIf66nYmJOH13EuG0H+Xx3M+9LQDtSvZFKXKw=="
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.7"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.17.9"
    "@babel/helper-optimise-call-expression" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-replace-supers" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.17.12.tgz"
  integrity "sha1-vKYWqDZ5aY8yWOiS7UIlRuUxOH8= sha512-a7XINeplB5cQUWMg1E/GI1tFz3LfK021IjV1rj1ypE+R7jHm+pIHmHl25VNkZxtx9uuYp7ThGk8fur1HHG7PgQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-destructuring@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.18.0.tgz"
  integrity "sha1-3E+SWH4pG02qeKogzC16Y6oR6Fg= sha512-Mo69klS79z6KEfrLg/1WkmVnB8javh75HX4pi2btjvlIoasuxilEyjtsQW6XPrubNd7AQy0MMaNIaQE4e7+PQw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-dotall-regex@^7.16.7", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz"
  integrity "sha1-ay1naG+rFftqf9S9iV1Zgs/IEkE= sha512-Lyttaao2SjZF6Pf4vk1dVKv8YypMpomAbygW+mU5cYP3S5cWTfCJjG8xV6CFdzGFlfWK81IjL9viiTvpb6G7gQ=="
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-duplicate-keys@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.17.12.tgz"
  integrity "sha1-oJqnCaMxABP45I4OI7x6zg8hR3w= sha512-EA5eYFUG6xeerdabina/xIoB95jJ17mAkR8ivx6ZSu9frKShBjpOGZPn511MTDTkiCO+zXnzNczvUM69YSf3Zw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-exponentiation-operator@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.7.tgz"
  integrity "sha1-76mGLvl+np5fZT9t3HtmXoU2/ps= sha512-8UYLSlyLgRixQvlYH3J2ekXFHDFLQutdy7FfFAMm3CPZ6q9wHCwnUyiXpQCe3gVVnQlHc5nsuiEVziteRNTXEA=="
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-for-of@^7.17.12":
  version "7.18.1"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.1.tgz"
  integrity "sha1-7RS2V+Fityr7uytM2tJ3vyuzIDY= sha512-+TTB5XwvJ5hZbO8xvl2H4XaMDOAK57zF4miuC9qQJgysPNEAZZ9Z69rdF5LJkozGdZrjBIUAIyKUWRMmebI7vg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-function-name@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.7.tgz"
  integrity "sha1-WrNDdcZNYdCD19LwXDjZC5fsZc8= sha512-SU/C68YVwTRxqWj5kgsbKINakGag0KTgq9f2iZEXdStoAbOzLHEBRYzImmA6yFo8YZhJVflvXmIHUO7GWHmxxA=="
  dependencies:
    "@babel/helper-compilation-targets" "^7.16.7"
    "@babel/helper-function-name" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-literals@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.17.12.tgz"
  integrity "sha1-lxMfvGu7JhSHEFtLPtv56/nIMK4= sha512-8iRkvaTjJciWycPIZ9k9duu663FT7VrBdNqNgxnVXEFwOIp55JWcZd23VBRySYbnS3PwQ3rGiabJBBBGj5APmQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-member-expression-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.7.tgz"
  integrity "sha1-bl3PkG74oJjmMBSdFMhn3Sj5I4Q= sha512-mBruRMbktKQwbxaJof32LT9KLy2f3gH+27a5XSuXo6h7R3vqltl0PgZ80C8ZMKw98Bf8bqt6BEVi3svOh2PzMw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-modules-amd@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.18.0.tgz"
  integrity "sha1-fvEALmfjbaMVXtyL8ayTmAZMAu0= sha512-h8FjOlYmdZwl7Xm2Ug4iX2j7Qy63NANI+NQVWQzv6r25fqgg7k2dZl03p95kvqNclglHs4FZ+isv4p1uXMA+QA=="
  dependencies:
    "@babel/helper-module-transforms" "^7.18.0"
    "@babel/helper-plugin-utils" "^7.17.12"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.18.0.tgz"
  integrity "sha1-O+V14Z+9Jz1CrbyEVmsfrTWCs9s= sha512-cCeR0VZWtfxWS4YueAK2qtHtBPJRSaJcMlbS8jhSIm/A3E2Kpro4W1Dn4cqJtp59dtWfXjQwK7SPKF8ghs7rlw=="
  dependencies:
    "@babel/helper-module-transforms" "^7.18.0"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-simple-access" "^7.17.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.18.0.tgz"
  integrity "sha1-UOzbQ96XyEg4JEAvcSXtuUzdsJo= sha512-vwKpxdHnlM5tIrRt/eA0bzfbi7gUBLN08vLu38np1nZevlPySRe6yvuATJB5F/WPJ+ur4OXwpVYq9+BsxqAQuQ=="
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-module-transforms" "^7.18.0"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-validator-identifier" "^7.16.7"
    babel-plugin-dynamic-import-node "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.18.0.tgz"
  integrity "sha1-VqrGSiwqGSI0ESmkWX0f1cP/Ag8= sha512-d/zZ8I3BWli1tmROLxXLc9A6YXvGK8egMxHp+E/rRwMh1Kip0AP77VwZae3snEJ33iiWwvNv2+UIIhfalqhzZA=="
  dependencies:
    "@babel/helper-module-transforms" "^7.18.0"
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-named-capturing-groups-regex@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.17.12.tgz"
  integrity "sha1-nEpaWWbgQ01RXyZ1wif9jMhgaTE= sha512-vWoWFM5CKaTeHrdUJ/3SIOTRV+MBVGybOC9mhJkaprGNt5demMymDW24yC74avb915/mIRe3TgNb/d8idvnCRA=="
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.17.12"
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-new-target@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.17.12.tgz"
  integrity "sha1-EIQs1gWmIJROgepgYOnmXCZXQuM= sha512-CaOtzk2fDYisbjAD4Sd1MTKGVIpRtx9bWLyj24Y/k6p4s4gQ3CqDGJauFJxt8M/LEx003d0i3klVqnN73qvK3w=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-object-super@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.7.tgz"
  integrity "sha1-rDWc+NMs9DVNJ6RoZ5mUkLbDKpQ= sha512-14J1feiQVWaGvRxj2WjyMuXS2jsBkgB3MdSN5HuC2G5nRspa5RK9COcs82Pwy5BuGcjb+fYaUj94mYcOj7rCvw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"
    "@babel/helper-replace-supers" "^7.16.7"

"@babel/plugin-transform-parameters@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.17.12.tgz"
  integrity "sha1-60Z82Vhv9f8RWpiA1v29SoRrd2Y= sha512-6qW4rWo1cyCdq1FkYri7AHpauchbGLXpdwnYsfxFb+KtddHENfsY5JZb35xUwkK5opOLcJ3BNd2l7PhRYGlwIA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-property-literals@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.7.tgz"
  integrity "sha1-La2shRVUNvIsaWxIJ3MOD+EFelU= sha512-z4FGr9NMGdoIl1RqavCqGG+ZuYjfZ/hkCIeuH6Do7tXmSm0ls11nYVSJqFEUOSJbDab5wC6lRE/w6YjVcr6Hqw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-regenerator@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.0.tgz"
  integrity "sha1-RCdNZV6z8a8/OldLqBnT9IyvmdU= sha512-C8YdRw9uzx25HSIzwA7EM7YP0FhCe5wNvJbZzjVNHHPGVcDJ3Aie+qGYYdS1oVQgn+B3eAIJbWFLrJ4Jipv7nw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    regenerator-transform "^0.15.0"

"@babel/plugin-transform-reserved-words@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.17.12.tgz"
  integrity "sha1-fb00nzzf+6dR6BfPQMoThnMvZS8= sha512-1KYqwbJV3Co03NIi14uEHW8P50Md6KqFgt0FfpHdK6oyAHQVTosgPuPSiWud1HX0oYJ1hGRRlk0fP87jFpqXZA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-shorthand-properties@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.7.tgz"
  integrity "sha1-6FSa5K/Pg4L3EXlMDHtrk0xfvSo= sha512-hah2+FEnoRoATdIb05IOXf+4GzXYTq75TVhIn1PewihbpyrNWUt2JbudKQOETWw6QpLe+AIUpJ5MVLYTQbeeUg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-spread@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.17.12.tgz"
  integrity "sha1-wRLK0wZCmfA+oyr+0dZZIjk10fU= sha512-9pgmuQAtFi3lpNUstvG9nGfk9DkrdmWNp9KeKPFmuZCpEnxRzYlS8JgwPjYj+1AWDOSvoGN0H30p1cBOmT/Svg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.7.tgz"
  integrity "sha1-yEdB1PSjgHK5oeLj/VbTWVUuhmA= sha512-NJa0Bd/87QV5NZZzTuZG5BPJjLYadeSZ9fO6oOUoL4iQx+9EEuw/eEM92SrsT19Yc2jgB1u1hsjqDtH02c3Drw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-template-literals@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.17.12.tgz"
  integrity "sha1-SuwKGPOd2GxELh0Hd0bfAD42LG4= sha512-kAKJ7DX1dSRa2s7WN1xUAuaQmkTpN+uig4wCKWivVXIObqGbVTUlSavHyfI2iZvz89GFAMGm9p2DBJ4Y1Tp0hw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-typeof-symbol@^7.17.12":
  version "7.17.12"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.17.12.tgz"
  integrity "sha1-DxL1esNemLNbTtNIKZSNQr0OaIk= sha512-Q8y+Jp7ZdtSPXCThB6zjQ74N3lj0f6TDh1Hnf5B+sYlzQ8i5Pjp8gW0My79iekSpT4WnI06blqP6DT0OmaXXmw=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.17.12"

"@babel/plugin-transform-unicode-escapes@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.7.tgz"
  integrity "sha1-2ocX3nsyh6LG1ll1DJZPMCsx7OM= sha512-TAV5IGahIz3yZ9/Hfv35TV2xEm+kaBDaZQCn2S/hG9/CZ0DktxJv9eKfPc7yYCvOYR4JGx1h8C+jcSOvgaaI/Q=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/plugin-transform-unicode-regex@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.7.tgz"
  integrity "sha1-D3qkpQEZiXbiXoJwJXTDTP6+nvI= sha512-oC5tYYKw56HO75KZVLQ+R/Nl3Hro9kf8iG0hXoaHP7tjAyCpvqBiSNe6vGrZni1Z6MggmUOC6A7VP7AVmw225Q=="
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.7"
    "@babel/helper-plugin-utils" "^7.16.7"

"@babel/preset-env@^7.11.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/preset-env/-/preset-env-7.18.0.tgz"
  integrity "sha1-7H5R9MbgJoFgALIw7Xz3ShUw2R0= sha512-cP74OMs7ECLPeG1reiCQ/D/ypyOxgfm8uR6HRYV23vTJ7Lu1nbgj9DQDo/vH59gnn7GOAwtTDPPYV4aXzsMKHA=="
  dependencies:
    "@babel/compat-data" "^7.17.10"
    "@babel/helper-compilation-targets" "^7.17.10"
    "@babel/helper-plugin-utils" "^7.17.12"
    "@babel/helper-validator-option" "^7.16.7"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.17.12"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.17.12"
    "@babel/plugin-proposal-async-generator-functions" "^7.17.12"
    "@babel/plugin-proposal-class-properties" "^7.17.12"
    "@babel/plugin-proposal-class-static-block" "^7.18.0"
    "@babel/plugin-proposal-dynamic-import" "^7.16.7"
    "@babel/plugin-proposal-export-namespace-from" "^7.17.12"
    "@babel/plugin-proposal-json-strings" "^7.17.12"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.17.12"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.17.12"
    "@babel/plugin-proposal-numeric-separator" "^7.16.7"
    "@babel/plugin-proposal-object-rest-spread" "^7.18.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.7"
    "@babel/plugin-proposal-optional-chaining" "^7.17.12"
    "@babel/plugin-proposal-private-methods" "^7.17.12"
    "@babel/plugin-proposal-private-property-in-object" "^7.17.12"
    "@babel/plugin-proposal-unicode-property-regex" "^7.17.12"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.17.12"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.17.12"
    "@babel/plugin-transform-async-to-generator" "^7.17.12"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.7"
    "@babel/plugin-transform-block-scoping" "^7.17.12"
    "@babel/plugin-transform-classes" "^7.17.12"
    "@babel/plugin-transform-computed-properties" "^7.17.12"
    "@babel/plugin-transform-destructuring" "^7.18.0"
    "@babel/plugin-transform-dotall-regex" "^7.16.7"
    "@babel/plugin-transform-duplicate-keys" "^7.17.12"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.7"
    "@babel/plugin-transform-for-of" "^7.17.12"
    "@babel/plugin-transform-function-name" "^7.16.7"
    "@babel/plugin-transform-literals" "^7.17.12"
    "@babel/plugin-transform-member-expression-literals" "^7.16.7"
    "@babel/plugin-transform-modules-amd" "^7.18.0"
    "@babel/plugin-transform-modules-commonjs" "^7.18.0"
    "@babel/plugin-transform-modules-systemjs" "^7.18.0"
    "@babel/plugin-transform-modules-umd" "^7.18.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.17.12"
    "@babel/plugin-transform-new-target" "^7.17.12"
    "@babel/plugin-transform-object-super" "^7.16.7"
    "@babel/plugin-transform-parameters" "^7.17.12"
    "@babel/plugin-transform-property-literals" "^7.16.7"
    "@babel/plugin-transform-regenerator" "^7.18.0"
    "@babel/plugin-transform-reserved-words" "^7.17.12"
    "@babel/plugin-transform-shorthand-properties" "^7.16.7"
    "@babel/plugin-transform-spread" "^7.17.12"
    "@babel/plugin-transform-sticky-regex" "^7.16.7"
    "@babel/plugin-transform-template-literals" "^7.17.12"
    "@babel/plugin-transform-typeof-symbol" "^7.17.12"
    "@babel/plugin-transform-unicode-escapes" "^7.16.7"
    "@babel/plugin-transform-unicode-regex" "^7.16.7"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.18.0"
    babel-plugin-polyfill-corejs2 "^0.3.0"
    babel-plugin-polyfill-corejs3 "^0.5.0"
    babel-plugin-polyfill-regenerator "^0.3.0"
    core-js-compat "^3.22.1"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  resolved "https://registry.yarnpkg.com/@babel/preset-modules/-/preset-modules-0.1.5.tgz"
  integrity "sha1-75Odbn8miCfhhBY43G/5VRXhFdk= sha512-A57th6YRG7oR3cq/yt/Y84MvGgE0eJG2F1JLhKuyG+jFxEgrd/HAMJatiFtmOiZurz+0DkrvbheCLaV5f2JfjA=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.13.10", "@babel/runtime@^7.13.17", "@babel/runtime@^7.14.5", "@babel/runtime@^7.15.4", "@babel/runtime@^7.16.4", "@babel/runtime@^7.5.5", "@babel/runtime@^7.6.2", "@babel/runtime@^7.7.2", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/runtime/-/runtime-7.18.0.tgz"
  integrity "sha1-bXcUKhnLYIjwr2Yq8a2jemBNNK4= sha512-YMQvx/6nKEaucl0MY56mwIG483xk8SDNdlUwb2Ts6FUpr7fm85DxEmsY18LXBNhcTz6tO6JwZV8w1W06v8UKeg=="
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.16.7":
  version "7.16.7"
  resolved "https://registry.yarnpkg.com/@babel/template/-/template-7.16.7.tgz"
  integrity "sha1-jRJshwH95NZrJks+uj2W8HZm0VU= sha512-I8j/x8kHUrbYRTUxXrrMbfCa7jxkE7tZre39x3kjr9hvI82cK1FfqLygotcWN5kdPGWcLdWMHpSBavse5tWw3w=="
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/parser" "^7.16.7"
    "@babel/types" "^7.16.7"

"@babel/traverse@^7.13.0", "@babel/traverse@^7.16.7", "@babel/traverse@^7.16.8", "@babel/traverse@^7.18.0":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/traverse/-/traverse-7.18.0.tgz"
  integrity "sha1-Dl7G2wmGYLI3LdY9CWv0hOMtJ7o= sha512-oNOO4vaoIQoGjDQ84LgtF/IAlxlyqL4TUuoQ7xLkQETFaHkY1F7yazhB4Kt3VcZGL0ZF/jhrEpnXqUb0M7V3sw=="
  dependencies:
    "@babel/code-frame" "^7.16.7"
    "@babel/generator" "^7.18.0"
    "@babel/helper-environment-visitor" "^7.16.7"
    "@babel/helper-function-name" "^7.17.9"
    "@babel/helper-hoist-variables" "^7.16.7"
    "@babel/helper-split-export-declaration" "^7.16.7"
    "@babel/parser" "^7.18.0"
    "@babel/types" "^7.18.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.16.0", "@babel/types@^7.16.7", "@babel/types@^7.16.8", "@babel/types@^7.17.0", "@babel/types@^7.18.0", "@babel/types@^7.4.4":
  version "7.18.0"
  resolved "https://registry.yarnpkg.com/@babel/types/-/types-7.18.0.tgz"
  integrity "sha1-71I+o0lyKEnLS/gG6TQu3k0HFVM= sha512-vhAmLPAiC8j9K2GnsnLPCIH5wCrPpYIVBCWRBFDCB7Y/BXLqi/O+1RSTTM2bsmg6U/551+FCf9PNPxjABmxHTw=="
  dependencies:
    "@babel/helper-validator-identifier" "^7.16.7"
    to-fast-properties "^2.0.0"

"@capacitor-community/privacy-screen@3.2.0":
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/@capacitor-community/privacy-screen/-/privacy-screen-3.2.0.tgz#ad6acfd11c121e21d8d3054a2576527599bc3aa0"
  integrity sha512-27ttigmFRldWcFLq5oPcYFAxbPD6C4v3WoRv/KAaXktWPn1LjFnIvcYpp9QVOnYW6VQJEBipll735XAFZLp3iQ==

"@capacitor-community/screen-brightness@^5.0.0":
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/@capacitor-community/screen-brightness/-/screen-brightness-5.0.0.tgz#18b51997610fd81e7ad45bbd109356534dca4e11"
  integrity sha512-EaAV7mk+Noxi0Natcx8C7C7ecW9IFAsMotYHjR/Ve8R4kA0ksvvjuWoAc0YtWT0DvpXP8qViWN2xU3Q8Uz6v4w==

"@capacitor/android@^4.3.0":
  version "4.4.0"
  resolved "https://registry.npmjs.org/@capacitor/android/-/android-4.4.0.tgz"
  integrity sha512-vPoUcoJ1CZ5pxjceN/HVnq5FuNo+V1/kBj/NH48nAiMzBbZPEDmplguGC8Dj2qbKjhkJ+LMSL26VTfnsyHMJbA==

"@capacitor/app@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@capacitor/app/-/app-4.1.0.tgz#377f5c334f69b2a2c027ca9bc8f343d97e372142"
  integrity sha512-QPZh+fnndlL1fT8jUmx8R7KPaWZDNJf/Aj1BiU867AxHnQNnM0a6wwDdQKGhA7OOniudekHEdrJAzHnPnKtLNg==

"@capacitor/browser@^4.0.1":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@capacitor/browser/-/browser-4.0.1.tgz"
  integrity sha512-tPOA/eYDJdDeMFd+A3I8teuGfPP0GEqhWtMzH8z5r2tW40iJ/mL8qH5NgVLMZ3uvs7gN2LfQTCMNJvl0F/WdBw==

"@capacitor/cli@^4.3.0":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@capacitor/cli/-/cli-4.3.0.tgz"
  integrity sha512-+IBIJvxpHWSrtfb6XxPSh5m5h5ijLBqh0aGPxvj0nm7mXUwkT/fuQbeEUVCwIoImmYZCuUAFIkQFB0nWd74bEg==
  dependencies:
    "@ionic/cli-framework-output" "^2.2.5"
    "@ionic/utils-fs" "^3.1.6"
    "@ionic/utils-subprocess" "^2.1.11"
    "@ionic/utils-terminal" "^2.3.3"
    commander "^9.3.0"
    debug "^4.3.4"
    env-paths "^2.2.0"
    kleur "^4.1.4"
    native-run "^1.6.0"
    open "^8.4.0"
    plist "^3.0.5"
    prompts "^2.4.2"
    rimraf "^3.0.2"
    semver "^7.3.7"
    tar "^6.1.11"
    tslib "^2.4.0"
    xml2js "^0.4.23"

"@capacitor/core@^4.3.0":
  version "4.4.0"
  resolved "https://registry.npmjs.org/@capacitor/core/-/core-4.4.0.tgz"
  integrity sha512-9k6EJTCMlHhr6g2oGo2FJM2byRyX5GEfB24Bv2e4fnemiiHrQEeAKdg++6Hj7jNNZtI1q+5EImTrcpXzPkiX7Q==
  dependencies:
    tslib "^2.1.0"

"@capacitor/device@^4.0.1":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@capacitor/device/-/device-4.0.1.tgz"
  integrity sha512-D0jJFQYifmsgcz4X9MEEKH5E36ARK2CJcUCXJbcuFTChHSxK+ly7Kd6PZC73Y5GkEZIjpebWAWo5F3w9S4hsYQ==

"@capacitor/geolocation@^4.1.0":
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/@capacitor/geolocation/-/geolocation-4.1.0.tgz#1941b5ca4288d7cb5c80940649d4416322b37ee2"
  integrity sha512-hfI4MUcu1zcJPTvm0g6V3telTGwq9sCU8EnY4hFJpLedbIQeWPthCOSbFtNHAU5mVaAP1Zls3x6TsXL8TX08EA==

"@capacitor/ios@^4.3.0":
  version "4.4.0"
  resolved "https://registry.npmjs.org/@capacitor/ios/-/ios-4.4.0.tgz"
  integrity sha512-x1i9WgaeUANBgXmCHcXoMrqZC49Ef0eu+kaRmcQ0/UkI2r31/PMGr3BVTlHRJLeBsodzwR4TiEeKRK044WX+Hw==

"@capacitor/preferences@4.0.1":
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/preferences/-/preferences-4.0.1.tgz#fd56f24fe2cde1222cf5c3057d52cd55fd033e7b"
  integrity sha512-FffJjKS4XcodUj/rtFtWizQ9q/oYDSn5opZ+JYwj/EkjiMWMhY/Pk9lN3vwQfp+VJrTt3hjr+bwKXjhTbYBqKw==

"@capacitor/push-notifications@^5.1":
  version "5.1.2"
  resolved "https://registry.yarnpkg.com/@capacitor/push-notifications/-/push-notifications-5.1.2.tgz#7f50e3bd2f215ea161dce8ff1824f815f8d45cdc"
  integrity sha512-l5kz/8B2oszi1ciJzOKfkhb6jSlYTs8OpjozV/ey+xcUVnYDTBWpDBUi66ZfPDFoZC/G7srIazR7Le+JJAoNUQ==

"@capacitor/toast@^4.0.1":
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/@capacitor/toast/-/toast-4.0.1.tgz#4d65e74c39a946bbab5c8a1e129fd136d9e3191f"
  integrity sha512-caBbhbDPg11R4F/Xb/HKpmIDjXxQbKocVTYLN577AymmyQBUJQgiADTYYnG+Viom41mG9crirZ9WKjARG9lR0w==

"@emotion/babel-plugin@^11.7.1":
  version "11.9.2"
  resolved "https://registry.yarnpkg.com/@emotion/babel-plugin/-/babel-plugin-11.9.2.tgz"
  integrity "sha1-cjttOUyJ+y73giKdkrqVp0BXbpU= sha512-Pr/7HGH6H6yKgnVFNEj2MVlreu3ADqftqjqwUvDy/OJzKFgxKeTQ+eeUf20FOTuHVkDON2iNa25rAXVYtWJCjw=="
  dependencies:
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/plugin-syntax-jsx" "^7.12.13"
    "@babel/runtime" "^7.13.10"
    "@emotion/hash" "^0.8.0"
    "@emotion/memoize" "^0.7.5"
    "@emotion/serialize" "^1.0.2"
    babel-plugin-macros "^2.6.1"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.0.13"

"@emotion/cache@^11.4.0", "@emotion/cache@^11.7.1":
  version "11.7.1"
  resolved "https://registry.yarnpkg.com/@emotion/cache/-/cache-11.7.1.tgz"
  integrity "sha1-CNCA45akLgA3hIIU6Kp7+HkGVTk= sha512-r65Zy4Iljb8oyjtLeCuBH8Qjiy107dOYC6SJq7g7GV5UCQWMObY4SJDPGFjiiVpPrOJ2hmJOoBiYTC7hwx9E2A=="
  dependencies:
    "@emotion/memoize" "^0.7.4"
    "@emotion/sheet" "^1.1.0"
    "@emotion/utils" "^1.0.0"
    "@emotion/weak-memoize" "^0.2.5"
    stylis "4.0.13"

"@emotion/hash@^0.8.0":
  version "0.8.0"
  resolved "https://registry.yarnpkg.com/@emotion/hash/-/hash-0.8.0.tgz"
  integrity "sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM= sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow=="

"@emotion/is-prop-valid@^0.8.2":
  version "0.8.8"
  resolved "https://registry.yarnpkg.com/@emotion/is-prop-valid/-/is-prop-valid-0.8.8.tgz"
  integrity "sha1-2yixxDaKJZtgqXMR1qlS1P0BrBo= sha512-u5WtneEAr5IDG2Wv65yhunPSMLIpuKsbuOktRojfrEiEvRyC85LgPMZI63cr7NUqT8ZIGdSVg8ZKGxIug4lXcA=="
  dependencies:
    "@emotion/memoize" "0.7.4"

"@emotion/memoize@0.7.4":
  version "0.7.4"
  resolved "https://registry.yarnpkg.com/@emotion/memoize/-/memoize-0.7.4.tgz"
  integrity "sha1-Gb8PWvGRSREcQNmLsM+CEZ9dnus= sha512-Ja/Vfqe3HpuzRsG1oBtWTHk2PGZ7GR+2Vz5iYGelAw8dx32K0y7PjVuxK6z1nMpZOqAFsRUPCkK1YjJ56qJlgw=="

"@emotion/memoize@^0.7.4", "@emotion/memoize@^0.7.5":
  version "0.7.5"
  resolved "https://registry.yarnpkg.com/@emotion/memoize/-/memoize-0.7.5.tgz"
  integrity "sha1-LED4FEmk5VTp/GOWkQ7UhD7CvlA= sha512-igX9a37DR2ZPGYtV6suZ6whr8pTFtyHL3K/oLUotxpSVO2ASaprmAe2Dkq7tBo7CRY7MMDrAa9nuQP9/YG8FxQ=="

"@emotion/react@^11.1.1", "@emotion/react@^11.8.1":
  version "11.9.0"
  resolved "https://registry.yarnpkg.com/@emotion/react/-/react-11.9.0.tgz"
  integrity "sha1-ttQrHbO9dRHnp8QVHci8guFFk7g= sha512-lBVSF5d0ceKtfKCDQJveNAtkC7ayxpVlgOohLgXqRwqWr9bOf4TZAFFyIcNngnV6xK6X4x2ZeXq7vliHkoVkxQ=="
  dependencies:
    "@babel/runtime" "^7.13.10"
    "@emotion/babel-plugin" "^11.7.1"
    "@emotion/cache" "^11.7.1"
    "@emotion/serialize" "^1.0.3"
    "@emotion/utils" "^1.1.0"
    "@emotion/weak-memoize" "^0.2.5"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.0.2", "@emotion/serialize@^1.0.3":
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/@emotion/serialize/-/serialize-1.0.3.tgz"
  integrity "sha1-meIGDCbGKSRp+zDbQfRpDhyP6mM= sha512-2mSSvgLfyV3q+iVh3YWgNlUc2a9ZlDU7DjuP5MjK3AXRR0dYigCrP99aeFtaB2L/hjfEZdSThn5dsZ0ufqbvsA=="
  dependencies:
    "@emotion/hash" "^0.8.0"
    "@emotion/memoize" "^0.7.4"
    "@emotion/unitless" "^0.7.5"
    "@emotion/utils" "^1.0.0"
    csstype "^3.0.2"

"@emotion/sheet@^1.1.0":
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/@emotion/sheet/-/sheet-1.1.0.tgz"
  integrity "sha1-VtmcQfChzaJyagWqaiCv1MY+WNI= sha512-u0AX4aSo25sMAygCuQTzS+HsImZFuS8llY8O7b9MDRzbJM0kVJlAz6KNDqcG7pOuQZJmj/8X/rAW+66kMnMW+g=="

"@emotion/unitless@^0.7.5":
  version "0.7.5"
  resolved "https://registry.yarnpkg.com/@emotion/unitless/-/unitless-0.7.5.tgz"
  integrity "sha1-dyESkcGQCnALinjPr9oxYNdpSe0= sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg=="

"@emotion/utils@^1.0.0", "@emotion/utils@^1.1.0":
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/@emotion/utils/-/utils-1.1.0.tgz"
  integrity "sha1-hrCyl/PxoPK9sI7qyaL0mv1A0M8= sha512-iRLa/Y4Rs5H/f2nimczYmS5kFJEbpiVvgN3XVfZ022IYhuNA1IRSHEizcof88LtCTXtl9S2Cxt32KgaXEu72JQ=="

"@emotion/weak-memoize@^0.2.5":
  version "0.2.5"
  resolved "https://registry.yarnpkg.com/@emotion/weak-memoize/-/weak-memoize-0.2.5.tgz"
  integrity "sha1-ju2YLi7m9/TkTCU+EpYpgHke/UY= sha512-6U71C2Wp7r5XtFtQzYrW5iKFT67OixrSxjI4MptCHzdSVlgabczzqLe0ZSgnub/5Kp4hSbpDB1tMytZY9pwxxA=="

"@graphql-typed-document-node/core@^3.1.1":
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/@graphql-typed-document-node/core/-/core-3.1.1.tgz#076d78ce99822258cf813ecc1e7fa460fa74d052"
  integrity sha512-NQ17ii0rK1b34VZonlmT2QMJFI70m0TRwbknO/ihlbatXyaktDhN/98vBiUU6kNBPljqGqyIrl2T4nY2RpFANg==

"@headlessui/react@^1.4.1":
  version "1.6.2"
  resolved "https://registry.yarnpkg.com/@headlessui/react/-/react-1.6.2.tgz"
  integrity "sha1-C6d5pkjoItdHisWLm8sRKginVls= sha512-x+kbTjJ+ixgsignqUU9MluoZYBJAlIYMeya4w7dk/Mo13vIdI/mSnzJAVgHhHKzE/hX//+rXylr4jUVThuQq/A=="

"@hookform/resolvers@^2.6.0":
  version "2.8.10"
  resolved "https://registry.yarnpkg.com/@hookform/resolvers/-/resolvers-2.8.10.tgz"
  integrity "sha1-tm16eEixsd1bl2pz//Nrs2Zmbn0= sha512-DDFtNlugsbwAhCJHYp3NcN5LvJrwSsCLPi41Wo5O8UAIbUFnBfY/jW+zKnlX57BZ4jE0j/g6R9rB3JlO89ad0g=="

"@ionic/cli-framework-output@^2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@ionic/cli-framework-output/-/cli-framework-output-2.2.5.tgz"
  integrity sha512-YeDLTnTaE6V4IDUxT8GDIep0GuRIFaR7YZDLANMuuWJZDmnTku6DP+MmQoltBeLmVvz1BAAZgk41xzxdq6H2FQ==
  dependencies:
    "@ionic/utils-terminal" "2.3.3"
    debug "^4.0.0"
    tslib "^2.0.1"

"@ionic/utils-array@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@ionic/utils-array/-/utils-array-2.1.5.tgz"
  integrity sha512-HD72a71IQVBmQckDwmA8RxNVMTbxnaLbgFOl+dO5tbvW9CkkSFCv41h6fUuNsSEVgngfkn0i98HDuZC8mk+lTA==
  dependencies:
    debug "^4.0.0"
    tslib "^2.0.1"

"@ionic/utils-fs@3.1.6", "@ionic/utils-fs@^3.1.6":
  version "3.1.6"
  resolved "https://registry.npmjs.org/@ionic/utils-fs/-/utils-fs-3.1.6.tgz"
  integrity sha512-eikrNkK89CfGPmexjTfSWl4EYqsPSBh0Ka7by4F0PLc1hJZYtJxUZV3X4r5ecA8ikjicUmcbU7zJmAjmqutG/w==
  dependencies:
    "@types/fs-extra" "^8.0.0"
    debug "^4.0.0"
    fs-extra "^9.0.0"
    tslib "^2.0.1"

"@ionic/utils-object@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@ionic/utils-object/-/utils-object-2.1.5.tgz"
  integrity sha512-XnYNSwfewUqxq+yjER1hxTKggftpNjFLJH0s37jcrNDwbzmbpFTQTVAp4ikNK4rd9DOebX/jbeZb8jfD86IYxw==
  dependencies:
    debug "^4.0.0"
    tslib "^2.0.1"

"@ionic/utils-process@2.1.10":
  version "2.1.10"
  resolved "https://registry.npmjs.org/@ionic/utils-process/-/utils-process-2.1.10.tgz"
  integrity sha512-mZ7JEowcuGQK+SKsJXi0liYTcXd2bNMR3nE0CyTROpMECUpJeAvvaBaPGZf5ERQUPeWBVuwqAqjUmIdxhz5bxw==
  dependencies:
    "@ionic/utils-object" "2.1.5"
    "@ionic/utils-terminal" "2.3.3"
    debug "^4.0.0"
    signal-exit "^3.0.3"
    tree-kill "^1.2.2"
    tslib "^2.0.1"

"@ionic/utils-stream@3.1.5":
  version "3.1.5"
  resolved "https://registry.npmjs.org/@ionic/utils-stream/-/utils-stream-3.1.5.tgz"
  integrity sha512-hkm46uHvEC05X/8PHgdJi4l4zv9VQDELZTM+Kz69odtO9zZYfnt8DkfXHJqJ+PxmtiE5mk/ehJWLnn/XAczTUw==
  dependencies:
    debug "^4.0.0"
    tslib "^2.0.1"

"@ionic/utils-subprocess@^2.1.11":
  version "2.1.11"
  resolved "https://registry.npmjs.org/@ionic/utils-subprocess/-/utils-subprocess-2.1.11.tgz"
  integrity sha512-6zCDixNmZCbMCy5np8klSxOZF85kuDyzZSTTQKQP90ZtYNCcPYmuFSzaqDwApJT4r5L3MY3JrqK1gLkc6xiUPw==
  dependencies:
    "@ionic/utils-array" "2.1.5"
    "@ionic/utils-fs" "3.1.6"
    "@ionic/utils-process" "2.1.10"
    "@ionic/utils-stream" "3.1.5"
    "@ionic/utils-terminal" "2.3.3"
    cross-spawn "^7.0.3"
    debug "^4.0.0"
    tslib "^2.0.1"

"@ionic/utils-terminal@2.3.3", "@ionic/utils-terminal@^2.3.3":
  version "2.3.3"
  resolved "https://registry.npmjs.org/@ionic/utils-terminal/-/utils-terminal-2.3.3.tgz"
  integrity sha512-RnuSfNZ5fLEyX3R5mtcMY97cGD1A0NVBbarsSQ6yMMfRJ5YHU7hHVyUfvZeClbqkBC/pAqI/rYJuXKCT9YeMCQ==
  dependencies:
    "@types/slice-ansi" "^4.0.0"
    debug "^4.0.0"
    signal-exit "^3.0.3"
    slice-ansi "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    tslib "^2.0.1"
    untildify "^4.0.0"
    wrap-ansi "^7.0.0"

"@jridgewell/gen-mapping@^0.1.0":
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz"
  integrity "sha1-5dLkUDBqlJHjvXfjI+ONev8xWZY= sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w=="
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/gen-mapping@^0.3.0":
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.1.tgz"
  integrity "sha1-z5Kpg8g0ZrjAzpEk+t6vCffGbqk= sha512-GcHwniMlA2z+WFPWuY8lp3fsza0I8xPFMWL5+n8LYyP6PSvPrXf4+n8stDHZY2DM0zy9sVkRDy1jDI4XGzYVqg=="
  dependencies:
    "@jridgewell/set-array" "^1.0.0"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.0.3":
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/@jridgewell/resolve-uri/-/resolve-uri-3.0.7.tgz"
  integrity "sha1-MM1JggqWKv9IyP/8XNdgFR/KYf4= sha512-8cXDaBBHOr2pQ7j77Y6Vp5VDT2sIqWyWQ56TjEq4ih/a4iST3dItRe8Q9fp0rrIl9DoKhWQtUQz/YpOxLkXbNA=="

"@jridgewell/set-array@^1.0.0":
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/@jridgewell/set-array/-/set-array-1.1.1.tgz"
  integrity "sha1-NqasyTmHrc8LpQxmkIvQtw3or+o= sha512-Ct5MqZkLGEXTVmQYbGtx9SVqD2fqwvdubdps5D3djjAkgkKwT918VNOz65pEHFaYTeWcukmJmH5SwsA9Tn2ObQ=="

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.13"
  resolved "https://registry.yarnpkg.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.13.tgz"
  integrity "sha1-tkYfsMKWQ1bEaeEV9QTJWtl6uIw= sha512-GryiOJmNcWbovBxTfZSF71V/mXbgcV3MewDe3kIMCLyIh5e7SKAeUZs+rMnJ8jkMolZ/4/VsdBmMrw3l+VdZ3w=="

"@jridgewell/trace-mapping@^0.3.9":
  version "0.3.13"
  resolved "https://registry.yarnpkg.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.13.tgz"
  integrity "sha1-3P4+lfIkyP6XqHpSNd7+yZmqkuo= sha512-o1xbKhp9qnIAoHJSWd6KlCZfqslL4valSF81H8ImioOAxluWYWOpWkpyktY2vnt4tbrX9XYaxovq6cgowaJp2w=="
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@next/env@12.3.1":
  version "12.3.1"
  resolved "https://registry.npmjs.org/@next/env/-/env-12.3.1.tgz"
  integrity sha512-9P9THmRFVKGKt9DYqeC2aKIxm8rlvkK38V1P1sRE7qyoPBIs8l9oo79QoSdPtOWfzkbDAVUqvbQGgTMsb8BtJg==

"@next/swc-android-arm-eabi@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-android-arm-eabi/-/swc-android-arm-eabi-12.3.1.tgz#b15ce8ad376102a3b8c0f3c017dde050a22bb1a3"
  integrity sha512-i+BvKA8tB//srVPPQxIQN5lvfROcfv4OB23/L1nXznP+N/TyKL8lql3l7oo2LNhnH66zWhfoemg3Q4VJZSruzQ==

"@next/swc-android-arm64@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-android-arm64/-/swc-android-arm64-12.3.1.tgz#85d205f568a790a137cb3c3f720d961a2436ac9c"
  integrity sha512-CmgU2ZNyBP0rkugOOqLnjl3+eRpXBzB/I2sjwcGZ7/Z6RcUJXK5Evz+N0ucOxqE4cZ3gkTeXtSzRrMK2mGYV8Q==

"@next/swc-darwin-arm64@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-darwin-arm64/-/swc-darwin-arm64-12.3.1.tgz#b105457d6760a7916b27e46c97cb1a40547114ae"
  integrity sha512-hT/EBGNcu0ITiuWDYU9ur57Oa4LybD5DOQp4f22T6zLfpoBMfBibPtR8XktXmOyFHrL/6FC2p9ojdLZhWhvBHg==

"@next/swc-darwin-x64@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-darwin-x64/-/swc-darwin-x64-12.3.1.tgz#6947b39082271378896b095b6696a7791c6e32b1"
  integrity sha512-9S6EVueCVCyGf2vuiLiGEHZCJcPAxglyckTZcEwLdJwozLqN0gtS0Eq0bQlGS3dH49Py/rQYpZ3KVWZ9BUf/WA==

"@next/swc-freebsd-x64@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-freebsd-x64/-/swc-freebsd-x64-12.3.1.tgz#2b6c36a4d84aae8b0ea0e0da9bafc696ae27085a"
  integrity sha512-qcuUQkaBZWqzM0F1N4AkAh88lLzzpfE6ImOcI1P6YeyJSsBmpBIV8o70zV+Wxpc26yV9vpzb+e5gCyxNjKJg5Q==

"@next/swc-linux-arm-gnueabihf@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-arm-gnueabihf/-/swc-linux-arm-gnueabihf-12.3.1.tgz#6e421c44285cfedac1f4631d5de330dd60b86298"
  integrity sha512-diL9MSYrEI5nY2wc/h/DBewEDUzr/DqBjIgHJ3RUNtETAOB3spMNHvJk2XKUDjnQuluLmFMloet9tpEqU2TT9w==

"@next/swc-linux-arm64-gnu@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-arm64-gnu/-/swc-linux-arm64-gnu-12.3.1.tgz#8863f08a81f422f910af126159d2cbb9552ef717"
  integrity sha512-o/xB2nztoaC7jnXU3Q36vGgOolJpsGG8ETNjxM1VAPxRwM7FyGCPHOMk1XavG88QZSQf+1r+POBW0tLxQOJ9DQ==

"@next/swc-linux-arm64-musl@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-arm64-musl/-/swc-linux-arm64-musl-12.3.1.tgz#0038f07cf0b259d70ae0c80890d826dfc775d9f3"
  integrity sha512-2WEasRxJzgAmP43glFNhADpe8zB7kJofhEAVNbDJZANp+H4+wq+/cW1CdDi8DqjkShPEA6/ejJw+xnEyDID2jg==

"@next/swc-linux-x64-gnu@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-x64-gnu/-/swc-linux-x64-gnu-12.3.1.tgz#c66468f5e8181ffb096c537f0dbfb589baa6a9c1"
  integrity sha512-JWEaMyvNrXuM3dyy9Pp5cFPuSSvG82+yABqsWugjWlvfmnlnx9HOQZY23bFq3cNghy5V/t0iPb6cffzRWylgsA==

"@next/swc-linux-x64-musl@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-linux-x64-musl/-/swc-linux-x64-musl-12.3.1.tgz#c6269f3e96ac0395bc722ad97ce410ea5101d305"
  integrity sha512-xoEWQQ71waWc4BZcOjmatuvPUXKTv6MbIFzpm4LFeCHsg2iwai0ILmNXf81rJR+L1Wb9ifEke2sQpZSPNz1Iyg==

"@next/swc-win32-arm64-msvc@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-win32-arm64-msvc/-/swc-win32-arm64-msvc-12.3.1.tgz#83c639ee969cee36ce247c3abd1d9df97b5ecade"
  integrity sha512-hswVFYQYIeGHE2JYaBVtvqmBQ1CppplQbZJS/JgrVI3x2CurNhEkmds/yqvDONfwfbttTtH4+q9Dzf/WVl3Opw==

"@next/swc-win32-ia32-msvc@12.3.1":
  version "12.3.1"
  resolved "https://registry.yarnpkg.com/@next/swc-win32-ia32-msvc/-/swc-win32-ia32-msvc-12.3.1.tgz#52995748b92aa8ad053440301bc2c0d9fbcf27c2"
  integrity sha512-Kny5JBehkTbKPmqulr5i+iKntO5YMP+bVM8Hf8UAmjSMVo3wehyLVc9IZkNmcbxi+vwETnQvJaT5ynYBkJ9dWA==

"@next/swc-win32-x64-msvc@12.3.1":
  version "12.3.1"
  resolved "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-12.3.1.tgz"
  integrity sha512-W1ijvzzg+kPEX6LAc+50EYYSEo0FVu7dmTE+t+DM4iOLqgGHoW9uYSz9wCVdkXOEEMP9xhXfGpcSxsfDucyPkA==

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.yarnpkg.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U= sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g=="
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos= sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A=="

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.yarnpkg.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po= sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg=="
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@popperjs/core@^2.9.2":
  version "2.11.6"
  resolved "https://registry.npmjs.org/@popperjs/core/-/core-2.11.6.tgz"
  integrity sha512-50/17A98tWUfQ176raKiOGXuYpLyyVMkxxG6oylzL3BPOlA6ADGdK7EYunSa4I064xerltq9TGXs8HmOk5E+vw==

"@react-pdf/font@^2.0.14":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@react-pdf/font/-/font-2.1.1.tgz"
  integrity "sha1-GL9N412acgguX6CN/q8IaRB8WtY= sha512-4XwdD4S9HoiuhUQZvCGTFz+baYqowUcq7+HknRlMEk0HQOL3Rnnqe7Sxlv8ap208bHUsdsQFMjxlW8D0BS6C0w=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/fontkit" "^2.1.0"
    "@react-pdf/types" "^2.0.8"
    cross-fetch "^3.1.5"
    is-url "^1.2.4"

"@react-pdf/fontkit@^2.1.0":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/fontkit/-/fontkit-2.1.0.tgz"
  integrity "sha1-mLC/4j9qrxTgR3GDZatwTrsUkac= sha512-/t7nOtH0XAgN7kPJtVfXQL78YmfUanhPc0HEIVgS0YdMTAtZJT0aIQ2HoBWyyYmlsDlsWyGkb78Hf4b1y5QaPQ=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/unicode-properties" "^2.5.0"
    brotli "^1.2.0"
    clone "^1.0.4"
    deep-equal "^1.0.0"
    dfa "^1.2.0"
    restructure "^0.5.3"
    tiny-inflate "^1.0.2"
    unicode-trie "^0.3.0"

"@react-pdf/image@^2.1.1":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@react-pdf/image/-/image-2.1.1.tgz"
  integrity "sha1-jbKt7Fbo1NTFkIZtyTa+03qamKQ= sha512-Uh9N1HBU5QGP1QxuIhpVES8FAQsSy2/IGrCHoCCzUUuvbUKf+Mezl3+gvaS4fkWbENPpZ9q6u2C3yL5IqRirsw=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/png-js" "^2.1.0"
    cross-fetch "^3.1.5"

"@react-pdf/layout@^2.0.21":
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/@react-pdf/layout/-/layout-2.1.1.tgz"
  integrity "sha1-7aq1bebi2bF3LnV3buB7A83eZdw= sha512-ddpXNRAU1JNHL+AUQEFO2RFolDrtqs/Z0Gb8fjicCcWinthpqkeLdNMWYA5bQuUIzuJXONAQDGd6JLomv2Wayg=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/image" "^2.1.1"
    "@react-pdf/pdfkit" "^2.1.0"
    "@react-pdf/primitives" "^2.0.2"
    "@react-pdf/stylesheet" "^2.1.0"
    "@react-pdf/textkit" "^2.1.0"
    "@react-pdf/types" "^2.0.8"
    "@react-pdf/yoga" "^2.0.4"
    cross-fetch "^3.1.5"
    emoji-regex "^8.0.0"
    queue "^6.0.1"
    ramda "^0.26.1"

"@react-pdf/pdfkit@^2.0.12", "@react-pdf/pdfkit@^2.1.0":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/pdfkit/-/pdfkit-2.1.0.tgz"
  integrity "sha1-3SdU33+rVdq2FyF6NZsu5C4wuQc= sha512-XS/mBgQadBEyz5b79L1rGOiPiX+9oZL8TlOV9PG9mGHS9VyrK7rieysay4azI/67a1nlRs2XyRETELPlqHYsHA=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/fontkit" "^2.1.0"
    "@react-pdf/png-js" "^2.1.0"
    crypto-js "^4.0.0"

"@react-pdf/png-js@^2.1.0":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/png-js/-/png-js-2.1.0.tgz"
  integrity "sha1-GmuIWPt61sPheuYoB4korI+uqVE= sha512-S5T5qGOlDK6VUJBVGkltNcPFEOWJW5FAD5IWkp9ATYPehC7L1d0CwuFlkFDaHh9ySmm46fKRHfn4YNQguq9gmw=="

"@react-pdf/primitives@^2.0.1", "@react-pdf/primitives@^2.0.2":
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/@react-pdf/primitives/-/primitives-2.0.2.tgz"
  integrity "sha1-fPSnEfKh/6UejYEz11cax1Y0b8Q= sha512-NkbOje/Sd/ziqfp9eYFc0ACeytmZB9MIrhx0j1rDT3gIhrjo19sS7R6Iap50gNgSphgx4Nh7GxWu/usBiuTmnw=="

"@react-pdf/render@^2.0.15":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/render/-/render-2.1.0.tgz"
  integrity "sha1-Ynj0KL0vYc/J387kdF62mbuXZr4= sha512-OacYR/eY47OzuuBXr0TaIqdP2m8GWIMWVkzTVaSxB6ZEs0wFCv6Hw5LYmh1k617o1YbZkRRDJIyGUtTmZuw6Ng=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/primitives" "^2.0.2"
    "@react-pdf/textkit" "^2.1.0"
    "@react-pdf/types" "^2.0.8"
    abs-svg-path "^0.1.1"
    color-string "^1.5.3"
    normalize-svg-path "^1.1.0"
    parse-svg-path "^0.1.2"
    ramda "^0.26.1"
    svg-arc-to-cubic-bezier "^3.2.0"

"@react-pdf/renderer@2.0.21":
  version "2.0.21"
  resolved "https://registry.yarnpkg.com/@react-pdf/renderer/-/renderer-2.0.21.tgz"
  integrity "sha1-xuSHZjVYNCcrXqAzv4XmdmfZAvE= sha512-HDVQ49Dc3zEM5SNXejqM831eGvm2yMfpW0x8R1iDPpLScTuioZeMMT9NO33TofZC4jNbRP5w1Vz8ZZcB20mafw=="
  dependencies:
    "@babel/runtime" "^7.6.2"
    "@react-pdf/font" "^2.0.14"
    "@react-pdf/layout" "^2.0.21"
    "@react-pdf/pdfkit" "^2.0.12"
    "@react-pdf/primitives" "^2.0.1"
    "@react-pdf/render" "^2.0.15"
    "@react-pdf/types" "^2.0.7"
    blob-stream "^0.1.3"
    queue "^6.0.1"
    ramda "^0.26.1"
    react-reconciler "^0.23.0"
    scheduler "^0.15.0"

"@react-pdf/stylesheet@^2.1.0":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/stylesheet/-/stylesheet-2.1.0.tgz"
  integrity "sha1-gof1219WNra3bX/6s9NfkTrsJus= sha512-F9v++z1QhlnCONkwdUJm1C/FNM9WkTiTRp+OCe6za2iZvrs9sGhSnTHFMOb5HOZYHsAXx+pLgHqE4WP+60SunQ=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/types" "^2.0.8"
    color-string "^1.5.3"
    hsl-to-hex "^1.0.0"
    media-engine "^1.0.3"
    postcss-value-parser "^4.1.0"
    ramda "^0.26.1"

"@react-pdf/textkit@^2.1.0":
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/textkit/-/textkit-2.1.0.tgz"
  integrity "sha1-Xs4uCaHFrDxzLg5K9XNzwr5YhRE= sha512-caFluGk2aHgPjeGxqcYvH3rake/01K5zQfjQ7RVtjye5ZvlSgEFSuorRynwFPs92Vw7tA9TjvVnNc3GDsWghgQ=="
  dependencies:
    "@babel/runtime" "^7.16.4"
    "@react-pdf/unicode-properties" "^2.5.0"
    hyphen "^1.6.4"
    ramda "^0.26.1"

"@react-pdf/types@^2.0.7", "@react-pdf/types@^2.0.8":
  version "2.0.8"
  resolved "https://registry.yarnpkg.com/@react-pdf/types/-/types-2.0.8.tgz"
  integrity "sha1-Lw88Q+GnyXUQDxUtWveOcfcXP90= sha512-/LngaZV8Z7+XEvbT2IVPbSxmU7pPYqCgdrrAkjGtdHsuGHRJegJMTBUEOellOY5Pqaqy3yvNTz/kZ9URrv+McQ=="

"@react-pdf/unicode-properties@^2.5.0":
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/@react-pdf/unicode-properties/-/unicode-properties-2.5.0.tgz"
  integrity "sha1-938roaVa0fac0u2IamDLVwDB8cU= sha512-L311rb7e2LInUcGpujxBjNQSU8C+4DHk83j7Q93mJwmsapCzwQmas2LxAJElGGQie90db21/X8sUniSLjrlceA=="
  dependencies:
    unicode-trie "^0.3.0"

"@react-pdf/yoga@^2.0.4":
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/@react-pdf/yoga/-/yoga-2.0.4.tgz"
  integrity "sha1-axTG8kTcVR2yCazQWgY1Shn+1m4= sha512-bsU48GQ8E4LEQ38AtyQPQZ9oEATMpolGPFewgI4sBXOZBNH2miLtoBTbyB/xEOMuBcyqtvJQwSNg2czSZjrlyQ=="
  dependencies:
    "@types/yoga-layout" "^1.9.3"

"@rollup/plugin-babel@^5.2.0":
  version "5.3.1"
  resolved "https://registry.yarnpkg.com/@rollup/plugin-babel/-/plugin-babel-5.3.1.tgz"
  integrity "sha1-BLwGCPSqSy5LGuvyhDRND2j9ooM= sha512-WFfdLWU/xVWKeRQnKmIAQULUI7Il0gZnBIH/ZFO069wYIfPu+8zrfp/KMW0atmELoRDq8FbiP3VCss9MhCut7Q=="
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@rollup/pluginutils" "^3.1.0"

"@rollup/plugin-node-resolve@^11.2.1":
  version "11.2.1"
  resolved "https://registry.yarnpkg.com/@rollup/plugin-node-resolve/-/plugin-node-resolve-11.2.1.tgz"
  integrity "sha1-gqpZOXopzU4TJIsQbmpKGIA2KmA= sha512-yc2n43jcqVyGE2sqV5/YCmocy9ArjVAP/BeXyTtADTBBX6V0e5UMqwO8CdQ0kzjb6zu5P1qMzsScCMRvE9OlVg=="
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    "@types/resolve" "1.17.1"
    builtin-modules "^3.1.0"
    deepmerge "^4.2.2"
    is-module "^1.0.0"
    resolve "^1.19.0"

"@rollup/plugin-replace@^2.4.1":
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/@rollup/plugin-replace/-/plugin-replace-2.4.2.tgz"
  integrity "sha1-otU5MU+8d8JEhY+qUjASglBoUQo= sha512-IGcu+cydlUMZ5En85jxHH4qj2hta/11BHq95iHEyb2sbgiN0eCdzvUcHw5gt9pBL5lTi4JDYJ1acCoMGpTvEZg=="
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    magic-string "^0.25.7"

"@rollup/pluginutils@^3.1.0":
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/@rollup/pluginutils/-/pluginutils-3.1.0.tgz"
  integrity "sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s= sha512-GksZ6pr6TpIjHm8h9lSQ8pi8BE9VeubNT0OMJ3B5uZJ8pz73NPiqOtCog/x2/QzM1ENChPKxMDhiQuRHsqc+lg=="
  dependencies:
    "@types/estree" "0.0.39"
    estree-walker "^1.0.1"
    picomatch "^2.2.2"

"@surma/rollup-plugin-off-main-thread@^2.2.3":
  version "2.2.3"
  resolved "https://registry.yarnpkg.com/@surma/rollup-plugin-off-main-thread/-/rollup-plugin-off-main-thread-2.2.3.tgz"
  integrity "sha1-7jSYWVLKIVWKsNlS8AKYrSGQwFM= sha512-lR8q/9W7hZpMWweNiAKU7NQerBnzQQLvi8qnTDU/fxItPhtZVMbPV3lbCwjhIlNBe9Bbr5V+KHshvWmVSG9cxQ=="
  dependencies:
    ejs "^3.1.6"
    json5 "^2.2.0"
    magic-string "^0.25.0"
    string.prototype.matchall "^4.0.6"

"@swc/helpers@0.4.11":
  version "0.4.11"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.4.11.tgz"
  integrity sha512-rEUrBSGIoSFuYxwBYtlUFMlE2CwGhmW+w9355/5oduSw8e5h2+Tj4UrAGNNgP9915++wj5vkQo0UuOBqOAq4nw==
  dependencies:
    tslib "^2.4.0"

"@tailwindcss/forms@^0.3.4":
  version "0.3.4"
  resolved "https://registry.yarnpkg.com/@tailwindcss/forms/-/forms-0.3.4.tgz"
  integrity "sha1-5JOdwWRQ7M9P0gKXcAlvOMu1VtQ= sha512-vlAoBifNJUkagB+PAdW4aHMe4pKmSLroH398UPgIogBFc91D2VlHUxe4pjxQhiJl0Nfw53sHSJSQBSTQBZP3vA=="
  dependencies:
    mini-svg-data-uri "^1.2.3"

"@types/body-parser@^1.19.2":
  version "1.19.2"
  resolved "https://registry.yarnpkg.com/@types/body-parser/-/body-parser-1.19.2.tgz"
  integrity "sha1-rqIFnii3ZYY5CBNHrE+rPeFm5vA= sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g=="
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/cleave.js@^1.4.4":
  version "1.4.6"
  resolved "https://registry.yarnpkg.com/@types/cleave.js/-/cleave.js-1.4.6.tgz"
  integrity "sha1-mFriWzVFmGeJwa2yYrIbs81Jsqw= sha512-OVW8lDUfaMa13OkWpH0ydQOB9IMLN9UZP4lBz63xCDZr/b7SqImRlBuzM1+eQb4whGPGGkZBOUe0Q7naOVO2dQ=="
  dependencies:
    "@types/react" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "https://registry.yarnpkg.com/@types/connect/-/connect-3.4.35.tgz"
  integrity "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE= sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ=="
  dependencies:
    "@types/node" "*"

"@types/estree@0.0.39":
  version "0.0.39"
  resolved "https://registry.yarnpkg.com/@types/estree/-/estree-0.0.39.tgz"
  integrity "sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8= sha512-EYNwp3bU+98cpU4lAWYYL7Zz+2gryWH1qbdDTidVd6hkiR6weksdbMadyXKXNPEkQFhXM+hVO9ZygomHXp+AIw=="

"@types/fs-extra@^8.0.0":
  version "8.1.2"
  resolved "https://registry.npmjs.org/@types/fs-extra/-/fs-extra-8.1.2.tgz"
  integrity sha512-SvSrYXfWSc7R4eqnOzbQF4TZmfpNSM9FrSWLU3EUnWBuyZqNBOrv1B1JA3byUDPUl9z4Ab3jeZG2eDdySlgNMg==
  dependencies:
    "@types/node" "*"

"@types/geojson@*":
  version "7946.0.8"
  resolved "https://registry.yarnpkg.com/@types/geojson/-/geojson-7946.0.8.tgz"
  integrity "sha1-MHRK/bOF4pReIvOwM/iX92sfEso= sha512-1rkryxURpr6aWP7R786/UQOkJ3PcpQiWkAXBmdWc7ryFWqN6a4xfK7BtjXvFBKO9LjQ+MWQSWxYeZX1OApnArA=="

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "https://registry.yarnpkg.com/@types/glob/-/glob-7.2.0.tgz"
  integrity "sha1-vBtb86qS8lvV3TnzXFc2G9zlsus= sha512-ZUxbzKl0IfJILTS6t7ip5fQQM/J3TJYubDm3nMbgubNNYS62eXeUpoLUC8/7fJNiFYHTrGPQn7hspDUzIHX3UA=="
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/google.maps@^3.54.10":
  version "3.55.12"
  resolved "https://registry.yarnpkg.com/@types/google.maps/-/google.maps-3.55.12.tgz#66b50be48533d116dddb3d705d277d06457735b0"
  integrity sha512-Q8MsLE+YYIrE1H8wdN69YHHAF8h7ApvF5MiMXh/zeCpP9Ut745mV9M0F4X4eobZ2WJe9k8tW2ryYjLa87IO2Sg==

"@types/hoist-non-react-statics@^3.3.1":
  version "3.3.1"
  resolved "https://registry.yarnpkg.com/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz"
  integrity "sha1-ESSq/lEYy1kZd66xzqrtEHDrA58= sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA=="
  dependencies:
    "@types/react" "*"
    hoist-non-react-statics "^3.3.0"

"@types/js-cookie@^2.2.6":
  version "2.2.7"
  resolved "https://registry.yarnpkg.com/@types/js-cookie/-/js-cookie-2.2.7.tgz"
  integrity "sha1-ImqeMWgINaYYjoh/OYjmDATT9qM= sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA=="

"@types/js-cookie@^3.0.0":
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/@types/js-cookie/-/js-cookie-3.0.2.tgz"
  integrity "sha1-RR6u7OZMa9rIst3gyqsjsIWJng0= sha512-6+0ekgfusHftJNYpihfkMu8BWdeHs9EOJuGcSofErjstGPfPGEu9yTu4t460lTzzAMl2cM5zngQJqPMHbbnvYA=="

"@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8":
  version "7.0.11"
  resolved "https://registry.yarnpkg.com/@types/json-schema/-/json-schema-7.0.11.tgz"
  integrity "sha1-1CG2xSejA398hEM/0sQingFoY9M= sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ=="

"@types/leaflet@^1.7.5":
  version "1.7.10"
  resolved "https://registry.yarnpkg.com/@types/leaflet/-/leaflet-1.7.10.tgz"
  integrity "sha1-CS+Xrym7hwt9HtctUW1LPd5mpsg= sha512-RzK5BYwYboOXXxyF01tp8g1J8UbdRvoaf+F/jCnVaWC42+QITB6wKvUklcX7jCMRWkzTnGO9NLg7A6SzrlGALA=="
  dependencies:
    "@types/geojson" "*"

"@types/lodash@^4.14.175", "@types/lodash@^4.14.176":
  version "4.14.182"
  resolved "https://registry.yarnpkg.com/@types/lodash/-/lodash-4.14.182.tgz"
  integrity "sha1-BTAaTV5iljIn6q/gzgTdd8VOpcI= sha512-/THyiqyQAP9AfARo4pF+aCGcyiQ94tX/Is2I7HofNRqoYLgN1PBoOWu2/zTA5zMxzP5EFutMtWtGAFRKUe961Q=="

"@types/minimatch@*":
  version "3.0.5"
  resolved "https://registry.yarnpkg.com/@types/minimatch/-/minimatch-3.0.5.tgz"
  integrity "sha1-EAHMXmo3BLg8I2An538vWOoBD0A= sha512-Klz949h02Gz2uZCMGwDUSDS1YBlTdDDgbWHi+81l29tQALUtvz4rAYi5uoVhE5Lagoq6DeqAUlbrHvW/mXDgdQ=="

"@types/node@*", "@types/node@^16.11.5":
  version "16.11.36"
  resolved "https://registry.yarnpkg.com/@types/node/-/node-16.11.36.tgz"
  integrity "sha1-mrn4J2mHEy7SsiXKziIYunlPx1E= sha512-FR5QJe+TaoZ2GsMHkjuwoNabr+UrJNRr2HNOo+r/7vhcuntM6Ee/pRPOnRhhL2XE9OOvX9VLEq+BcXl3VjNoWA=="

"@types/overlayscrollbars@^1.12.1":
  version "1.12.1"
  resolved "https://registry.yarnpkg.com/@types/overlayscrollbars/-/overlayscrollbars-1.12.1.tgz"
  integrity "sha1-+2NwcbVFg0+xKuqU7jCaL/TNwKg= sha512-V25YHbSoKQN35UasHf0EKD9U2vcmexRSp78qa8UglxFH8H3D+adEa9zGZwrqpH4TdvqeMrgMqVqsLB4woAryrQ=="

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/@types/parse-json/-/parse-json-4.0.0.tgz"
  integrity "sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA= sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA=="

"@types/prop-types@*":
  version "15.7.5"
  resolved "https://registry.yarnpkg.com/@types/prop-types/-/prop-types-15.7.5.tgz"
  integrity "sha1-XxnSuFqY6VWANvajysyIGUIPBc8= sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="

"@types/react-copy-to-clipboard@^5.0.2":
  version "5.0.2"
  resolved "https://registry.yarnpkg.com/@types/react-copy-to-clipboard/-/react-copy-to-clipboard-5.0.2.tgz"
  integrity "sha1-wpaQtHKlTt/zWRbw0cbHl60P00s= sha512-O29AThfxrkUFRsZXjfSWR2yaWo0ppB1yLEnHA+Oh24oNetjBAwTDu1PmolIqdJKzsZiO4J1jn6R6TmO96uBvGg=="
  dependencies:
    "@types/react" "*"

"@types/react-datepicker@^4.4.2":
  version "4.4.2"
  resolved "https://registry.npmjs.org/@types/react-datepicker/-/react-datepicker-4.4.2.tgz"
  integrity sha512-g8DhWvYmaIMLzVrIEVLXncylyImyBaoPsEUr3yR13JDaaHoebhDorqnVv4tLkNGa8SjBB8SAOQvxD5jaPNBX8A==
  dependencies:
    "@popperjs/core" "^2.9.2"
    "@types/react" "*"
    date-fns "^2.0.1"
    react-popper "^2.2.5"

"@types/react-lazy-load-image-component@^1.5.3":
  version "1.5.3"
  resolved "https://registry.yarnpkg.com/@types/react-lazy-load-image-component/-/react-lazy-load-image-component-1.5.3.tgz#ec64f698510e2c1f6fe09ec2e7eb0b5ed7bedab9"
  integrity sha512-hTzsQQ64mmPR6W03DQr6zhzeBGn17ExnVd7sLHqTQZQVd9Oi0Dy7tfSPgK0+AZV5YNC+e8Jw7oq9kzYfDkCDCA==
  dependencies:
    "@types/react" "*"
    csstype "^3.0.2"

"@types/react-scroll@^1.8.3":
  version "1.8.3"
  resolved "https://registry.yarnpkg.com/@types/react-scroll/-/react-scroll-1.8.3.tgz"
  integrity "sha1-gJUe0ZNKtJ1JJqrZXCZge4salxM= sha512-Xt0+Y58pwrIv+vRFxcyKDoo0gBWBR2eNMJ4XRMhQpZdGUtmjnszPi/wFEJSAY+5r83ypJsSA+hOpSc3hRpYlWw=="
  dependencies:
    "@types/react" "*"

"@types/react-select@^5.0.1":
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/@types/react-select/-/react-select-5.0.1.tgz"
  integrity "sha1-BPyF7dNKcmdaCrVq1MMEKKqw5EQ= sha512-h5Im0AP0dr4AVeHtrcvQrLV+gmPa7SA0AGdxl2jOhtwiE6KgXBFSogWw8az32/nusE6AQHlCOHQWjP1S/+oMWA=="
  dependencies:
    react-select "*"

"@types/react-star-rating-component@^1.4.1":
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/@types/react-star-rating-component/-/react-star-rating-component-1.4.1.tgz"
  integrity "sha1-Mcw6EkVbtPhlDkTcT6AiISX/Zzk= sha512-f4rKKGvS9//wr2mjsT2Ol3N7tgV3DPmu4RZRp2dabEvYukHDx5tIjXecnEZGxpdU6HszKkvbpcy4kPq5VrAqew=="
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@^4.4.0":
  version "4.4.4"
  resolved "https://registry.yarnpkg.com/@types/react-transition-group/-/react-transition-group-4.4.4.tgz"
  integrity "sha1-rNTM6qK+a3V9th7XtDLhAyQtFj4= sha512-7gAPz7anVK5xzbeQW9wFBDg7G++aPLAFY0QaSMOou9rJZpbuI58WAuJrgu+qR92l61grlnCUe7AFX8KGahAgug=="
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^17.0.32":
  version "17.0.45"
  resolved "https://registry.yarnpkg.com/@types/react/-/react-17.0.45.tgz"
  integrity "sha1-mz1bZh/SY2X+/vDnZqHGwwzPez8= sha512-YfhQ22Lah2e3CHPsb93tRwIGNiSwkuz1/blk4e6QrWS0jQzCSNbGLtOEYhPg02W0yGTTmpajp7dCTbBAMN3qsg=="
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/resolve@1.17.1":
  version "1.17.1"
  resolved "https://registry.yarnpkg.com/@types/resolve/-/resolve-1.17.1.tgz"
  integrity "sha1-Ov1q2JZ8d+Q3bFmKgt3Vj0bsRdY= sha512-yy7HuzQhj0dhGpD8RLXSZWEkLsV9ibvxvi6EiJ3bkqLAO1RGo0WbkWQiwpRlSFymTJRz0d3k5LM3kkx8ArDbLw=="
  dependencies:
    "@types/node" "*"

"@types/scheduler@*":
  version "0.16.2"
  resolved "https://registry.yarnpkg.com/@types/scheduler/-/scheduler-0.16.2.tgz"
  integrity "sha1-GmL4lSVyPd4kuhsBsJK/XfitTTk= sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew=="

"@types/slice-ansi@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmjs.org/@types/slice-ansi/-/slice-ansi-4.0.0.tgz"
  integrity sha512-+OpjSaq85gvlZAYINyzKpLeiFkSC4EsC6IIiT6v6TLSU5k5U83fHGj9Lel8oKEXM0HqgrMVCjXPDPVICtxF7EQ==

"@types/trusted-types@^2.0.2":
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/@types/trusted-types/-/trusted-types-2.0.2.tgz"
  integrity "sha1-/CWtmUO8rBHM64Fo208nXg5y51Y= sha512-F5DIZ36YVLE+PN+Zwws4kJogq47hNgX3Nx6WyDJ3kcplxyke3XIzB8uK5n/Lpm1HBsbGzd6nmGehL8cPekP+Tg=="

"@types/yoga-layout@^1.9.3":
  version "1.9.4"
  resolved "https://registry.yarnpkg.com/@types/yoga-layout/-/yoga-layout-1.9.4.tgz"
  integrity "sha1-+tVP72I0ZNrNnnZdhU8Odv8RhPc= sha512-RRHc1+8Hc5mf/2lZKnom6kCnqcNS07s8keahniWTOva0KELF6RgDJmaEcvGEKUUJgN4UgessmEsWuidaOycIOw=="

"@vis.gl/react-google-maps@^1.1.0":
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/@vis.gl/react-google-maps/-/react-google-maps-1.1.0.tgz#1373fd0acf021e4adfa356a79ff624fbc77b18ed"
  integrity sha512-MxDIhCfPRzTQY1c6sS0GFg8Ukl40o13fkIKEaCN0cR1BIrV4LPo+EuCov9WElbe0bOo8MApx5qAbqBKOmLQyKg==
  dependencies:
    "@types/google.maps" "^3.54.10"
    fast-deep-equal "^3.1.3"

"@xobotyi/scrollbar-width@^1.9.5":
  version "1.9.5"
  resolved "https://registry.yarnpkg.com/@xobotyi/scrollbar-width/-/scrollbar-width-1.9.5.tgz"
  integrity "sha1-gCJKaRknL0Bbh5E8oTuSkpvfPE0= sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ=="

abs-svg-path@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/abs-svg-path/-/abs-svg-path-0.1.1.tgz"
  integrity "sha1-32Acjo0roQ1KdtYl4japo5wnI78= sha512-d8XPSGjfyzlXC3Xx891DJRyZfqk5JU0BJrDQcsWomFIV1/BIzPW5HDH5iDdWpqWaav0YVIEzT1RHTwWr0FFshA=="

acorn-node@^1.6.1:
  version "1.8.2"
  resolved "https://registry.yarnpkg.com/acorn-node/-/acorn-node-1.8.2.tgz"
  integrity "sha1-EUyV1kU55T3t4j3oudlt98euKvg= sha512-8mt+fslDufLYntIoPAaIMUe/lrbrehIiwmR3t2k9LljIzoigEPF27eLk2hy8zSGzmR/ogr7zbRKINMo1u0yh5A=="
  dependencies:
    acorn "^7.0.0"
    acorn-walk "^7.0.0"
    xtend "^4.0.2"

acorn-walk@^7.0.0:
  version "7.2.0"
  resolved "https://registry.yarnpkg.com/acorn-walk/-/acorn-walk-7.2.0.tgz"
  integrity "sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w= sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA=="

acorn@^7.0.0:
  version "7.4.1"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-7.4.1.tgz"
  integrity "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo= sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A=="

acorn@^8.5.0:
  version "8.7.1"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-8.7.1.tgz"
  integrity "sha1-AZcSLIQ9G/bQpegyIKeI8nj2PDA= sha512-Xx54uLJQZ19lKygFXOWsscKUbsBZW0CPykPhVQdhIeIwrbPmJzqeASDInc8nKBnp/JT6igTs82qPXz069H8I/A=="

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity "sha1-MfKdpatuANHC0yms97WSlhTVAU0= sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ=="

ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-6.12.6.tgz"
  integrity "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ= sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.6.0:
  version "8.11.0"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-8.11.0.tgz"
  integrity "sha1-l36R3ZbKZp9UoR4j43jjO4hKVl8= sha512-wGgprdCvMalC0BztXvitD2hC04YffAvtsUn93JbGXYLAtCUO4xd17mCCZQxUOItiBwZvJScWo8NIvQMQ71rdpg=="
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/amdefine/-/amdefine-1.0.1.tgz"
  integrity "sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU= sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg=="

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz"
  integrity "sha1-w7M6te42DYbg5ijwRorn7yfWVN8= sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA=="

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0= sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA=="
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity "sha1-7dgDYornHATIWuegkG7a00tkiTc= sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  dependencies:
    color-convert "^2.0.1"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/anymatch/-/anymatch-3.1.2.tgz"
  integrity "sha1-wFV8CWrzLxBhmPT04qODU343hxY= sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg=="
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.0.3:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/aproba/-/aproba-1.2.0.tgz"
  integrity "sha1-aALmJk79GMeQobDVF/DyYnvyyUo= sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw=="

are-we-there-yet@~1.1.2:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/are-we-there-yet/-/are-we-there-yet-1.1.7.tgz"
  integrity "sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY= sha512-nxwy40TuMiUGqMyRHgCSWZ9FM4VAoRP4xUYSTv5ImRog+h9yISPbVH7H8fASCIzYn9wlEv4zvFL7uKDMCFQm3g=="
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

arg@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/arg/-/arg-5.0.1.tgz"
  integrity "sha1-6wyaj3d4bK0q+P8rhiiZhC17ats= sha512-e0hDa9H2Z9AwFkk2qDlwhoMYE4eToKarchkQHovNdLTCYMHZHeRjI71crOh+dio4K6u1IcwubQqo79Ga4CyAQA=="

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-union/-/array-union-1.0.2.tgz"
  integrity "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk= sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng=="
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/array-union/-/array-union-2.1.0.tgz"
  integrity "sha1-t5hCCtvrHego2ErNii4j0+/oXo0= sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw=="

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/array-uniq/-/array-uniq-1.0.3.tgz"
  integrity "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY= sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q=="

ast-transform@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/ast-transform/-/ast-transform-0.0.0.tgz"
  integrity "sha1-dJRAWIh9goPhidlUYAlHvJj+AGI= sha512-e/JfLiSoakfmL4wmTGPjv0HpTICVmxwXgYOB8x+mzozHL8v+dSfCbrJ8J8hJ0YBP0XcYu1aLZ6b/3TnxNK3P2A=="
  dependencies:
    escodegen "~1.2.0"
    esprima "~1.0.4"
    through "~2.3.4"

ast-types@^0.7.0:
  version "0.7.8"
  resolved "https://registry.yarnpkg.com/ast-types/-/ast-types-0.7.8.tgz"
  integrity "sha1-kC0uDWDQcb3NRtwRXhgJ7RHBOKk= sha512-RIOpVnVlltB6PcBJ5BMLx+H+6JJ/zjDGU0t7f0L6c2M1dqcK92VQopLBlPQ9R80AVXelfqYgjcPLtHtDbNFg0Q=="

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz"
  integrity sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==

async@^3.2.3:
  version "3.2.3"
  resolved "https://registry.yarnpkg.com/async/-/async-3.2.3.tgz"
  integrity "sha1-rFPa/T9HIO6eihYGKPGOqR3xlsk= sha512-spZRyzKL5l5BZQrr/6m/SqFdBN0q3OCI0f9rjfBzCMBIP4p75P620rR3gTmaksNOhmzgdxcaxdNfMy6anrbM0g=="

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/at-least-node/-/at-least-node-1.0.0.tgz"
  integrity "sha1-YCzUtG6EStTv/JKoARo8RuAjjcI= sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg=="

autoprefixer@10.4.5:
  version "10.4.5"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.5.tgz"
  integrity sha512-Fvd8yCoA7lNX/OUllvS+aS1I7WRBclGXsepbvT8ZaPgrH24rgXpZzF0/6Hh3ZEkwg+0AES/Osd196VZmYoEFtw==
  dependencies:
    browserslist "^4.20.2"
    caniuse-lite "^1.0.30001332"
    fraction.js "^4.2.0"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

axios@^0.24.0:
  version "0.24.0"
  resolved "https://registry.yarnpkg.com/axios/-/axios-0.24.0.tgz"
  integrity "sha1-gE5voeS5xSiFAd2d/1anoJQNINY= sha512-Q6cWsys88HoPgAaFAVUb0WpPk0O8iTeisR9IMqy9G8AbO4NlpVknrnQS03zzF9PGAWgO3cgletO3VjV/P7VztA=="
  dependencies:
    follow-redirects "^1.14.4"

babel-loader@^8.2.4:
  version "8.2.5"
  resolved "https://registry.yarnpkg.com/babel-loader/-/babel-loader-8.2.5.tgz"
  integrity "sha1-1F9YXmVNWl2Q9TUKd512R8XtUS4= sha512-OSiFfH89LrEMiWd4pLNqGz4CwJDtbs2ZVc+iGu2HrkRfPxId9F2anQj38IxWpmRfsUY0aBZYi1EFcd3mhtRMLQ=="
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^2.0.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M= sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ=="
  dependencies:
    object.assign "^4.1.0"

babel-plugin-macros@^2.6.1:
  version "2.8.0"
  resolved "https://registry.yarnpkg.com/babel-plugin-macros/-/babel-plugin-macros-2.8.0.tgz"
  integrity "sha1-D5WKfMZVax5lNERl2ZERoeXhATg= sha512-SEP5kJpfGYqYKpBrj5XU3ahw5p5GOHJ0U5ssOSQ/WBVdwkD2Dzlce95exQTs3jOVWPPKLBN2rlEWkCK7dSmLvg=="
  dependencies:
    "@babel/runtime" "^7.7.2"
    cosmiconfig "^6.0.0"
    resolve "^1.12.0"

babel-plugin-polyfill-corejs2@^0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.1.tgz"
  integrity "sha1-RA8bcMz6q8a2dtGWI5sTj4os+6U= sha512-v7/T6EQcNfVLfcN2X8Lulb7DjprieyLWJK/zOWH5DUYcAgex9sP3h25Q+DLsX9TloXe3y1O8l2q2Jv9q8UVB9w=="
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.5.0:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.2.tgz"
  integrity "sha1-qr5LL6BKbgOLaIxeVdROeM06X3I= sha512-G3uJih0XWiID451fpeFaYGVuxHEjzKTHtc9uGFEjR6hHrvNzeS/PX+LLLcetJcytsB5m4j+K3o/EpXJNb/5IEQ=="
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"
    core-js-compat "^3.21.0"

babel-plugin-polyfill-regenerator@^0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.1.tgz"
  integrity "sha1-LAZ46kfHXIzC+7GFInjY+2gjOZA= sha512-Y2B06tvgHYt1x0yz17jGkGeeMr5FeKUu+ASJ+N6nB5lQ8Dapfg42i0OVrf8PNGJ3zKL4A23snMi1IRwrqqND7A=="
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.1"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4= sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="

base64-js@^1.1.2, base64-js@^1.3.1, base64-js@^1.5.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.5.1.tgz"
  integrity "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo= sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA=="

big-integer@1.6.x, big-integer@^1.6.16:
  version "1.6.51"
  resolved "https://registry.yarnpkg.com/big-integer/-/big-integer-1.6.51.tgz"
  integrity "sha1-DfkqXZiAVg0/8tX9ICRciJ0TBoY= sha512-GPEid2Y9QU1Exl1rpO9B2IPJGHPSupF5GnVIP0blYvNOMer2bTvSWs1jGOUg04hTmu67nmLsQ9TBo1puaotBHg=="

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.yarnpkg.com/big.js/-/big.js-5.2.2.tgz"
  integrity "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg= sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ=="

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0= sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA=="

bl@^4.0.3:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/bl/-/bl-4.1.0.tgz"
  integrity "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo= sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w=="
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

blob-stream@^0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/blob-stream/-/blob-stream-0.1.3.tgz"
  integrity "sha1-mNZor2mW4PMu9mbQbiFczH13aGw= sha512-xXwyhgVmPsFVFFvtM5P0syI17/oae+MIjLn5jGhuD86mmSJ61EWMWmbPrV/0+bdcH9jQ2CzIhmTQKNUJL7IPog=="
  dependencies:
    blob "0.0.4"

blob@0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/blob/-/blob-0.0.4.tgz"
  integrity "sha1-vPEwUspURj8w+fx+lbmkdjCpSSE= sha512-YRc9zvVz4wNaxcXmiSgb9LAg7YYwqQ2xd0Sj6osfA7k/PKmIGVlnOYs3wOFdkRC9/JpQu8sGt/zHgJV7xzerfg=="

bplist-parser@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.2.tgz"
  integrity sha512-apC2+fspHGI3mMKj+dGevkGo/tCqVB8jMb6i+OX+E29p0Iposz07fABkRIfVUPNd5A5VbuOz1bZbnmkKLYF+wQ==
  dependencies:
    big-integer "1.6.x"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0= sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity "sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4= sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/braces/-/braces-3.0.2.tgz"
  integrity "sha1-NFThpGLujVmeI23zNs2epPiv4Qc= sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A=="
  dependencies:
    fill-range "^7.0.1"

broadcast-channel@^3.4.1:
  version "3.7.0"
  resolved "https://registry.yarnpkg.com/broadcast-channel/-/broadcast-channel-3.7.0.tgz"
  integrity "sha1-Lfpce0KJVHrD9nBfnACvhyOImTc= sha512-cIAKJXAxGJceNZGTZSBzMxzyOn72cVgPnKx4dc6LRjQgbaJUQqhy5rzL3zbMxkMWsGKkv2hSFkPRMEXfoMZ2Mg=="
  dependencies:
    "@babel/runtime" "^7.7.2"
    detect-node "^2.1.0"
    js-sha3 "0.8.0"
    microseconds "0.2.0"
    nano-time "1.0.0"
    oblivious-set "1.0.0"
    rimraf "3.0.2"
    unload "2.2.0"

brotli@^1.2.0:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/brotli/-/brotli-1.3.2.tgz"
  integrity "sha1-UlqcrU/LqWR119OI9q7LE+7VL0Y= sha512-K0HNa0RRpUpcF8yS4yNSd6vmkrvA+wRd+symIcwhfqGLAi7YgGlKfO4oDYVgiahiLGNviO9uY7Zlb1MCPeTmSA=="
  dependencies:
    base64-js "^1.1.2"

browser-resolve@^1.8.1:
  version "1.11.3"
  resolved "https://registry.yarnpkg.com/browser-resolve/-/browser-resolve-1.11.3.tgz"
  integrity "sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY= sha512-exDi1BYWB/6raKHmDTCicQfTkqwN5fioMFV4j8BsfMU4R2DK/QfZfK7kOVkmWCNANf0snkBzqGqAJBao9gZMdQ=="
  dependencies:
    resolve "1.1.7"

browserify-optional@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/browserify-optional/-/browserify-optional-1.0.1.tgz"
  integrity "sha1-HhNyLP3g2F8SFnbCpyztUzoBiGk= sha512-VrhjbZ+Ba5mDiSYEuPelekQMfTbhcA2DhLk2VQWqdcCROWeFqlTcXZ7yfRkXCIl8E+g4gINJYJiRB7WEtfomAQ=="
  dependencies:
    ast-transform "0.0.0"
    ast-types "^0.7.0"
    browser-resolve "^1.8.1"

browserslist@^4.20.2, browserslist@^4.20.3:
  version "4.20.3"
  resolved "https://registry.yarnpkg.com/browserslist/-/browserslist-4.20.3.tgz"
  integrity "sha1-63Vy9J7EMOBU9W1S/w6+m+kV+L8= sha512-NBhymBQl1zM0Y5dQT/O+xiLP9/rzOIQdKM/eMJBAq7yBgaB6krIYLGejrwVYnSHZdqjscB1SPuAjHwxjvN6Wdg=="
  dependencies:
    caniuse-lite "^1.0.30001332"
    electron-to-chromium "^1.4.118"
    escalade "^3.1.1"
    node-releases "^2.0.3"
    picocolors "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz"
  integrity sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U= sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.7.1.tgz"
  integrity "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA= sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ=="
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

builtin-modules@^3.1.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/builtin-modules/-/builtin-modules-3.3.0.tgz"
  integrity "sha1-yuYoEriYAellYzbkYiPgMDhr57Y= sha512-zhaCDicdLuWN5UbN5IMnFqNMhNfo919sH85y2/ea+5Yg9TsTkeZxpL+JLbp6cgYFS4sRLp3YV4S6yDuqVWHYOw=="

bytes@^3.0.0:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-3.1.2.tgz"
  integrity "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU= sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/call-bind/-/call-bind-1.0.2.tgz"
  integrity "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw= sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA=="
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/callsites/-/callsites-3.1.0.tgz"
  integrity "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M= sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity "sha1-7pePaUeRTMMMa0R0G27R338EP9U= sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA=="

camelcase-keys@^7.0.0:
  version "7.0.2"
  resolved "https://registry.yarnpkg.com/camelcase-keys/-/camelcase-keys-7.0.2.tgz"
  integrity "sha1-0EjYxpRIdFuw3m/EwcUqMN++clI= sha512-Rjs1H+A9R+Ig+4E/9oyB66UC5Mj9Xq3N//vcLf2WzgdTi/3gUu3Z9KoqmlrEG4VuuLK8wJHofxzdQXz/knhiYg=="
  dependencies:
    camelcase "^6.3.0"
    map-obj "^4.1.0"
    quick-lru "^5.1.1"
    type-fest "^1.2.1"

camelcase@^6.3.0:
  version "6.3.0"
  resolved "https://registry.yarnpkg.com/camelcase/-/camelcase-6.3.0.tgz"
  integrity "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo= sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA=="

caniuse-lite@^1.0.30001332, caniuse-lite@^1.0.30001406:
  version "1.0.30001426"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001426.tgz"
  integrity sha512-n7cosrHLl8AWt0wwZw/PJZgUg3lV0gk9LMI7ikGJwhyhgsd2Nb65vKvmSexCqq/J7rbH3mFG6yZZiPR5dLPW5A==

capacitor-plugin-app-tracking-transparency@^2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/capacitor-plugin-app-tracking-transparency/-/capacitor-plugin-app-tracking-transparency-2.0.3.tgz#27f6fc7e773bf0db09d2e923ad01dd0fb51ed3ef"
  integrity sha512-ZFRZDjFWYBCT50t6HQCEhZbHmmiyQ+ggbn5AZG53/J8taJvkhFQsQEbOGbKdR1d5XSufRIRWpRXqq1kBoTi6VA==

chalk@^2.0.0:
  version "2.4.2"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.4.2.tgz"
  integrity "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ= sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ=="
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.2, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-4.1.2.tgz"
  integrity "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE= sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.5.2:
  version "3.5.3"
  resolved "https://registry.yarnpkg.com/chokidar/-/chokidar-3.5.3.tgz"
  integrity "sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70= sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^1.1.1:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/chownr/-/chownr-1.1.4.tgz"
  integrity "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs= sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg=="

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

classnames@^2.2.1, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/classnames/-/classnames-2.3.1.tgz"
  integrity "sha1-38+jiR4wbsHa0QXQ6I9EF7hTXo4= sha512-OlQdbZ7gLfGarSqxesMesDa5uz7KFbID8Kpq/SxIoNGDqY8lSYs0D+hhtBXhcdB3rcbXArFr7vlHheLk1voeNA=="

clean-webpack-plugin@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/clean-webpack-plugin/-/clean-webpack-plugin-4.0.0.tgz"
  integrity "sha1-cpR9RAPUUvOO1hqf8K2oEiqs1yk= sha512-WuWE1nyTNAyW5T7oNyys2EN0cfP2fdRxhxnIQWiAp0bMabPdHhoGxM8A6YL2GhqwgrPnnaemVE7nv5XJ2Fhh2w=="
  dependencies:
    del "^4.1.1"

cleave.js@^1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/cleave.js/-/cleave.js-1.6.0.tgz"
  integrity "sha1-Dk4BGUO91wxnydz0/4AM5xBSkXE= sha512-ivqesy3j5hQVG3gywPfwKPbi/7ZSftY/UNp5uphnqjr25yI2CP8FS2ODQPzuLXXnNLi29e2+PgPkkiKUXLs/Nw=="

clone@^1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/clone/-/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clsx@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/clsx/-/clsx-1.1.1.tgz"
  integrity "sha1-mLMTT5q73yOyZjSRrOE8XAOnMYg= sha512-6/bPho624p3S2pMyvP5kKBPXnI3ufHLObBFCfgx+LkeR5lg2XYy2hqZqUf45ypD8COn2bhgGJSUE+l5dhNBieA=="

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/code-point-at/-/code-point-at-1.1.0.tgz"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.3.tgz"
  integrity "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg= sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg=="
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-2.0.1.tgz"
  integrity "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM= sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.4.tgz"
  integrity "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI= sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="

color-string@^1.5.3, color-string@^1.9.0:
  version "1.9.1"
  resolved "https://registry.yarnpkg.com/color-string/-/color-string-1.9.1.tgz"
  integrity "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q= sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg=="
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^4.0.1:
  version "4.2.3"
  resolved "https://registry.yarnpkg.com/color/-/color-4.2.3.tgz"
  integrity "sha1-14HsteVyJO5D6pYnVgEHwODGRjo= sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A=="
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.20.3.tgz"
  integrity "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM= sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="

commander@^8.0.0:
  version "8.3.0"
  resolved "https://registry.yarnpkg.com/commander/-/commander-8.3.0.tgz"
  integrity "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY= sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww=="

commander@^9.3.0:
  version "9.4.1"
  resolved "https://registry.npmjs.org/commander/-/commander-9.4.1.tgz"
  integrity sha512-5EEkTNyHNGFPD2H+c/dXXfQZYa/scCKasxWcXJaWnNJ99pnQN9Vnmqow+p+PlFPE63Q6mThaZws1T+HxfpgtPw==

common-tags@^1.8.0:
  version "1.8.2"
  resolved "https://registry.yarnpkg.com/common-tags/-/common-tags-1.8.2.tgz"
  integrity "sha1-lOuzwHbSYDJ0X9VPrOf2iO9aycY= sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA=="

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/commondir/-/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=

"consolidated-events@^1.1.0 || ^2.0.0":
  version "2.0.2"
  resolved "https://registry.npmjs.org/consolidated-events/-/consolidated-events-2.0.2.tgz"
  integrity sha512-2/uRVMdRypf5z/TW/ncD/66l75P5hH2vM/GR8Jf8HLc2xnfJtmina6F6du8+v4Z2vTrMo7jC+W1tmEEuuELgkQ==

convert-source-map@^1.5.0, convert-source-map@^1.7.0:
  version "1.8.0"
  resolved "https://registry.yarnpkg.com/convert-source-map/-/convert-source-map-1.8.0.tgz"
  integrity "sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k= sha512-+OQdjP49zViI/6i7nIJpA8rAl4sV/JdPfU9nZs3VqOwGIgizICvuN2ru6fMd+4llL0tar18UYJXfZ/TWtmhUjA=="
  dependencies:
    safe-buffer "~5.1.1"

copy-to-clipboard@^3.3.1:
  version "3.3.1"
  resolved "https://registry.yarnpkg.com/copy-to-clipboard/-/copy-to-clipboard-3.3.1.tgz"
  integrity "sha1-EVqhqZmP+rYZb5MHatbaO5E2Yq4= sha512-i13qo6kIHTTpCm8/Wup+0b1mVWETvu2kIMzKoK8FpkLkFxlt0znUAHcMzox+T8sPlqtZXq3CulEjQHsYiGFJUw=="
  dependencies:
    toggle-selection "^1.0.6"

cordova-plugin-inappbrowser@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/cordova-plugin-inappbrowser/-/cordova-plugin-inappbrowser-5.0.0.tgz#2f1cf3ebfecf43bdd175a74743ee426832054951"
  integrity sha512-MqnpmUQ/iy6hxtIGDdlIhy8aNi1pNanLATpbnkk7uCqW9YQ4rH/dGK9zESbZ50pUi2A2D2QMjBXNV175TJK5OQ==

core-js-compat@^3.21.0, core-js-compat@^3.22.1:
  version "3.22.6"
  resolved "https://registry.yarnpkg.com/core-js-compat/-/core-js-compat-3.22.6.tgz"
  integrity "sha1-Lnx6cDI4wWiIPb9Vwku7Z1A83cs= sha512-dQ/SxlHcuiywaPIoSUCU6Fx+Mk/H5TXENqd/ZJcK85ta0ZcQkbzHwblxPeL0hF5o+NsT2uK3q9ZOG5TboiVuWw=="
  dependencies:
    browserslist "^4.20.3"
    semver "7.0.0"

core-js@^3:
  version "3.22.6"
  resolved "https://registry.yarnpkg.com/core-js/-/core-js-3.22.6.tgz"
  integrity "sha1-KU3YJLTK4sJHJaNrqkp5HtALsN4= sha512-2IGcGH00z9I4twgNWU4uGCNEsBFG1s2JudVQrgSCoVhOfwoTwQjxC8aMo9exrpTMOxvobggEpaHnGMmQY4cfBQ=="

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U= sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/cosmiconfig/-/cosmiconfig-6.0.0.tgz"
  integrity "sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI= sha512-xb3ZL6+L8b9JLLCx3ZdoZy4+2ECphCMo2PwqgP1tlfVq6M6YReyzBJtvWWtbDSpNr9hn96pkCiZqUcFEc+54Qg=="
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cosmiconfig@^7.0.1:
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/cosmiconfig/-/cosmiconfig-7.0.1.tgz"
  integrity "sha1-cU11ZSLKzoZ4Z8y0R0xdAbuuXW0= sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ=="
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-fetch@^3.1.5:
  version "3.1.5"
  resolved "https://registry.yarnpkg.com/cross-fetch/-/cross-fetch-3.1.5.tgz"
  integrity "sha1-4TifRNnnunZ5B/evhFR4eVKrU08= sha512-lvb1SBsI0Z7GDwmuid+mU3kWVBwTVUbe7S0H52yaaAdQOXq2YktTCZdlAcNKFzE6QtRz0snpw9bNiPeOIkkQvw=="
  dependencies:
    node-fetch "2.6.7"

cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^4.0.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/crypto-js/-/crypto-js-4.1.1.tgz"
  integrity "sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8= sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw=="

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/crypto-random-string/-/crypto-random-string-2.0.0.tgz"
  integrity "sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU= sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA=="

css-color-names@^0.0.4:
  version "0.0.4"
  resolved "https://registry.yarnpkg.com/css-color-names/-/css-color-names-0.0.4.tgz"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-in-js-utils@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/css-in-js-utils/-/css-in-js-utils-2.0.1.tgz"
  integrity "sha1-O0crOYeHKRtHz+PkT+z92ekUupk= sha512-PJF0SpJT+WdbVVt0AOYp9C8GnuruRlL/UFW7932nLWmFLQTaWEzTBQEx7/hn4BuV+WON75iAViSUJLiU3PKbpA=="
  dependencies:
    hyphenate-style-name "^1.0.2"
    isobject "^3.0.1"

css-tree@^1.1.2:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/css-tree/-/css-tree-1.1.3.tgz"
  integrity "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0= sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q=="
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-unit-converter@^1.1.1:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/css-unit-converter/-/css-unit-converter-1.1.2.tgz"
  integrity "sha1-THf1oZVObb/2BpXsshTjJwQ2qyE= sha512-IiJwMC8rdZE0+xiEZHeru6YoONC4rfPMqGm2W85jMIbkFvv5nFTwJVFHam2eFrN6txmoUYFAFXiv8ICVeTO0MA=="

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/cssesc/-/cssesc-3.0.0.tgz"
  integrity "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4= sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg=="

csstype@^3.0.2, csstype@^3.0.6:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/csstype/-/csstype-3.1.0.tgz"
  integrity "sha1-TdysNxjXh8+d8NG30VAzklyPKfI= sha512-uX1KG+x9h5hIJsaKR9xHUeUraxf8IODOwq9JLNPq6BwB04a/xgpq3rcx47l5BZu5zBPlgD342tdke3Hom/nJRA=="

date-fns@^2.0.1, date-fns@^2.24.0:
  version "2.29.3"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-2.29.3.tgz"
  integrity sha512-dDCnyH2WnnKusqvZZ6+jA1O51Ibt8ZMRNkDZdyAyK4YfbDwa/cEmuztzG5pk6hqlp9aSBPYcjOlktquahGwGeA==

dayjs@^1.10.7:
  version "1.11.2"
  resolved "https://registry.yarnpkg.com/dayjs/-/dayjs-1.11.2.tgz"
  integrity "sha1-+g9SI+8NZySz2DJxNIkM/j1y++U= sha512-F4LXf1OeU9hrSYRPTTj/6FbO4HTjPKXvEIC1P2kcnFurViINCVk3ZV0xAS3XVx9MkMsXbbqlK6hjseaYbgKEHw=="

debug@^2.1.3:
  version "2.6.9"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.9.tgz"
  integrity "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8= sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  dependencies:
    ms "2.0.0"

debug@^4.0.0, debug@^4.1.0, debug@^4.1.1, debug@^4.3.4:
  version "4.3.4"
  resolved "https://registry.yarnpkg.com/debug/-/debug-4.3.4.tgz"
  integrity "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU= sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  dependencies:
    ms "2.1.2"

decompress-response@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/decompress-response/-/decompress-response-6.0.0.tgz"
  integrity "sha1-yjh2Et234QS9FthaqwDV7PCcZvw= sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ=="
  dependencies:
    mimic-response "^3.1.0"

deep-equal@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/deep-equal/-/deep-equal-1.1.1.tgz"
  integrity "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o= sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g=="
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/deep-extend/-/deep-extend-0.6.0.tgz"
  integrity "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw= sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA=="

deepmerge@^4.0.0, deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.yarnpkg.com/deepmerge/-/deepmerge-4.2.2.tgz"
  integrity "sha1-RNLqNnm49NT/ujPwPYZfwee/SVU= sha512-FJ3UgI4gIl+PHZm53knsuSFpE+nESMr7M4v9QcgB7S63Kj/6WqMiFQJpBBYz1Pt+66bZpP3Q7Lye0Oo9MPKEdg=="

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==

define-properties@^1.1.3, define-properties@^1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/define-properties/-/define-properties-1.1.4.tgz"
  integrity "sha1-CxTXvX++svNXLDp+2oDqXVf7BbE= sha512-uckOqKcfaVvtBdsVkdPv3XjveQJsNQqmhXgRi8uhvWWuPYZCNlzT8qAyblUgNoXdHdjMTzAqeGjAoli8f+bzPA=="
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

defined@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/defined/-/defined-1.0.0.tgz"
  integrity sha1-yY2bzvdWdBiOEQlpFRGZ45sfppM=

del@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/del/-/del-4.1.1.tgz"
  integrity "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ= sha512-QwGuEUouP2kVwQenAsOof5Fv8K9t3D8Ca8NxcXKrIpEHjTXK5J2nXLdP+ALI1cgv8wj7KuwBhTwBkOZSJKM5XQ=="
  dependencies:
    "@types/glob" "^7.1.1"
    globby "^6.1.0"
    is-path-cwd "^2.0.0"
    is-path-in-cwd "^2.0.0"
    p-map "^2.0.0"
    pify "^4.0.1"
    rimraf "^2.6.3"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delegates/-/delegates-1.0.0.tgz"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/detect-libc/-/detect-libc-1.0.3.tgz"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

detect-libc@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/detect-libc/-/detect-libc-2.0.1.tgz"
  integrity "sha1-4Yl6qI+mrRl4YpN/vARB7zUu4M0= sha512-463v3ZeIrcWtdgIg6vI6XUncguvr2TnGl4SzDXinkt9mSLpBJKXT3mW6xT3VQdDN11+WVs29pgvivTc4Lp8v+w=="

detect-node@^2.0.4, detect-node@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/detect-node/-/detect-node-2.1.0.tgz"
  integrity "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE= sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g=="

detective@^5.2.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/detective/-/detective-5.2.0.tgz"
  integrity "sha1-/rKnfoW5BOzepFmtiXzJCpm9Kns= sha512-6SsIx+nUUbuK0EthKjv0zrdnajCCXVYGmbYYiYjFVpzcjwEs/JMDZ8tPRG29J/HhN56t3GJp2cGSWDRjjot8Pg=="
  dependencies:
    acorn-node "^1.6.1"
    defined "^1.0.0"
    minimist "^1.1.1"

dfa@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/dfa/-/dfa-1.2.0.tgz"
  integrity "sha1-lqwyBOLSnEnqW1evjZLCrhJ5Blc= sha512-ED3jP8saaweFTjeGX8HQPjeC1YYyZs98jGNZx6IiBvxW7JG5v492kamAQB3m2wop07CvU/RQmzcKr6bgcC5D/Q=="

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity "sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc= sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw=="

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8= sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA=="
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/dlv/-/dlv-1.1.3.tgz"
  integrity "sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk= sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA=="

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity "sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI= sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA=="
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dom7@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/dom7/-/dom7-3.0.0.tgz"
  integrity "sha1-uGHOXWemvs16qjrQKUL/FLEkAzE= sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g=="
  dependencies:
    ssr-window "^3.0.0-alpha.1"

ejs@^3.1.6:
  version "3.1.8"
  resolved "https://registry.yarnpkg.com/ejs/-/ejs-3.1.8.tgz"
  integrity "sha1-dY0ykQx4BHWFx+8fkvnuBBwcGQs= sha512-/sXZeMlhS0ArkfX2Aw780gJzXSMPnKjtspYZv+f3NiKLlubezAHDU5+9xz6gd3/NhG3txQCo6xlglmTS+oTGEQ=="
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.4.118:
  version "1.4.137"
  resolved "https://registry.yarnpkg.com/electron-to-chromium/-/electron-to-chromium-1.4.137.tgz"
  integrity "sha1-GGGApFYXKD8cASKERYUQzZnWeH8= sha512-0Rcpald12O11BUogJagX3HsCN3FE83DSqWjgXoHo5a72KUKMSfI39XBgJpgNNxS9fuGzytaFjE06kZkiVFy2qA=="

elementtree@^0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/elementtree/-/elementtree-0.1.7.tgz"
  integrity sha512-wkgGT6kugeQk/P6VZ/f4T+4HB41BVgNBq5CDIZVbQ02nvTVqAiVTbskxxu3eA/X96lMlfYOwnLQpN2v5E1zDEg==
  dependencies:
    sax "1.1.4"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.yarnpkg.com/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc= sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity "sha1-VXBmIEatKeLpFucariYKvf9Pang= sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q=="

end-of-stream@^1.1.0, end-of-stream@^1.4.1:
  version "1.4.4"
  resolved "https://registry.yarnpkg.com/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA= sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  dependencies:
    once "^1.4.0"

env-paths@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz"
  integrity sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/error-ex/-/error-ex-1.3.2.tgz"
  integrity "sha1-tKxAZIEH/c3PriQvQovqihTU8b8= sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g=="
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.0.7"
  resolved "https://registry.yarnpkg.com/error-stack-parser/-/error-stack-parser-2.0.7.tgz"
  integrity "sha1-sMbizifQSVz3itmHFeDK0SGau1c= sha512-chLOW0ZGRf4s8raLrDxa5sdkvPec5YdvwbFnqJme4rk0rFajP8mPtrDL1+I+CwrQDCjswDA5sREX7jYQDQs9vA=="
  dependencies:
    stackframe "^1.1.1"

es-abstract@^1.19.0, es-abstract@^1.19.1, es-abstract@^1.19.5:
  version "1.20.1"
  resolved "https://registry.yarnpkg.com/es-abstract/-/es-abstract-1.20.1.tgz"
  integrity "sha1-AnKSzW70S9ErGRO4KBFvVHh9GBQ= sha512-WEm2oBhfoI2sImeM4OF2zE2V3BYdSF+KnSi9Sidz51fQHd7+JuF8Xgcj9/0o+OWeIeIS/MiuNnlruQrJf16GQA=="
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.1.1"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    is-callable "^1.2.4"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-weakref "^1.0.2"
    object-inspect "^1.12.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    regexp.prototype.flags "^1.4.3"
    string.prototype.trimend "^1.0.5"
    string.prototype.trimstart "^1.0.5"
    unbox-primitive "^1.0.2"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo= sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA=="
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/escalade/-/escalade-3.1.1.tgz"
  integrity "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA= sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw=="

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ= sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="

escodegen@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/escodegen/-/escodegen-1.2.0.tgz"
  integrity sha1-Cd55Z3kcyVi3+Jot220jRRrzJ+E=
  dependencies:
    esprima "~1.0.4"
    estraverse "~1.5.0"
    esutils "~1.0.0"
  optionalDependencies:
    source-map "~0.1.30"

esprima@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-1.0.4.tgz"
  integrity sha1-n1V+CPw7TSbs6d00+Pv0drYlha0=

estraverse@~1.5.0:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-1.5.1.tgz"
  integrity sha1-hno+jlip+EYYr7bC3bzZFrfLr3E=

estree-walker@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/estree-walker/-/estree-walker-1.0.1.tgz"
  integrity "sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA= sha512-1fMXF3YP4pZZVozF8j/ZLfvnR8NSIljt56UhbZ5PeeDmmGHpgpdwQt7ITlGvYaQukCvuBRMLEiKiYC+oeIg4cg=="

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.3.tgz"
  integrity "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q= sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="

esutils@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-1.0.0.tgz"
  integrity sha1-gVHTWOIMisx/t0XnRywAJf5JZXA=

expand-template@^2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/expand-template/-/expand-template-2.0.3.tgz"
  integrity "sha1-bhSz/O4POmNA7LV9LokYaSBSpHw= sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg=="

extract-files@^9.0.0:
  version "9.0.0"
  resolved "https://registry.yarnpkg.com/extract-files/-/extract-files-9.0.0.tgz#8a7744f2437f81f5ed3250ed9f1550de902fe54a"
  integrity sha512-CvdFfHkC95B4bBBk36hcEmvdR2awOdhhVUYH6S/zrVj3477zven/fJMYg7121h4T1xHZC+tetUpubpAhxwI7hQ==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU= sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="

fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.2.11"
  resolved "https://registry.yarnpkg.com/fast-glob/-/fast-glob-3.2.11.tgz"
  integrity "sha1-oRcq2VzrihbiDKpcXlZIDlEpwdk= sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew=="
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM= sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="

fast-shallow-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fast-shallow-equal/-/fast-shallow-equal-1.0.0.tgz"
  integrity "sha1-1NyvZHJEDc76b4i5jjJR4n8lYos= sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw=="

fastest-stable-stringify@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/fastest-stable-stringify/-/fastest-stable-stringify-2.0.2.tgz"
  integrity "sha1-N1emd09uyN5AxOhuwo6gJBchTHY= sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q=="

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.yarnpkg.com/fastq/-/fastq-1.13.0.tgz"
  integrity "sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw= sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw=="
  dependencies:
    reusify "^1.0.4"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz"
  integrity sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==
  dependencies:
    pend "~1.2.0"

filelist@^1.0.1:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/filelist/-/filelist-1.0.4.tgz"
  integrity "sha1-94l4oelEd1/55i50RCTyFeWDUrU= sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q=="
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.yarnpkg.com/fill-range/-/fill-range-7.0.1.tgz"
  integrity "sha1-GRmmp8df44ssfHflGYU12prN2kA= sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ=="
  dependencies:
    to-regex-range "^5.0.1"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  integrity "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks= sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig=="
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/find-root/-/find-root-1.1.0.tgz"
  integrity "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ= sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng=="

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-4.1.0.tgz"
  integrity "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk= sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw=="
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

follow-redirects@^1.14.4:
  version "1.15.0"
  resolved "https://registry.yarnpkg.com/follow-redirects/-/follow-redirects-1.15.0.tgz"
  integrity "sha1-BkQYaCgchtDdpK2L2urS0C3KidQ= sha512-aExlJShTV4qOUOL7yF1U5tvLCB0xQuudbf6toyYA0E/acBNw71mvjFTnLaRp50aQaYocMR0a/RMMBIHeZnGyjQ=="

form-data@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-3.0.1.tgz#ebd53791b78356a99af9a300d4282c4d5eb9755f"
  integrity sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fraction.js@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/fraction.js/-/fraction.js-4.2.0.tgz"
  integrity "sha1-RI5RCaMTo1J/WjqyEZ7Ezw4OKVA= sha512-MhLuK+2gUcnZe8ZHlaaINnQLl0xRIGRfcGk2yl8xoQAfHrSsL3rYu6FCmBdkdbhc9EPlwyGHewaRsvwRMJtAlA=="

framer-motion@^6.0.0:
  version "6.3.3"
  resolved "https://registry.yarnpkg.com/framer-motion/-/framer-motion-6.3.3.tgz"
  integrity "sha1-WJeP5pVdEwADUepME0ybwFJjiWc= sha512-wo0dCnoq5vn4L8YVOPO9W54dliH78vDaX0Lj+bSPUys6Nt5QaehrS3uaYa0q5eVeikUgtTjz070UhQ94thI5Sw=="
  dependencies:
    framesync "6.0.1"
    hey-listen "^1.0.8"
    popmotion "11.0.3"
    style-value-types "5.0.0"
    tslib "^2.1.0"
  optionalDependencies:
    "@emotion/is-prop-valid" "^0.8.2"

framesync@6.0.1:
  version "6.0.1"
  resolved "https://registry.yarnpkg.com/framesync/-/framesync-6.0.1.tgz"
  integrity "sha1-XjL8AfHEKznGVMNbFkQOB6JdbyA= sha512-fUY88kXvGiIItgNC7wcTOl0SNRCVXMKSWW2Yzfmn7EKNc+MpCzcz9DhdHcdjbrtN3c6R4H5dTY2jiCpPdysEjA=="
  dependencies:
    tslib "^2.1.0"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs-constants/-/fs-constants-1.0.0.tgz"
  integrity "sha1-a+Dem+mYzhavivwkSXue6bfM2a0= sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow=="

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity "sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8= sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ=="
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^9.0.0, fs-extra@^9.0.1:
  version "9.1.0"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-9.1.0.tgz"
  integrity "sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0= sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ=="
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz"
  integrity "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0= sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "https://registry.yarnpkg.com/function.prototype.name/-/function.prototype.name-1.1.5.tgz"
  integrity "sha1-zOBQX+H/uAUD5vnkbMZORqEqliE= sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA=="
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functions-have-names@^1.2.2:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ= sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="

gauge@~2.7.3:
  version "2.7.4"
  resolved "https://registry.yarnpkg.com/gauge/-/gauge-2.7.4.tgz"
  integrity sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.yarnpkg.com/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA= sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/get-intrinsic/-/get-intrinsic-1.1.1.tgz"
  integrity "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y= sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q=="
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"
  integrity "sha1-tf3nfyLL4185C04ImSLFC85u9mQ= sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g=="

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  integrity "sha1-f9uByQAQH71WTdXxowr1qtweWNY= sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw=="
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

github-from-package@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/github-from-package/-/github-from-package-0.0.0.tgz"
  integrity sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ= sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://registry.yarnpkg.com/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM= sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  dependencies:
    is-glob "^4.0.3"

glob@^7.0.3, glob@^7.1.3, glob@^7.1.6, glob@^7.1.7:
  version "7.2.3"
  resolved "https://registry.yarnpkg.com/glob/-/glob-7.2.3.tgz"
  integrity "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys= sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-11.12.0.tgz"
  integrity "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4= sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA=="

globby@^11.0.4:
  version "11.1.0"
  resolved "https://registry.yarnpkg.com/globby/-/globby-11.1.0.tgz"
  integrity "sha1-vUvpi7BC+D15b344EZkfvoKg00s= sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g=="
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globby@^6.1.0:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/globby/-/globby-6.1.0.tgz"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.10"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.10.tgz"
  integrity "sha1-FH06AG2kyjzhRyjHrvwofDZ9emw= sha512-9ByhssR2fPVsNZj478qUUbKfmL0+t5BDVyjShtyZZLiK7ZDAArFFfopyOTj0M05wE2tJPisA4iTnnXl2YoPvOA=="

graphql-request@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/graphql-request/-/graphql-request-5.0.0.tgz#7504a807d0e11be11a3c448e900f0cc316aa18ef"
  integrity sha512-SpVEnIo2J5k2+Zf76cUkdvIRaq5FMZvGQYnA4lUWYbc99m+fHh4CZYRRO/Ff4tCLQ613fzCm3SiDT64ubW5Gyw==
  dependencies:
    "@graphql-typed-document-node/core" "^3.1.1"
    cross-fetch "^3.1.5"
    extract-files "^9.0.0"
    form-data "^3.0.0"

graphql@^16.6.0:
  version "16.6.0"
  resolved "https://registry.yarnpkg.com/graphql/-/graphql-16.6.0.tgz#c2dcffa4649db149f6282af726c8c83f1c7c5fdb"
  integrity sha512-KPIBPDlW7NxrbT/eh4qPXz5FiFdL5UbaA0XUNz2Rp3Z3hqBSkbj0GVjwFDztsWVauZUWsbKHgMg++sk8UX0bkw==

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/has-bigints/-/has-bigints-1.0.2.tgz"
  integrity "sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo= sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ=="

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-4.0.0.tgz"
  integrity "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s= sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
  integrity "sha1-YQcIYAYG02lh7QTBlhk7amB/qGE= sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ=="
  dependencies:
    get-intrinsic "^1.1.1"

has-symbols@^1.0.1, has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg= sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  integrity "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU= sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ=="
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/has/-/has-1.0.3.tgz"
  integrity "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y= sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw=="
  dependencies:
    function-bind "^1.1.1"

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/hex-color-regex/-/hex-color-regex-1.1.0.tgz"
  integrity "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4= sha512-l9sfDFsuqtOqKDsQdqrMRk0U85RZc0RtOR9yPI7mRVOa4FsR/BVnZ0shmQRM96Ji99kYZP/7hn1cedc1+ApsTQ=="

hey-listen@^1.0.8:
  version "1.0.8"
  resolved "https://registry.yarnpkg.com/hey-listen/-/hey-listen-1.0.8.tgz"
  integrity "sha1-jllWH/ckkI3hqpJO1uzISlapqmg= sha512-COpmrF2NOg4TBWUJ5UVyaCU2A88wEMkUPK4hNqyCkqHbxT92BbvfjoSozkAIIm6XhicGlJHhFdullInrdhwU8Q=="

hoist-non-react-statics@^3.2.0, hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "https://registry.yarnpkg.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity "sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U= sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw=="
  dependencies:
    react-is "^16.7.0"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/hsl-regex/-/hsl-regex-1.0.0.tgz"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsl-to-hex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/hsl-to-hex/-/hsl-to-hex-1.0.0.tgz"
  integrity sha1-xYyCbcbS8eCl/x2lp+y/A/qsE1I=
  dependencies:
    hsl-to-rgb-for-reals "^1.1.0"

hsl-to-rgb-for-reals@^1.1.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/hsl-to-rgb-for-reals/-/hsl-to-rgb-for-reals-1.1.1.tgz"
  integrity "sha1-4esj9reAFuNyJDHfaBl+bc3AFtk= sha512-LgOWAkrN0rFaQpfdWBQlv/VhkOxb5AsBjk6NQVx4yEzWS923T07X0M1Y0VNko2H52HeSpZrZNNMJ0aFqsdVzQg=="

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/hsla-regex/-/hsla-regex-1.0.0.tgz"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-escaper@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity "sha1-39YAJ9o2o238viNiYsAKWCJoFFM= sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg=="

html-parse-stringify@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz"
  integrity "sha1-38EBc0fOn3fIFBpQfyMwQMWcVdI= sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg=="
  dependencies:
    void-elements "3.1.0"

html-tags@^3.1.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/html-tags/-/html-tags-3.2.0.tgz"
  integrity "sha1-27NRjSC3JlJOTdQ945frCpVyaWE= sha512-vy7ClnArOZwCnqZgvv+ddgHgJiAFXe3Ge9ML5/mBctVJoUoYPCdxVucOywjDARn6CVoh3dRSFdPHy2sX80L0Wg=="

husky@^7.0.4:
  version "7.0.4"
  resolved "https://registry.yarnpkg.com/husky/-/husky-7.0.4.tgz"
  integrity "sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU= sha512-vbaCKN2QLtP/vD4yvs6iz6hBEo6wkSzs8HpRah1Z6aGmF2KW5PdYuAd7uX5a+OyBZHBhd+TFLqgjUgytQr4RvQ=="

hyphen@^1.6.4:
  version "1.6.4"
  resolved "https://registry.yarnpkg.com/hyphen/-/hyphen-1.6.4.tgz"
  integrity "sha1-mFlnj+GvJ5O0nn1GboX4W1aYkX4= sha512-nWwvXceFMAFIjkiRzqZMZSOa1LVngieSolnYIVKWSwmDwMSmdutjzqImmdbxe2eUCfX693fgrCgtPjbllqx1lA=="

hyphenate-style-name@^1.0.2:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/hyphenate-style-name/-/hyphenate-style-name-1.0.4.tgz"
  integrity "sha1-aRh5r44iCupXUOiCfbTvYqVONh0= sha512-ygGZLjmXfPHj+ZWh6LwbC37l43MhfztxetbFCoYTM2VjkIUpeHgSNn7QIyVFj7YQ1Wl9Cbw5sholVJPzWvC2MQ=="

i18next-fs-backend@^1.0.7:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/i18next-fs-backend/-/i18next-fs-backend-1.1.4.tgz"
  integrity "sha1-0Om57S+noPEQAtgrn6acPD1kgto= sha512-/MfAGMP0jHonV966uFf9PkWWuDjPYLIcsipnSO3NxpNtAgRUKLTwvm85fEmsF6hGeu0zbZiCQ3W74jwO6K9uXA=="

i18next@^20.1.0:
  version "20.6.1"
  resolved "https://registry.yarnpkg.com/i18next/-/i18next-20.6.1.tgz"
  integrity "sha1-U15fbluutoXH0l33DbY788wKo0U= sha512-yCMYTMEJ9ihCwEQQ3phLo7I/Pwycf8uAx+sRHwwk5U9Aui/IZYgQRyMqXafQOw5QQ7DM1Z+WyEXWIqSuJHhG2A=="
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

idb@^6.1.4:
  version "6.1.5"
  resolved "https://registry.yarnpkg.com/idb/-/idb-6.1.5.tgz"
  integrity "sha1-28U+et8ax8Wfmyv1bgC06k/OjHs= sha512-IJtugpKkiVXQn5Y+LteyBCNk1N8xpGV3wWZk9EVtZWH8DYkjBn0bX1XnGP9RkyZF0sAcywa6unHqSWKe7q4LGw=="

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.2.1.tgz"
  integrity "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I= sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA=="

ignore@^5.2.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/ignore/-/ignore-5.2.0.tgz"
  integrity "sha1-bTusj6f+DUXZ+b57rC/CeVd+NFo= sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ=="

import-fresh@^3.1.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity "sha1-NxYsJfy566oublPVtNiM4X2eDCs= sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw=="
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz"
  integrity "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w= sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="

ini@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/ini/-/ini-3.0.1.tgz"
  integrity sha512-it4HyVAUTKBc6m8e1iXWvXSTdndF7HbdN713+kvLrymxTaU4AUBWrJ4vEooP+V7fexnVD3LKcBshjGGPefSMUQ==

ini@~1.3.0:
  version "1.3.8"
  resolved "https://registry.yarnpkg.com/ini/-/ini-1.3.8.tgz"
  integrity "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw= sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew=="

inline-style-prefixer@^6.0.0:
  version "6.0.1"
  resolved "https://registry.yarnpkg.com/inline-style-prefixer/-/inline-style-prefixer-6.0.1.tgz"
  integrity "sha1-xcDkO6iDFwevxfW7/Zft9Fwfp64= sha512-AsqazZ8KcRzJ9YPN1wMH2aNM7lkWQ8tSPrW5uDk1ziYwiAPWSZnUsC7lfZq+BDqLqz0B4Pho5wscWcJzVvRzDQ=="
  dependencies:
    css-in-js-utils "^2.0.0"

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/internal-slot/-/internal-slot-1.0.3.tgz"
  integrity "sha1-c0fjB97uovqsKsYgXUvH00ln9Zw= sha512-O0DB1JC/sPyZl7cIo78n5dR7eUSwwpYPiXRhTzNxZVAMUuB8vlnRFyLxdrVToks6XPLVnFfbzaVd5WLjhgg+vA=="
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/is-arguments/-/is-arguments-1.1.1.tgz"
  integrity "sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps= sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA=="
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM= sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ=="

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/is-bigint/-/is-bigint-1.0.4.tgz"
  integrity "sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM= sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg=="
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk= sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  integrity "sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk= sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA=="
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.4, is-callable@^1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/is-callable/-/is-callable-1.2.4.tgz"
  integrity "sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU= sha512-nsuwtxZfMX67Oryl9LCQ+upnC0Z0BgpwntpS89m1H/TLF0zNfzfLMV/9Wa/6MZsj0acpEjAO0KF1xT6ZdLl95w=="

is-color-stop@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/is-color-stop/-/is-color-stop-1.1.0.tgz"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-core-module@^2.8.1:
  version "2.9.0"
  resolved "https://registry.yarnpkg.com/is-core-module/-/is-core-module-2.9.0.tgz"
  integrity "sha1-4cNEKc1Rxt2eCeB5njluJ7GanGk= sha512-+5FPy5PnwmO3lvfMb0AsoPaBG+5KHUI0wYFXOtYPnVVVspTFUuMZNfNaNVRt3FZadstu2c8x23vykRW/NBoU6A=="
  dependencies:
    has "^1.0.3"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity "sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8= sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ=="
  dependencies:
    has-tostringtag "^1.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.yarnpkg.com/is-glob/-/is-glob-4.0.3.tgz"
  integrity "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ= sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  dependencies:
    is-extglob "^2.1.1"

is-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-module/-/is-module-1.0.0.tgz"
  integrity sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  integrity "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA= sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA=="

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/is-number-object/-/is-number-object-1.0.7.tgz"
  integrity "sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw= sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ=="
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/is-number/-/is-number-7.0.0.tgz"
  integrity "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss= sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="

is-obj@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/is-obj/-/is-obj-1.0.1.tgz"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-path-cwd@^2.0.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/is-path-cwd/-/is-path-cwd-2.2.0.tgz"
  integrity "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s= sha512-w942bTcih8fdJPJmQHFzkS76NEP8Kzzvmw92cXsazb8intwLqPibPPdXf4ANdKV3rYMuuQYGIWtvz9JilB3NFQ=="

is-path-in-cwd@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz"
  integrity "sha1-v+Lcomxp85cmWkAJljYCk1oFOss= sha512-rNocXHgipO+rvnP6dk3zI20RpOtrAM/kzbB258Uw5BWr3TpXi861yzjo16Dn4hUox07iw5AyeMLHWsujkjzvRQ=="
  dependencies:
    is-path-inside "^2.1.0"

is-path-inside@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-path-inside/-/is-path-inside-2.1.0.tgz"
  integrity "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI= sha512-wiyhTzfDWsvwAW53OBWF5zuvaOGlZ6PwYxAbPVDhpm+gM09xKQGjBq/8uYN12aDvMxnAnq3dxTyoSoRNmg5YFg=="
  dependencies:
    path-is-inside "^1.0.2"

is-regex@^1.0.4, is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.yarnpkg.com/is-regex/-/is-regex-1.1.4.tgz"
  integrity "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg= sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg=="
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-regexp/-/is-regexp-1.0.0.tgz"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  integrity "sha1-jyWcVztgtqMtQFihoHQwwKc0THk= sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA=="
  dependencies:
    call-bind "^1.0.2"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/is-stream/-/is-stream-2.0.1.tgz"
  integrity "sha1-+sHj1TuXrVqdCunO8jifWBClwHc= sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg=="

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/is-string/-/is-string-1.0.7.tgz"
  integrity "sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0= sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg=="
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/is-symbol/-/is-symbol-1.0.4.tgz"
  integrity "sha1-ptrJO2NbBjymhyI23oiRClevE5w= sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg=="
  dependencies:
    has-symbols "^1.0.2"

is-url@^1.2.4:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/is-url/-/is-url-1.2.4.tgz"
  integrity "sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI= sha512-ITvGim8FhRiYe4IQ5uHSkj7pVaPDrCTkNd3yq3cV7iZAcJdHTUMPMEHcqSOy9xZ9qFenQCvi+2wjH9a1nXqHww=="

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/is-weakref/-/is-weakref-1.0.2.tgz"
  integrity "sha1-lSnzg6kzggXol2XgOS78LxAPBvI= sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ=="
  dependencies:
    call-bind "^1.0.2"

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/isobject/-/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

jake@^10.8.5:
  version "10.8.5"
  resolved "https://registry.yarnpkg.com/jake/-/jake-10.8.5.tgz"
  integrity "sha1-8hg9LFk4LLJ0ImA0VDucA7gWTEY= sha512-sVpxYeuAhWt0OTWITwT98oyV0GsXyMlXCF+3L1SuafBVUIr/uILGRB+NqwkzhgXKvoJpDIpQvqkUALgdmQsQxw=="
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.1"
    minimatch "^3.0.4"

jest-worker@^26.2.1:
  version "26.6.2"
  resolved "https://registry.yarnpkg.com/jest-worker/-/jest-worker-26.6.2.tgz"
  integrity "sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0= sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ=="
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://registry.yarnpkg.com/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity "sha1-jRRvCQDolzsQa29zzB6ajLhvjbA= sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg=="
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

js-cookie@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/js-cookie/-/js-cookie-2.2.1.tgz"
  integrity "sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg= sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ=="

js-cookie@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/js-cookie/-/js-cookie-3.0.1.tgz"
  integrity "sha1-njm0xsL1ZWNwjX0x9vXyGHOpJBQ= sha512-+0rgsUXZu4ncpPxRL+lNEptWMOWl9etvPHc/koSRp6MPwpRYAhmk0dUG00J4bxVV3r9uUzfo24wW0knS07SKSw=="

js-sha3@0.8.0:
  version "0.8.0"
  resolved "https://registry.yarnpkg.com/js-sha3/-/js-sha3-0.8.0.tgz"
  integrity "sha1-ubel2nOvrX3t0PjEY5VMveaBiEA= sha512-gF1cRrHhIzNfToc802P800N8PpXS+evLLXfsVpowqmAFR9uwbi89WvXg2QspOmXL8QL86J4T1EpFu+yUkwJY3Q=="

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity "sha1-GSA/tZmR35jjoocFDUZHzerzJJk= sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="

jsbarcode@^3.8.0:
  version "3.11.6"
  resolved "https://registry.yarnpkg.com/jsbarcode/-/jsbarcode-3.11.6.tgz#96e8fbc3395476e162982a6064b98a09b5ea02c0"
  integrity sha512-G5TKGyKY1zJo0ZQKFM1IIMfy0nF2rs92BLlCz+cU4/TazIc4ZH+X1GYeDRt7TKjrYqmPfTjwTBkU/QnQlsYiuA==

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-2.5.2.tgz"
  integrity "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q= sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA=="

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.yarnpkg.com/jsesc/-/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0= sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w=="

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity "sha1-afaofZUTq4u4/mO9sJecRI5oRmA= sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI= sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug=="

json-schema@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/json-schema/-/json-schema-0.4.0.tgz"
  integrity "sha1-995M9u+rg4666zI2R0y7paGTCrU= sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="

json5@^2.1.2, json5@^2.2.0, json5@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/json5/-/json5-2.2.1.tgz"
  integrity "sha1-ZV1Q7R5vla0aPKq6vSsO/aELOVw= sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA=="

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4= sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ=="
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonp@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/jsonp/-/jsonp-0.2.1.tgz"
  integrity sha1-pltPoPEL2nGaBUQep7lMVfPhW64=
  dependencies:
    debug "^2.1.3"

jsonpointer@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/jsonpointer/-/jsonpointer-5.0.0.tgz"
  integrity "sha1-+AJmmlJOxIBfpzierbyZIdXcgHI= sha512-PNYZIdMjVIvVgDSYKTT63Y+KZ6IZvGRNNWcxwD+GNnUz1MKPfv30J8ueCjdwcN0nDx2SlshgyB7Oy0epAzVRRg=="

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

kleur@^4.1.4:
  version "4.1.5"
  resolved "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz"
  integrity sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/leven/-/leven-3.1.0.tgz"
  integrity "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I= sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A=="

lilconfig@^2.0.5:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/lilconfig/-/lilconfig-2.0.5.tgz"
  integrity "sha1-GeV/0GzMOEj9GJFlW1pEcJIiWyU= sha512-xaYmXZtTHPAw5m+xLN8ab9C+3a8YmV3asNSPOATITbtwrfbwaLJj8h66H1WMIpALCkqsIzK3h7oQ+PdX+LQ9Eg=="

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.yarnpkg.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity "sha1-7KKE910pZQeTCdwK2SVauy68FjI= sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="

load-script@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/load-script/-/load-script-1.0.0.tgz"
  integrity sha1-BJGTngvuVkPuSUp+PaPSuscMbKQ=

loader-utils@^2.0.0:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/loader-utils/-/loader-utils-2.0.2.tgz"
  integrity "sha1-1uO0+4GHByGuTghoqxHdY4NowSk= sha512-TM57VeHptv569d/GKh6TAYdzKblwDNiumOdkFnejjD0XwTH87K90w3O7AiJRqdQoXygvi1VQTJTLGhJl7WqA7A=="
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-5.0.0.tgz"
  integrity "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA= sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g=="
  dependencies:
    p-locate "^4.1.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity "sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4= sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw=="

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://registry.yarnpkg.com/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/lodash.throttle/-/lodash.throttle-4.1.1.tgz"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.topath@^4.5.2:
  version "4.5.2"
  resolved "https://registry.yarnpkg.com/lodash.topath/-/lodash.topath-4.5.2.tgz"
  integrity sha1-NhY1Hzu6YZlKCTGYlmC9AyVP0Ak=

lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.21.tgz"
  integrity "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw= sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8= sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q=="
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ= sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA=="
  dependencies:
    yallist "^4.0.0"

magic-string@^0.25.0, magic-string@^0.25.7:
  version "0.25.9"
  resolved "https://registry.yarnpkg.com/magic-string/-/magic-string-0.25.9.tgz"
  integrity "sha1-3n+fr5HvihyR0CwuUxTIJ3283Rw= sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ=="
  dependencies:
    sourcemap-codec "^1.4.8"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/make-dir/-/make-dir-3.1.0.tgz"
  integrity "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8= sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw=="
  dependencies:
    semver "^6.0.0"

map-obj@^4.1.0:
  version "4.3.0"
  resolved "https://registry.yarnpkg.com/map-obj/-/map-obj-4.3.0.tgz"
  integrity "sha1-kwT5Buk/qucIgNoQKp8d8OqLsFo= sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ=="

match-sorter@^6.0.2:
  version "6.3.1"
  resolved "https://registry.yarnpkg.com/match-sorter/-/match-sorter-6.3.1.tgz"
  integrity "sha1-mMw3/adWCTQk3fPLxiv+nHW5K9o= sha512-mxybbo3pPNuA+ZuCUhm5bwNkXrJTbsk5VWbR5wiwz/GC6LIiegBGn2w3O08UG/jdbYLinw51fSQ5xNU1U3MgBw=="
  dependencies:
    "@babel/runtime" "^7.12.5"
    remove-accents "0.4.2"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.yarnpkg.com/mdn-data/-/mdn-data-2.0.14.tgz"
  integrity "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA= sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow=="

media-engine@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/media-engine/-/media-engine-1.0.3.tgz"
  integrity sha1-vjGI9s0kPqKkCASjXeWlsDL1ja0=

memoize-one@^5.0.0, memoize-one@^5.1.1:
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/memoize-one/-/memoize-one-5.2.1.tgz"
  integrity "sha1-gzeqPEM1WBg57AHD1ZQJDOvo8A4= sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q=="

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A= sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w=="

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/merge2/-/merge2-1.4.1.tgz"
  integrity "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4= sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg=="

micromatch@^4.0.4:
  version "4.0.5"
  resolved "https://registry.yarnpkg.com/micromatch/-/micromatch-4.0.5.tgz"
  integrity "sha1-vImZp8u/d83InxMvbkZwUbSQkMY= sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

microseconds@0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/microseconds/-/microseconds-0.2.0.tgz"
  integrity "sha1-Izsl9Qxipl2GH5eKSk+OwYeX3Dk= sha512-n7DHHMjR1avBbSpsTBj6fmMGh2AGrifVV4e+WYc3Q9lO+xnSZ3NyhcBND3vzzatt05LFhoKFRxrIyklmLlUtyA=="

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.52.0.tgz"
  integrity "sha1-u6vNwChZ9JhzAchW4zh85exDv3A= sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="

mime-types@^2.1.12:
  version "2.1.35"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.35.tgz"
  integrity "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo= sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  dependencies:
    mime-db "1.52.0"

mimic-response@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/mimic-response/-/mimic-response-3.1.0.tgz"
  integrity "sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k= sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ=="

mini-svg-data-uri@^1.2.3:
  version "1.4.4"
  resolved "https://registry.yarnpkg.com/mini-svg-data-uri/-/mini-svg-data-uri-1.4.4.tgz"
  integrity "sha1-irCqvN+MKa1Wk8pZWvGd0urQmTk= sha512-r9deDe9p5FJUPZAk3A59wGH7Ii9YrjjWw0jmw/liSbHl2CHiyXj6FcDXDu2K3TjVAXqiJdaw3xxwlZZr9E6nHg=="

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.1.2.tgz"
  integrity "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s= sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-5.1.0.tgz"
  integrity "sha1-Fxe0ZPSXGxRPaqvo8tC45FEeCcc= sha512-9TPBGGak4nHfGZsPBohm9AWg6NoT7QTCehS3BIJABslyZbzxfV78QM2Y6+i741OPZIafFAaiiEMh5OyIrJPgtg=="
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.3:
  version "1.2.6"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-1.2.6.tgz"
  integrity "sha1-hjelt1nqDW6YcCz7OpKDMjyTr0Q= sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q=="

minipass@^3.0.0:
  version "3.3.4"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.3.4.tgz"
  integrity sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw==
  dependencies:
    yallist "^4.0.0"

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp-classic@^0.5.2, mkdirp-classic@^0.5.3:
  version "0.5.3"
  resolved "https://registry.yarnpkg.com/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz"
  integrity "sha1-+hDJEVzG2IZb4iG6R+6b7XhgERM= sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A=="

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

modern-normalize@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/modern-normalize/-/modern-normalize-1.1.0.tgz"
  integrity "sha1-2o6AFA2SIUJr1PclxuESg9NPkLc= sha512-2lMlY1Yc1+CUy0gw4H95uNN7vjbpoED7NNRSBHE25nWfLBdmMzFCsPshlzbxHz+gYMcBEUN8V4pU16prcdPSgA=="

moment@^2.29.4:
  version "2.29.4"
  resolved "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz"
  integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.1.2.tgz"
  integrity "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk= sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="

mustache@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/mustache/-/mustache-4.2.0.tgz"
  integrity sha512-71ippSywq5Yb7/tVYyGbkBggbU8H3u5Rz56fH60jGFgr8uHwxs+aSKeqmluIVzM0m0kB7xQjKS6qPfd0b2ZoqQ==

nano-css@^5.3.1:
  version "5.3.5"
  resolved "https://registry.yarnpkg.com/nano-css/-/nano-css-5.3.5.tgz"
  integrity "sha1-MHXqKf/esMfLbSXtsh2Pf6jo/o4= sha512-vSB9X12bbNu4ALBu7nigJgRViZ6ja3OU7CeuiV1zMIbXOdmkLahgtPmh3GBOlDxbKY0CitqlPdOReGlBLSp+yg=="
  dependencies:
    css-tree "^1.1.2"
    csstype "^3.0.6"
    fastest-stable-stringify "^2.0.2"
    inline-style-prefixer "^6.0.0"
    rtl-css-js "^1.14.0"
    sourcemap-codec "^1.4.8"
    stacktrace-js "^2.0.2"
    stylis "^4.0.6"

nano-time@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/nano-time/-/nano-time-1.0.0.tgz"
  integrity sha1-sFVPaa2J4i0JB/ehKwmTpdlhN+8=
  dependencies:
    big-integer "^1.6.16"

nanoclone@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/nanoclone/-/nanoclone-0.2.1.tgz"
  integrity "sha1-3UCQ+PGhENJrsyxJ7S9bkjUgntQ= sha512-wynEP02LmIbLpcYw8uBKpcfF6dmg2vcpKqxeH5UcoKEYdExslsdUA4ugFauuaeYdTB76ez6gJW8XAZ6CgkXYxA=="

nanoid@^3.3.4:
  version "3.3.4"
  resolved "https://registry.yarnpkg.com/nanoid/-/nanoid-3.3.4.tgz"
  integrity "sha1-cwtn480J4t6s8DwCfIHJ2dvF6Ks= sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw=="

napi-build-utils@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/napi-build-utils/-/napi-build-utils-1.0.2.tgz"
  integrity "sha1-sf3cCyxG44Cgt6dvmE3UfEGhOAY= sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg=="

native-run@^1.6.0:
  version "1.7.1"
  resolved "https://registry.npmjs.org/native-run/-/native-run-1.7.1.tgz"
  integrity sha512-70ZneVVcOL1ifqw7SG5O2AJYIHEBSX5C25ShwwKCcdMcgbZ+MzvAc2fjHzfekcPYtInHqcJQOki6NXj9f6LgOg==
  dependencies:
    "@ionic/utils-fs" "^3.1.6"
    "@ionic/utils-terminal" "^2.3.3"
    bplist-parser "^0.3.2"
    debug "^4.3.4"
    elementtree "^0.1.7"
    ini "^3.0.1"
    plist "^3.0.6"
    split2 "^4.1.0"
    through2 "^4.0.2"
    tslib "^2.4.0"
    yauzl "^2.10.0"

next-compose-plugins@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/next-compose-plugins/-/next-compose-plugins-2.2.1.tgz#020fc53f275a7e719d62521bef4300fbb6fde5ab"
  integrity sha512-OjJ+fV15FXO2uQXQagLD4C0abYErBjyjE0I0FHpOEIB8upw0hg1ldFP6cqHTJBH1cZqy96OeR3u1dJ+Ez2D4Bg==

next-export-i18n@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/next-export-i18n/-/next-export-i18n-2.1.0.tgz"
  integrity sha512-u5AVBG233Ev4hYJtFM7d6wD2TKe2oDWmRtgV9A6F6RUXkBjkDnhLWO1O7SHrAA31bZ5qzRg/ePhDWGNxCcXufQ==
  dependencies:
    mustache "^4.2.0"

next-i18next@^8.9.0:
  version "8.10.0"
  resolved "https://registry.yarnpkg.com/next-i18next/-/next-i18next-8.10.0.tgz"
  integrity "sha1-lEQNeW18PRanQhisct8pba8TqqM= sha512-nY3tw+uU6lzZq7YfqRr0lFBiz15txIeYBX5R6rVeAK8wWFsCRJpZ6lTm02DMC+MfD1G6LmtpR6bmOOaBD3TR9A=="
  dependencies:
    "@babel/runtime" "^7.13.17"
    "@types/hoist-non-react-statics" "^3.3.1"
    core-js "^3"
    hoist-non-react-statics "^3.2.0"
    i18next "^20.1.0"
    i18next-fs-backend "^1.0.7"
    react-i18next "^11.8.13"

next-pwa@^5.4.0:
  version "5.5.2"
  resolved "https://registry.yarnpkg.com/next-pwa/-/next-pwa-5.5.2.tgz"
  integrity "sha1-pZLEkZUAGotdIeDozxGQ9Notwo0= sha512-NOZxIS/4Qa4lsPG99CNh3ZA1vfVJ3vpZjBvfouXOfWn0K9CLjBRZwGkJAcWsMWngSGoTN1hUkg97Pe+9xESzWQ=="
  dependencies:
    babel-loader "^8.2.4"
    clean-webpack-plugin "^4.0.0"
    globby "^11.0.4"
    terser-webpack-plugin "^5.3.1"
    workbox-webpack-plugin "^6.5.2"
    workbox-window "^6.5.2"

next-seo@^4.28.1:
  version "4.29.0"
  resolved "https://registry.yarnpkg.com/next-seo/-/next-seo-4.29.0.tgz"
  integrity "sha1-0oHpW6R5FBF8yZ6eRoWZ8FR9m5s= sha512-xmwzcz4uHaYJ8glbuhs6FSBQ7z3irmdPYdJJ5saWm72Uy3o+mPKGaPCXQetTCE6/xxVnpoDV4yFtFlEjUcljSg=="

next@^12.3.1:
  version "12.3.1"
  resolved "https://registry.npmjs.org/next/-/next-12.3.1.tgz"
  integrity sha512-l7bvmSeIwX5lp07WtIiP9u2ytZMv7jIeB8iacR28PuUEFG5j0HGAPnMqyG5kbZNBG2H7tRsrQ4HCjuMOPnANZw==
  dependencies:
    "@next/env" "12.3.1"
    "@swc/helpers" "0.4.11"
    caniuse-lite "^1.0.30001406"
    postcss "8.4.14"
    styled-jsx "5.0.7"
    use-sync-external-store "1.2.0"
  optionalDependencies:
    "@next/swc-android-arm-eabi" "12.3.1"
    "@next/swc-android-arm64" "12.3.1"
    "@next/swc-darwin-arm64" "12.3.1"
    "@next/swc-darwin-x64" "12.3.1"
    "@next/swc-freebsd-x64" "12.3.1"
    "@next/swc-linux-arm-gnueabihf" "12.3.1"
    "@next/swc-linux-arm64-gnu" "12.3.1"
    "@next/swc-linux-arm64-musl" "12.3.1"
    "@next/swc-linux-x64-gnu" "12.3.1"
    "@next/swc-linux-x64-musl" "12.3.1"
    "@next/swc-win32-arm64-msvc" "12.3.1"
    "@next/swc-win32-ia32-msvc" "12.3.1"
    "@next/swc-win32-x64-msvc" "12.3.1"

node-abi@^3.3.0:
  version "3.15.0"
  resolved "https://registry.yarnpkg.com/node-abi/-/node-abi-3.15.0.tgz"
  integrity "sha1-zZrIxYMoEptJmYzG+haqVQYVJxY= sha512-Ic6z/j6I9RLm4ov7npo1I48UQr2BEyFCqh6p7S1dhEx9jPO0GPGq/e2Rb7x7DroQrmiVMz/Bw1vJm9sPAl2nxA=="
  dependencies:
    semver "^7.3.5"

node-addon-api@^4.2.0:
  version "4.3.0"
  resolved "https://registry.yarnpkg.com/node-addon-api/-/node-addon-api-4.3.0.tgz"
  integrity "sha1-UqGgtHUZPgko6Y4EJqDRJUeCt38= sha512-73sE9+3UaLYYFmDsFZnqCInzPyh3MqIwZO9cw58yIqAZhONrrabrYyYe3TuIqtIiOuTXVhsGau8hcrhhwSsDIQ=="

node-emoji@^1.11.0:
  version "1.11.0"
  resolved "https://registry.yarnpkg.com/node-emoji/-/node-emoji-1.11.0.tgz"
  integrity "sha1-aaAVDmlG4vEV6dfqTfeXHiYoMBw= sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A=="
  dependencies:
    lodash "^4.17.21"

node-fetch@2.6.7:
  version "2.6.7"
  resolved "https://registry.yarnpkg.com/node-fetch/-/node-fetch-2.6.7.tgz"
  integrity "sha1-JN6fuoJ+O0rkTciyAlajeRYAUq0= sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ=="
  dependencies:
    whatwg-url "^5.0.0"

node-releases@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/node-releases/-/node-releases-2.0.4.tgz"
  integrity "sha1-84JSNwxDhU3EiqQxx2bGw5j0BHY= sha512-gbMzqQtTtDz/00jQzZ21PQzdI9PyLYqUSvD0p3naOhX4odFji0ZxYdnVwPTxmSwkmxhcFImpozceidSG+AgoPQ=="

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU= sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-svg-path@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/normalize-svg-path/-/normalize-svg-path-1.1.0.tgz"
  integrity "sha1-DmFOyiPDnwz/6CHWvmzRflaadmw= sha512-r9KHKG2UUeB5LoTouwDzBy2VxXlHsiM6fyLQvnJa0S5hrhzqElH/CH7TUGhT1fVvIYBIKf3OpY4YJ4CK+iaqHg=="
  dependencies:
    svg-arc-to-cubic-bezier "^3.0.0"

npmlog@^4.0.1:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/npmlog/-/npmlog-4.1.2.tgz"
  integrity "sha1-CKfyqL9zRgR3mp76StXMcXq7lUs= sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg=="
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.3"
    set-blocking "~2.0.0"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/number-is-nan/-/number-is-nan-1.0.1.tgz"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/object-hash/-/object-hash-2.2.0.tgz"
  integrity "sha1-WtUYWB7vxEO9djRyuP8unCwNVKU= sha512-gScRMn0bS5fH+IuwyIFgnh9zBdo4DV+6GhygmWM9HyNJSgS0hScp1f5vjtm7oIIOiT9trXrShAkLFSc2IqKNgw=="

object-inspect@^1.12.0, object-inspect@^1.9.0:
  version "1.12.1"
  resolved "https://registry.yarnpkg.com/object-inspect/-/object-inspect-1.12.1.tgz"
  integrity "sha1-KKZhFTutfkcOSwFHnvHLkc5REZE= sha512-Y/jF6vnvEtOPGiKD1+q+X0CiUYRQtEHp89MLLUJ7TUivtH8Ugn2+3A7Rynqk7BRsAoqeOQWnFnjpDrKSxDgIGA=="

object-is@^1.0.1:
  version "1.1.5"
  resolved "https://registry.yarnpkg.com/object-is/-/object-is-1.1.5.tgz"
  integrity "sha1-ud7qpfx/GEag+uzc7sE45XePU6w= sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw=="
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/object-keys/-/object-keys-1.1.1.tgz"
  integrity "sha1-HEfyct8nfzsdrwYWd9nILiMixg4= sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="

object.assign@^4.1.0, object.assign@^4.1.2:
  version "4.1.2"
  resolved "https://registry.yarnpkg.com/object.assign/-/object.assign-4.1.2.tgz"
  integrity "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA= sha512-ixT2L5THXsApyiUPYKmW+2EHpXXe5Ii3M+f4e+aJFAHao5amFRW6J0OO6c/LU8Be47utCx2GL89hxGB6XSmKuQ=="
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

oblivious-set@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/oblivious-set/-/oblivious-set-1.0.0.tgz"
  integrity "sha1-yDFvLC+2/3sRthWNsyNMSfczxWY= sha512-z+pI07qxo4c2CulUHCDf9lcqDlMSo72N/4rLUpRXf6fu+q8vjt8y0xS+Tlf8NTJDdTXHbdeO1n3MlbctwEoXZw=="

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

open@^8.4.0:
  version "8.4.0"
  resolved "https://registry.npmjs.org/open/-/open-8.4.0.tgz"
  integrity sha512-XgFPPM+B28FtCCgSb9I+s9szOC1vZRSwgWsRUA5ylIxRTgKozqjOCrVOqGsYABPYK5qnfqClxZTFBa8PKt2v6Q==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

overlayscrollbars-react@^0.2.3:
  version "0.2.3"
  resolved "https://registry.yarnpkg.com/overlayscrollbars-react/-/overlayscrollbars-react-0.2.3.tgz"
  integrity "sha1-mjWt6lIYyDZ1t1b58C3716iDUkg= sha512-eN/JsEtJvPulOXOZXIdo1H90eriUWcgj4TwSdOcchk2M4uY2/BpsHlZ2+0viZMLXTcNQNJz+/4m47NugSBg+0g=="

overlayscrollbars@^1.13.1:
  version "1.13.1"
  resolved "https://registry.yarnpkg.com/overlayscrollbars/-/overlayscrollbars-1.13.1.tgz"
  integrity "sha1-C4QKiHN/Q6lGudh4daL55CHQM4o= sha512-gIQfzgGgu1wy80EB4/6DaJGHMEGmizq27xHIESrzXq0Y/J0Ay1P3DWk6tuVmEPIZH15zaBlxeEJOqdJKmowHCQ=="

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-2.3.0.tgz"
  integrity "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE= sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w=="
  dependencies:
    p-try "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-4.1.0.tgz"
  integrity "sha1-o0KLtwiLOmApL2aRkni3wpetTwc= sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A=="
  dependencies:
    p-limit "^2.2.0"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/p-map/-/p-map-2.1.0.tgz"
  integrity "sha1-MQko/u+cnsxltosXaTAYpmXOoXU= sha512-y3b8Kpd8OAN444hxfBbFfj1FY/RjtTd8tzYwhUqNYXx0fXx2iX4maP4Qr6qhIKbQXI02wTLAda4fYUbDagTUFw=="

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/p-try/-/p-try-2.2.0.tgz"
  integrity "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY= sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ=="

pako@^0.2.5:
  version "0.2.9"
  resolved "https://registry.yarnpkg.com/pako/-/pako-0.2.9.tgz"
  integrity sha1-8/dSL073gjSNqBYbrZ7P1Rv4OnU=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/parent-module/-/parent-module-1.0.1.tgz"
  integrity "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI= sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-5.2.0.tgz"
  integrity "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80= sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg=="
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-svg-path@^0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/parse-svg-path/-/parse-svg-path-0.1.2.tgz"
  integrity sha1-en7A0esG+lMlx9PgCbhZoJtdSes=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-4.0.0.tgz"
  integrity "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM= sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/path-is-inside/-/path-is-inside-1.0.2.tgz"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.7.tgz"
  integrity "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU= sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw=="

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/path-type/-/path-type-4.0.0.tgz"
  integrity "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs= sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw=="

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz"
  integrity sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/picocolors/-/picocolors-1.0.0.tgz"
  integrity "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw= sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ=="

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/picomatch/-/picomatch-2.3.1.tgz"
  integrity "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI= sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/pify/-/pify-4.0.1.tgz"
  integrity "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE= sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM= sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ=="
  dependencies:
    find-up "^4.0.0"

plist@^3.0.5, plist@^3.0.6:
  version "3.0.6"
  resolved "https://registry.npmjs.org/plist/-/plist-3.0.6.tgz"
  integrity sha512-WiIVYyrp8TD4w8yCvyeIr+lkmrGRd5u0VbRnU+tP/aRLxP/YadJUYOMZJ/6hIa3oUyVCsycXvtNRgd5XBJIbiA==
  dependencies:
    base64-js "^1.5.1"
    xmlbuilder "^15.1.1"

popmotion@11.0.3:
  version "11.0.3"
  resolved "https://registry.yarnpkg.com/popmotion/-/popmotion-11.0.3.tgz"
  integrity "sha1-VlxfZZC7zdq3ozoHS7K6l+JLDMk= sha512-Y55FLdj3UxkR7Vl3s7Qr4e9m0onSnP8W7d/xQLsoJM40vs6UKHFdygs6SWryasTZYqugMjm3BepCF4CWXDiHgA=="
  dependencies:
    framesync "6.0.1"
    hey-listen "^1.0.8"
    style-value-types "5.0.0"
    tslib "^2.1.0"

postcss-js@^3.0.3:
  version "3.0.3"
  resolved "https://registry.yarnpkg.com/postcss-js/-/postcss-js-3.0.3.tgz"
  integrity "sha1-LwvTcKLoWZ1FQ59pcEA7WHOr2jM= sha512-gWnoWQXKFw65Hk/mi2+WTQTHdPD5UJdDXZmX073EY/B3BWnYjO4F4t0VneTCnCGQ5E5GsCdMkzPaTXwl3r5dJw=="
  dependencies:
    camelcase-css "^2.0.1"
    postcss "^8.1.6"

postcss-load-config@^3.1.0:
  version "3.1.4"
  resolved "https://registry.yarnpkg.com/postcss-load-config/-/postcss-load-config-3.1.4.tgz"
  integrity "sha1-GrJXH6+EuweId+HQeQXqvp69qFU= sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg=="
  dependencies:
    lilconfig "^2.0.5"
    yaml "^1.10.2"

postcss-nested@5.0.6:
  version "5.0.6"
  resolved "https://registry.yarnpkg.com/postcss-nested/-/postcss-nested-5.0.6.tgz"
  integrity "sha1-RmND9/yNPUavPn26P81H0FKpRbw= sha512-rKqm2Fk0KbA8Vt3AdGN0FB9OBOMDVajMG6ZCf/GoHgdxUJ4sBFp0A/uMIRm+MJUdo33YXEtjqIz8u7DAp8B7DA=="
  dependencies:
    postcss-selector-parser "^6.0.6"

postcss-selector-parser@^6.0.6:
  version "6.0.10"
  resolved "https://registry.yarnpkg.com/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz"
  integrity "sha1-ebYeLA0b/CYC1UnhHQh2JW+N+I0= sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w=="
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^3.3.0:
  version "3.3.1"
  resolved "https://registry.yarnpkg.com/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz"
  integrity "sha1-n/giVH4okyE88cMO+lGsX9G6goE= sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ=="

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ= sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="

postcss@8.4.14, postcss@^8.1.6, postcss@^8.3.11, postcss@^8.3.5:
  version "8.4.14"
  resolved "https://registry.yarnpkg.com/postcss/-/postcss-8.4.14.tgz"
  integrity "sha1-7pJ01WIrSFjBAHp0125C5W/SHK8= sha512-E398TUmfAYFPBSdzgeieK2Y1+1cpdxJx8yXbK/m57nRhKSmk1GB2tO4lbLBtlkfPQTDKfe4Xqv1ASWPpayPEig=="
  dependencies:
    nanoid "^3.3.4"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prebuild-install@^7.0.0:
  version "7.1.0"
  resolved "https://registry.yarnpkg.com/prebuild-install/-/prebuild-install-7.1.0.tgz"
  integrity "sha1-mRtqwWyBWRukCm1d6T+zNnOsE3A= sha512-CNcMgI1xBypOyGqjp3wOc8AAo1nMhZS3Cwd3iHIxOdAUbb+YxdNuM4Z5iIrZ8RLvOsf3F3bl7b7xGq6DjQoNYA=="
  dependencies:
    detect-libc "^2.0.0"
    expand-template "^2.0.3"
    github-from-package "0.0.0"
    minimist "^1.2.3"
    mkdirp-classic "^0.5.3"
    napi-build-utils "^1.0.1"
    node-abi "^3.3.0"
    npmlog "^4.0.1"
    pump "^3.0.0"
    rc "^1.2.7"
    simple-get "^4.0.0"
    tar-fs "^2.0.0"
    tunnel-agent "^0.6.0"

prettier@^2.4.1:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/prettier/-/prettier-2.6.2.tgz"
  integrity "sha1-4m1xoYp0w9DwWX9V8B+2wGwgYDI= sha512-PkUpF+qoXTqhOeWL9fu7As8LXsIUZ1WYaJiY/a7McAQzxjk82OF0tibkFXVCDImZtWxbvojFjerkiLb0/q8mew=="

pretty-bytes@^5.3.0, pretty-bytes@^5.4.1:
  version "5.6.0"
  resolved "https://registry.yarnpkg.com/pretty-bytes/-/pretty-bytes-5.6.0.tgz"
  integrity "sha1-NWJW9kOAR3PIL2RyP+eMksYr6us= sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg=="

pretty-hrtime@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz"
  integrity sha1-t+PqQkNaTJsnWdmeDyAesZWALuE=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity "sha1-eCDZsWEgzFXKmud5JoCufbptf+I= sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="

prompts@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.0.0, prop-types@^15.6.0, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://registry.yarnpkg.com/prop-types/-/prop-types-15.8.1.tgz"
  integrity "sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU= sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg=="
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.4:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/property-expr/-/property-expr-2.0.5.tgz"
  integrity "sha1-J4vbFTCK4Wrz47lkACRST03ALLQ= sha512-IJUkICM5dP5znhCckHSv30Q4b5/JA5enCtkRHYaOVOAocnH/1BQEYTC5NMfT3AVl/iXKdr3aqQbQn9DxyWknwA=="

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/pump/-/pump-3.0.0.tgz"
  integrity "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ= sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww=="
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/punycode/-/punycode-2.1.1.tgz"
  integrity "sha1-tYsBCsQMIsVldhbI0sLALHv0eew= sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="

purgecss@^4.0.3:
  version "4.1.3"
  resolved "https://registry.yarnpkg.com/purgecss/-/purgecss-4.1.3.tgz"
  integrity "sha1-aD9qEzyMTeeqgv4nRtE5OyFJGPc= sha512-99cKy4s+VZoXnPxaoM23e5ABcP851nC2y2GROkkjS8eJaJtlciGavd7iYAw2V84WeBqggZ12l8ef44G99HmTaw=="
  dependencies:
    commander "^8.0.0"
    glob "^7.1.7"
    postcss "^8.3.5"
    postcss-selector-parser "^6.0.6"

qr.js@0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/qr.js/-/qr.js-0.0.0.tgz#cace86386f59a0db8050fa90d9b6b0e88a1e364f"
  integrity sha512-c4iYnWb+k2E+vYpRimHqSu575b1/wKl4XFeJGpFmrJQz5I88v9aY2czh7s0w36srfCM1sXgC/xpoJz5dJfq+OQ==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity "sha1-SSkii7xyTfrEPg77BYyve2z7YkM= sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A=="

queue@^6.0.1:
  version "6.0.2"
  resolved "https://registry.yarnpkg.com/queue/-/queue-6.0.2.tgz"
  integrity "sha1-uRUlKD4jFcdVPS76GNg+dkMv7WU= sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA=="
  dependencies:
    inherits "~2.0.3"

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/quick-lru/-/quick-lru-5.1.1.tgz"
  integrity "sha1-NmST5rPkKjpoheLpnRj4D7eoyTI= sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA=="

ramda@^0.26.1:
  version "0.26.1"
  resolved "https://registry.yarnpkg.com/ramda/-/ramda-0.26.1.tgz"
  integrity "sha1-jUE1HrgRHFU1Nhf8O7/62OTTXQY= sha512-hLWjpy7EnsDBb0p+Z3B7rPi3GDeRG5ZtiI33kJhTt+ORCd38AbAIjB/9zRIUoeTbE/AVX5ZkU7m6bznsvrf8eQ=="

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/randombytes/-/randombytes-2.1.0.tgz"
  integrity "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo= sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ=="
  dependencies:
    safe-buffer "^5.1.0"

rc-drawer@^4.4.2:
  version "4.4.3"
  resolved "https://registry.yarnpkg.com/rc-drawer/-/rc-drawer-4.4.3.tgz"
  integrity "sha1-IJSTeoROVdyWRCNqLZ+6ecNE4yE= sha512-FYztwRs3uXnFOIf1hLvFxIQP9MiZJA+0w+Os8dfDh/90X7z/HqP/Yg+noLCIeHEbKln1Tqelv8ymCAN24zPcfQ=="
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.7.0"

rc-pagination@^3.1.9:
  version "3.1.16"
  resolved "https://registry.yarnpkg.com/rc-pagination/-/rc-pagination-3.1.16.tgz"
  integrity "sha1-sAghCM8Cft7RjtYdgY0xiXw0PoE= sha512-GFcHXJ7XxeJDf9B+ndP4PRDt46maSSgYhiwofBMiIGKIlBhJ0wfu8DMCEvaWJJLpI2u4Gb6zF1dHpiqPFrosPg=="
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"

rc-resize-observer@^1.1.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/rc-resize-observer/-/rc-resize-observer-1.2.0.tgz"
  integrity "sha1-n0YFL4HN8DSYvjUUTLfFP9KCxMc= sha512-6W+UzT3PyDM0wVCEHfoW3qTHPTvbdSgiA43buiy8PzmeMnfgnDeb9NjdimMXMl3/TcrvvWl5RRVdp+NqcR47pQ=="
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-util "^5.15.0"
    resize-observer-polyfill "^1.5.1"

rc-table@^7.19.0:
  version "7.24.2"
  resolved "https://registry.yarnpkg.com/rc-table/-/rc-table-7.24.2.tgz"
  integrity "sha1-+8z170uEzbOMigtBY2XeFXSDv1E= sha512-yefqhtc4V3BeWG2bnDhWYxWX1MOckvW2KU1J55pntZmIGrov5Hx8tQn2gcs6OM0fJ6NgEwUvVEknsCsWI24zUg=="
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.14.0"
    shallowequal "^1.1.0"

rc-util@^5.14.0, rc-util@^5.15.0, rc-util@^5.7.0:
  version "5.21.4"
  resolved "https://registry.yarnpkg.com/rc-util/-/rc-util-5.21.4.tgz"
  integrity "sha1-YeJK0pf2ecoHlrYYo+8w7KlZ2QQ= sha512-rq11ap3NnOIdywFhcMQ9J7DXRJJ1c1Id1Hvr/1Dphr+5X75ERJBJybuh779DdurP4LJQqAhT6Aie0AjrBc5Vqw=="
  dependencies:
    "@babel/runtime" "^7.12.5"
    react-is "^16.12.0"
    shallowequal "^1.1.0"

rc@^1.2.7:
  version "1.2.8"
  resolved "https://registry.yarnpkg.com/rc/-/rc-1.2.8.tgz"
  integrity "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0= sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw=="
  dependencies:
    deep-extend "^0.6.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

react-barcode@^1.4.6:
  version "1.4.6"
  resolved "https://registry.yarnpkg.com/react-barcode/-/react-barcode-1.4.6.tgz#a2ffd8717d1b7df6b482872f1692020c235463a0"
  integrity sha512-56WCPqjgpExPVi5LgKOfxM6uSCB4Wd1r94jY2VEwkIdigPjLp8P9RyIBIyQmDraxtYGOHE0e4Uer4+QrmOIy7Q==
  dependencies:
    jsbarcode "^3.8.0"
    prop-types "^15.6.2"

react-content-loader@^6.0.3:
  version "6.2.0"
  resolved "https://registry.yarnpkg.com/react-content-loader/-/react-content-loader-6.2.0.tgz"
  integrity "sha1-zY/ugWC4/aZhDQxpzlrue4CUy6Y= sha512-r1dI6S+uHNLW68qraLE2njJYOuy6976PpCExuCZUcABWbfnF3FMcmuESRI8L4Bj45wnZ7n8g71hkPLzbma7/Cw=="

react-copy-to-clipboard@^5.0.4:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/react-copy-to-clipboard/-/react-copy-to-clipboard-5.1.0.tgz"
  integrity "sha1-Carl7ExidQzLLmQhpYcl6rxBJVw= sha512-k61RsNgAayIJNoy9yDsYzDe/yAZAzEbEgcz3DZMhF686LEyukcE1hzurxe85JandPUG+yTfGVFzuEw3xt8WP/A=="
  dependencies:
    copy-to-clipboard "^3.3.1"
    prop-types "^15.8.1"

react-countdown@^2.3.2:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/react-countdown/-/react-countdown-2.3.2.tgz"
  integrity "sha1-TMJ/KPLc1HI37mbkufbSoh/AsK0= sha512-Q4SADotHtgOxNWhDdvgupmKVL0pMB9DvoFcxv5AzjsxVhzOVxnttMbAywgqeOdruwEAmnPhOhNv/awAgkwru2w=="
  dependencies:
    prop-types "^15.7.2"

react-datepicker@^4.8.0:
  version "4.8.0"
  resolved "https://registry.npmjs.org/react-datepicker/-/react-datepicker-4.8.0.tgz"
  integrity sha512-u69zXGHMpxAa4LeYR83vucQoUCJQ6m/WBsSxmUMu/M8ahTSVMMyiyQzauHgZA2NUr9y0FUgOAix71hGYUb6tvg==
  dependencies:
    "@popperjs/core" "^2.9.2"
    classnames "^2.2.6"
    date-fns "^2.24.0"
    prop-types "^15.7.2"
    react-onclickoutside "^6.12.0"
    react-popper "^2.2.5"

react-dom@17.0.2:
  version "17.0.2"
  resolved "https://registry.yarnpkg.com/react-dom/-/react-dom-17.0.2.tgz"
  integrity "sha1-7P+2hF462Nv83EmPDQqTlzZQLCM= sha512-s4h96KtLDUQlsENhMn1ar8t2bEa+q/YAtj8pPPdIjPDGBDIVNsrD9aXNWqspUe6AzKCIG0C1HZZLqLV7qpOBGA=="
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    scheduler "^0.20.2"

react-fast-compare@^3.0.1:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/react-fast-compare/-/react-fast-compare-3.2.0.tgz"
  integrity "sha1-ZBqdqBtqYyDycOiXJPtFoLOeQ7s= sha512-rtGImPZ0YyLrscKI9xTpV8psd6I8VAtjKCzQDlzyDvqJA8XOW78TXYQwNRNd8g8JZnDu8q9Fu/1v4HPAVwVdHA=="

react-hook-form@^7.31.2:
  version "7.31.2"
  resolved "https://registry.yarnpkg.com/react-hook-form/-/react-hook-form-7.31.2.tgz"
  integrity "sha1-77esRpgQlUSIt89Avk5QFxIsbl4= sha512-oPudn3YuyzWg//IsT9z2cMEjWocAgHWX/bmueDT8cmsYQnGY5h7/njjvMDfLVv3mbdhYBjslTRnII2MIT7eNCA=="

react-i18next@^11.8.13:
  version "11.16.9"
  resolved "https://registry.yarnpkg.com/react-i18next/-/react-i18next-11.16.9.tgz"
  integrity "sha1-iQzawMSRIOB11sUgtD260/kb0t8= sha512-euXxWvcEAvsY7ZVkwx9ztCq4butqtsGHEkpkuo0RMj8Ru09IF9o2KxCyN+zyv51Nr0aBh/elaTIiR6fMb8YfVg=="
  dependencies:
    "@babel/runtime" "^7.14.5"
    html-escaper "^2.0.2"
    html-parse-stringify "^3.0.1"

react-icons@^4.3.1:
  version "4.3.1"
  resolved "https://registry.yarnpkg.com/react-icons/-/react-icons-4.3.1.tgz"
  integrity "sha1-L6kq67vHH0PS2y7RrtBzYRJOkco= sha512-cB10MXLTs3gVuXimblAdI71jrJx8njrJZmNMEMC+sQu5B/BIOmlsAjskdqpn81y8UBVEGuHODd7/ci5DvoSzTQ=="

react-is@^16.12.0, react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.yarnpkg.com/react-is/-/react-is-16.13.1.tgz"
  integrity "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ= sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="

"react-is@^17.0.1 || ^18.0.0":
  version "18.2.0"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz"
  integrity sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==

react-laag@^2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/react-laag/-/react-laag-2.0.4.tgz"
  integrity "sha1-mieHyp2Dv02LbjBPKdIs/wNlNIw= sha512-9CGIwYJbysmpQC4KeeTx3fNzchvZT3AIYapi2/z7kOJrYopP2uCoPK39qHKuiyawE57EVRI8F1OtbJeyJ7NTrg=="
  dependencies:
    tiny-warning "^1.0.3"

react-lazy-load-image-component@^1.5.6:
  version "1.5.6"
  resolved "https://registry.yarnpkg.com/react-lazy-load-image-component/-/react-lazy-load-image-component-1.5.6.tgz#a4b84257be21b1825680b4e158d167c08aeda5ff"
  integrity sha512-M0jeJtOlTHgThOfgYM9krSqYbR6ShxROy/KVankwbw9/amPKG1t5GSGN1sei6Cyu8+QJVuyAUvQ+LFtCVTTlKw==
  dependencies:
    lodash.debounce "^4.0.8"
    lodash.throttle "^4.1.1"

react-onclickoutside@^6.12.0:
  version "6.12.2"
  resolved "https://registry.npmjs.org/react-onclickoutside/-/react-onclickoutside-6.12.2.tgz"
  integrity sha512-NMXGa223OnsrGVp5dJHkuKxQ4czdLmXSp5jSV9OqiCky9LOpPATn3vLldc+q5fK3gKbEHvr7J1u0yhBh/xYkpA==

react-player@^2.9.0:
  version "2.10.1"
  resolved "https://registry.yarnpkg.com/react-player/-/react-player-2.10.1.tgz"
  integrity "sha1-8u4+wxOT1wQvcnc3VFQUuVH/x+Q= sha512-ova0jY1Y1lqLYxOehkzbNEju4rFXYVkr5rdGD71nsiG4UKPzRXQPTd3xjoDssheoMNjZ51mjT5ysTrdQ2tEvsg=="
  dependencies:
    deepmerge "^4.0.0"
    load-script "^1.0.0"
    memoize-one "^5.1.1"
    prop-types "^15.7.2"
    react-fast-compare "^3.0.1"

react-popper@^2.2.5:
  version "2.3.0"
  resolved "https://registry.npmjs.org/react-popper/-/react-popper-2.3.0.tgz"
  integrity sha512-e1hj8lL3uM+sgSR4Lxzn5h1GxBlpa4CQz0XLF8kx4MDrDRWY0Ena4c97PUeSX9i5W3UAfDP0z0FXCTQkoXUl3Q==
  dependencies:
    react-fast-compare "^3.0.1"
    warning "^4.0.2"

react-qr-code@^2.0.8:
  version "2.0.8"
  resolved "https://registry.yarnpkg.com/react-qr-code/-/react-qr-code-2.0.8.tgz#d34a766fb5b664a40dbdc7020f7ac801bacb2851"
  integrity sha512-zYO9EAPQU8IIeD6c6uAle7NlKOiVKs8ji9hpbWPTGxO+FLqBN2on+XCXQvnhm91nrRd306RvNXUkUNcXXSfhWA==
  dependencies:
    prop-types "^15.8.1"
    qr.js "0.0.0"

react-query@^3.28.0:
  version "3.39.0"
  resolved "https://registry.yarnpkg.com/react-query/-/react-query-3.39.0.tgz"
  integrity "sha1-DKynsNqY5lAIu81N8NJWGMIQAFA= sha512-Od0IkSuS79WJOhzWBx/ys0x13+7wFqgnn64vBqqAAnZ9whocVhl/y1padD5uuZ6EIkXbFbInax0qvY7zGM0thA=="
  dependencies:
    "@babel/runtime" "^7.5.5"
    broadcast-channel "^3.4.1"
    match-sorter "^6.0.2"

react-reconciler@^0.23.0:
  version "0.23.0"
  resolved "https://registry.yarnpkg.com/react-reconciler/-/react-reconciler-0.23.0.tgz"
  integrity "sha1-Xwv8Nd2gMLAiDAfeEfkxMcXW22M= sha512-vV0KlLimP9a/NuRcM6GRVakkmT6MKSzhfo8K72fjHMnlXMOhz9GlPe+/tCp5CWBkg+lsMUt/CR1nypJBTPfwuw=="
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    prop-types "^15.6.2"
    scheduler "^0.17.0"

react-scroll@^1.8.4:
  version "1.8.7"
  resolved "https://registry.yarnpkg.com/react-scroll/-/react-scroll-1.8.7.tgz"
  integrity "sha1-gCADUynvrQDwOWThiv9oIhN946o= sha512-fBOIwweAlhicx8RqP9tQXn/Uhd+DTtVRjw+0VBsIn1Z+MjRYLhTZ0tMoTAU1vOD3dce8mI6copexI4yWII+Luw=="
  dependencies:
    lodash.throttle "^4.1.1"
    prop-types "^15.7.2"

react-select@*:
  version "5.3.2"
  resolved "https://registry.yarnpkg.com/react-select/-/react-select-5.3.2.tgz"
  integrity "sha1-7O4NXFntSst/Vn9948daSI2T2ss= sha512-W6Irh7U6Ha7p5uQQ2ZnemoCQ8mcfgOtHfw3wuMzG6FAu0P+CYicgofSLOq97BhjMx8jS+h+wwWdCBeVVZ9VqlQ=="
  dependencies:
    "@babel/runtime" "^7.12.0"
    "@emotion/cache" "^11.4.0"
    "@emotion/react" "^11.8.1"
    "@types/react-transition-group" "^4.4.0"
    memoize-one "^5.0.0"
    prop-types "^15.6.0"
    react-transition-group "^4.3.0"

react-select@5.1.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/react-select/-/react-select-5.1.0.tgz"
  integrity "sha1-rDhMjiW6bwMSYCYZKyva0PU/v1A= sha512-SkEBD1AYsSXrIdNj5HBt7+Ehe+jxdiB448J0atJqR6lE3l/GcFlRf4JYB3NlHe/02jrW4AnIQLo1t0IqWrxXOw=="
  dependencies:
    "@babel/runtime" "^7.12.0"
    "@emotion/cache" "^11.4.0"
    "@emotion/react" "^11.1.1"
    "@types/react-transition-group" "^4.4.0"
    memoize-one "^5.0.0"
    prop-types "^15.6.0"
    react-transition-group "^4.3.0"

react-share@^4.4.0:
  version "4.4.0"
  resolved "https://registry.yarnpkg.com/react-share/-/react-share-4.4.0.tgz"
  integrity "sha1-yrvyER16kHqIirTYnQhBAynv1e4= sha512-POe8Ge/JT9Ew9iyW7CiYsCCWCb8uMJWqFl9S7W0fJ/oH5gBJNzukH0bL5vSr17KKG5h15d3GfKaoviI22BKeYA=="
  dependencies:
    classnames "^2.2.5"
    jsonp "^0.2.1"

react-star-rating-component@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/react-star-rating-component/-/react-star-rating-component-1.4.1.tgz"
  integrity "sha1-dNwcc0k/CIATMxwjQZC4zMrTH2s= sha512-i0YEvQzToS0s0GDkxn01Jy4EeLpVEyh023NXJTJ+/1+xkvhpACyD4d1YeBhYWZab53ppUnUxs5gmp75gJr3khA=="
  dependencies:
    classnames "^2.2.5"
    prop-types "^15.6.1"

react-toastify@^8.0.3:
  version "8.2.0"
  resolved "https://registry.yarnpkg.com/react-toastify/-/react-toastify-8.2.0.tgz"
  integrity "sha1-731Wvf3GJyymsig2irVkchw6MkQ= sha512-Pg2Ju7NngAamarFvLwqrFomJ57u/Ay6i6zfLurt/qPynWkAkOthu6vxfqYpJCyNhHRhR4hu7+bySSeWWJu6PAg=="
  dependencies:
    clsx "^1.1.1"

react-transition-group@^4.3.0:
  version "4.4.2"
  resolved "https://registry.yarnpkg.com/react-transition-group/-/react-transition-group-4.4.2.tgz"
  integrity "sha1-i1mlbwnO17VcvVPDZ2i5IokNVHA= sha512-/RNYfRAMlZwDSr6z4zNKV6xu53/e2BuaBbGhbyYIXTrmgu/bGHzmqOs7mJSJBHy9Ud+ApHx3QjrkKSp1pxvlFg=="
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react-universal-interface@^0.6.2:
  version "0.6.2"
  resolved "https://registry.yarnpkg.com/react-universal-interface/-/react-universal-interface-0.6.2.tgz"
  integrity "sha1-Xo1DigFymk27y+7OsLhr4Ub+Kzs= sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw=="

react-use@^17.3.1:
  version "17.4.0"
  resolved "https://registry.yarnpkg.com/react-use/-/react-use-17.4.0.tgz"
  integrity "sha1-zv7yWLCmxTSlyAIcJSisbhpM3G0= sha512-TgbNTCA33Wl7xzIJegn1HndB4qTS9u03QUwyNycUnXaweZkE4Kq2SB+Yoxx8qbshkZGYBDvUXbXWRUmQDcZZ/Q=="
  dependencies:
    "@types/js-cookie" "^2.2.6"
    "@xobotyi/scrollbar-width" "^1.9.5"
    copy-to-clipboard "^3.3.1"
    fast-deep-equal "^3.1.3"
    fast-shallow-equal "^1.0.0"
    js-cookie "^2.2.1"
    nano-css "^5.3.1"
    react-universal-interface "^0.6.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.1.0"
    set-harmonic-interval "^1.0.1"
    throttle-debounce "^3.0.1"
    ts-easing "^0.2.0"
    tslib "^2.1.0"

react-waypoint@^10.1.0:
  version "10.3.0"
  resolved "https://registry.npmjs.org/react-waypoint/-/react-waypoint-10.3.0.tgz"
  integrity sha512-iF1y2c1BsoXuEGz08NoahaLFIGI9gTUAAOKip96HUmylRT6DUtpgoBPjk/Y8dfcFVmfVDvUzWjNXpZyKTOV0SQ==
  dependencies:
    "@babel/runtime" "^7.12.5"
    consolidated-events "^1.1.0 || ^2.0.0"
    prop-types "^15.0.0"
    react-is "^17.0.1 || ^18.0.0"

react@17.0.2:
  version "17.0.2"
  resolved "https://registry.yarnpkg.com/react/-/react-17.0.2.tgz#d0b5cc516d29eb3eee383f75b62864cfb6800037"
  integrity sha512-gnhPt75i/dq/z3/6q/0asP78D0u592D5L1pd7M8P+dck6Fu/jJeL6iVVK23fptSUZj8Vjf++7wXA8UNclGQcbA==
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

readable-stream@3, readable-stream@^3.1.1, readable-stream@^3.4.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-3.6.0.tgz"
  integrity "sha1-M3u9o63AcGvT4CRCaihtS0sskZg= sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA=="
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^2.0.6:
  version "2.3.7"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.7.tgz"
  integrity "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c= sha512-Ebho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw=="
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.yarnpkg.com/readdirp/-/readdirp-3.6.0.tgz"
  integrity "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc= sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  dependencies:
    picomatch "^2.2.1"

reduce-css-calc@^2.1.8:
  version "2.1.8"
  resolved "https://registry.yarnpkg.com/reduce-css-calc/-/reduce-css-calc-2.1.8.tgz"
  integrity "sha1-fvh2GijWFJgNwMmC93LJP3qZ3gM= sha512-8liAVezDmUcH+tdzoEGrhfbGcP7nOV4NkGE3a74+qqvE7nt9i4sKLGBuZNOnpI4WiGksiNPklZxva80061QiPg=="
  dependencies:
    css-unit-converter "^1.1.1"
    postcss-value-parser "^3.3.0"

regenerate-unicode-properties@^10.0.1:
  version "10.0.1"
  resolved "https://registry.yarnpkg.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.1.tgz"
  integrity "sha1-f0QnMqp5NKN0DHebubM0DczB+1Y= sha512-vn5DU6yg6h8hP/2OkQo3K7uVILvY4iu0oI4t3HFa81UPkhGJwkRwM10JEc3upjdhHjs/k8GJY1sRBhk5sr69Bw=="
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.yarnpkg.com/regenerate/-/regenerate-1.4.2.tgz"
  integrity "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo= sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A=="

regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "https://registry.yarnpkg.com/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz"
  integrity "sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I= sha512-p3VT+cOEgxFsRRA9X4lkI1E+k2/CtnKtU4gcxyaCUreilL/vqI6CdZ3wxVUx3UOUg+gnUOQQcRI7BmSI656MYA=="

regenerator-transform@^0.15.0:
  version "0.15.0"
  resolved "https://registry.yarnpkg.com/regenerator-transform/-/regenerator-transform-0.15.0.tgz"
  integrity "sha1-y9nq1dd/rhpI2VfPiJrQWGrbZTc= sha512-LsrGtPmbYg19bcPHwdtmXwbW+TqNvtY4riE3P83foeHRroMbH6/2ddFBfab3t7kbzc7v7p4wbkIecHImqt0QNg=="
  dependencies:
    "@babel/runtime" "^7.8.4"

regexp.prototype.flags@^1.2.0, regexp.prototype.flags@^1.4.1, regexp.prototype.flags@^1.4.3:
  version "1.4.3"
  resolved "https://registry.yarnpkg.com/regexp.prototype.flags/-/regexp.prototype.flags-1.4.3.tgz"
  integrity "sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w= sha512-fjggEOO3slI6Wvgjwflkc4NFRCTZAu5CnNfBd5qOMYhWdn67nJBBu34/TkD++eeFmd8C9r9jfXJ27+nSiRkSUA=="
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    functions-have-names "^1.2.2"

regexpu-core@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/regexpu-core/-/regexpu-core-5.0.1.tgz"
  integrity "sha1-xTESKnhA3nQ9z5yD6SO1VgMjztM= sha512-CriEZlrKK9VJw/xQGJpQM5rY88BtuL8DM+AEwvcThHilbxiTAy8vq4iJnd2tqq8wLmjbGZzP7ZcKFjbGkmEFrw=="
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.0.1"
    regjsgen "^0.6.0"
    regjsparser "^0.8.2"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.0.0"

regjsgen@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/regjsgen/-/regjsgen-0.6.0.tgz"
  integrity "sha1-g0FMU1Sv19ZiexavXxD0HE5xgI0= sha512-ozE883Uigtqj3bx7OhL1KNbCzGyW2NQZPl6Hs09WTvCuZD5sTI4JY58bkbQWa/Y9hxIsvJ3M8Nbf7j54IqeZbA=="

regjsparser@^0.8.2:
  version "0.8.4"
  resolved "https://registry.yarnpkg.com/regjsparser/-/regjsparser-0.8.4.tgz"
  integrity "sha1-ihQoX/zF3njFuV1iu/QTtrwTLV8= sha512-J3LABycON/VNEu3abOviqGHuB/LOtOQj8SKmfP9anY5GfAVw/SPjwzSjxGjbZXIxbGfqTHtJw58C2Li/WkStmA=="
  dependencies:
    jsesc "~0.5.0"

remove-accents@0.4.2:
  version "0.4.2"
  resolved "https://registry.yarnpkg.com/remove-accents/-/remove-accents-0.4.2.tgz"
  integrity sha1-CkPTqq4egNuRngeuJUsoXZ4ce7U=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk= sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="

resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "https://registry.yarnpkg.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ= sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY= sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="

resolve@1.1.7:
  version "1.1.7"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.1.7.tgz"
  integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=

resolve@^1.12.0, resolve@^1.14.2, resolve@^1.19.0, resolve@^1.20.0:
  version "1.22.0"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.22.0.tgz"
  integrity "sha1-XguMZ8Fd9XqJvbq+YDoALyFzEZg= sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw=="
  dependencies:
    is-core-module "^2.8.1"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restructure@^0.5.3:
  version "0.5.4"
  resolved "https://registry.yarnpkg.com/restructure/-/restructure-0.5.4.tgz"
  integrity sha1-9U591WNZD7NP1r9Vh2EJrsyyjeg=
  dependencies:
    browserify-optional "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/reusify/-/reusify-1.0.4.tgz"
  integrity "sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY= sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw=="

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/rgb-regex/-/rgb-regex-1.0.1.tgz"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/rgba-regex/-/rgba-regex-1.0.0.tgz"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

rimraf@3.0.2, rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-3.0.2.tgz"
  integrity "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho= sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA=="
  dependencies:
    glob "^7.1.3"

rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.7.1.tgz"
  integrity "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w= sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w=="
  dependencies:
    glob "^7.1.3"

rollup-plugin-terser@^7.0.0:
  version "7.0.2"
  resolved "https://registry.yarnpkg.com/rollup-plugin-terser/-/rollup-plugin-terser-7.0.2.tgz"
  integrity "sha1-6Pu6SGmYGy3DWufopQLVxsBNMk0= sha512-w3iIaU4OxcF52UUXiZNsNeuXIMDvFrr+ZXK6bFZ0Q60qyVfq4uLptoS4bbq3paG3x216eQllFZX7zt6TIImguQ=="
  dependencies:
    "@babel/code-frame" "^7.10.4"
    jest-worker "^26.2.1"
    serialize-javascript "^4.0.0"
    terser "^5.0.0"

rollup@^2.43.1:
  version "2.74.1"
  resolved "https://registry.yarnpkg.com/rollup/-/rollup-2.74.1.tgz"
  integrity "sha1-T7oP8cMSzE7oJpGxVO7mmg0Bkp8= sha512-K2zW7kV8Voua5eGkbnBtWYfMIhYhT9Pel2uhBk2WO5eMee161nPze/XRfvEQPFYz7KgrCCnmh2Wy0AMFLGGmMA=="
  optionalDependencies:
    fsevents "~2.3.2"

rtl-css-js@^1.14.0:
  version "1.15.0"
  resolved "https://registry.yarnpkg.com/rtl-css-js/-/rtl-css-js-1.15.0.tgz"
  integrity "sha1-aA7YFuVwqevMup4c0PICxqi7LcA= sha512-99Cu4wNNIhrI10xxUaABHsdDqzalrSRTie4GeCmbGVuehm4oj+fIy8fTzB+16pmKe8Bv9rl+hxIBez6KxExTew=="
  dependencies:
    "@babel/runtime" "^7.1.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4= sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA=="
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY= sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity "sha1-mR7GnSluAxN0fVm9/St0XDX4go0= sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

sax@1.1.4, sax@>=0.6.0:
  version "1.1.4"
  resolved "https://registry.npmjs.org/sax/-/sax-1.1.4.tgz"
  integrity sha512-5f3k2PbGGp+YtKJjOItpg3P99IMD84E4HOvcfleTb5joCHNXYLsR9yWFPOYGgaeMPDubQILTCMdsFb2OMeOjtg==

scheduler@^0.15.0:
  version "0.15.0"
  resolved "https://registry.yarnpkg.com/scheduler/-/scheduler-0.15.0.tgz"
  integrity "sha1-a/z4D/hQsoD+1K7sxlE7wLTxf44= sha512-xAefmSfN6jqAa7Kuq7LIJY0bwAPG3xlCj0HMEBQk1lxYiDKZscY2xJ5U/61ZTrYbmNQbXa+gc7czPkVo11tnCg=="
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.17.0:
  version "0.17.0"
  resolved "https://registry.yarnpkg.com/scheduler/-/scheduler-0.17.0.tgz"
  integrity "sha1-fJxnPk7HgfrIU5J5FtHEJrbz3f4= sha512-7rro8Io3tnCPuY4la/NuI5F2yfESpnfZyT6TtkXnSWVkcu0BCDJ+8gk5ozUaFaxpIyNuWAPXrH0yFcSi28fnDA=="
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://registry.yarnpkg.com/scheduler/-/scheduler-0.20.2.tgz"
  integrity "sha1-S67jlDbjSqk7SHS93L8P6Li1DpE= sha512-2eWfGgAqqWFGqtdMmcL5zCMK1U8KlXv8SQFGglL3CEtd0aDVDWgeF/YoCmvln55m5zSk3J/20hTaSBeSObsQDQ=="
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://registry.yarnpkg.com/schema-utils/-/schema-utils-2.7.1.tgz"
  integrity "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc= sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg=="
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.1.1:
  version "3.1.1"
  resolved "https://registry.yarnpkg.com/schema-utils/-/schema-utils-3.1.1.tgz"
  integrity "sha1-vHTEtraZXB2I92qLd76nIZ4MgoE= sha512-Y5PQxS4ITlC+EahLuXaY86TXfR7Dc5lw294alXOq86JAHCihAIZfqv8nNCWvaEJvaC51uN9hbLGeV0cFBdH+Fw=="
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

screenfull@^5.1.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/screenfull/-/screenfull-5.2.0.tgz"
  integrity "sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo= sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA=="

semver@7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.0.0.tgz"
  integrity "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44= sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A=="

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.yarnpkg.com/semver/-/semver-6.3.0.tgz"
  integrity "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0= sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw=="

semver@^7.3.5:
  version "7.3.7"
  resolved "https://registry.yarnpkg.com/semver/-/semver-7.3.7.tgz"
  integrity "sha1-EsW2Sa/b+QSXB3luIqQCiBTOUj8= sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g=="
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.7:
  version "7.3.8"
  resolved "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz"
  integrity sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==
  dependencies:
    lru-cache "^6.0.0"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/serialize-javascript/-/serialize-javascript-4.0.0.tgz"
  integrity "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao= sha512-GaNA54380uFefWghODBWEGisLZFj00nS5ACs6yHa9nLqlLpVLO8ChDGeKRjZnV4Nh4n0Qi7nhYZD/9fCPzEqkw=="
  dependencies:
    randombytes "^2.1.0"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "https://registry.yarnpkg.com/serialize-javascript/-/serialize-javascript-6.0.0.tgz"
  integrity "sha1-765diPRdeSQUHai1w6en5mP+/rg= sha512-Qr3TosvguFt8ePWqsvRfrKyQXIiW+nGbYpy8XK24NQHE83caxWt+mIymTT19DGFbNWNLfEwsrkSmN64lVWB9ag=="
  dependencies:
    randombytes "^2.1.0"

set-blocking@~2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-harmonic-interval@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/set-harmonic-interval/-/set-harmonic-interval-1.0.1.tgz"
  integrity "sha1-4Xc3BVOc37gM4cPZnn8pi7OZUkk= sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g=="

shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/shallowequal/-/shallowequal-1.1.0.tgz"
  integrity "sha1-GI1SHelbkIdAT9TctosT3wrk5/g= sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ=="

sharp@^0.29.2:
  version "0.29.3"
  resolved "https://registry.yarnpkg.com/sharp/-/sharp-0.29.3.tgz"
  integrity "sha1-DaGD1iYJTJdFFqSPq5s+S6kutcI= sha512-fKWUuOw77E4nhpyzCCJR1ayrttHoFHBT2U/kR/qEMRhvPEcluG4BKj324+SCO1e84+knXHwhJ1HHJGnUt4ElGA=="
  dependencies:
    color "^4.0.1"
    detect-libc "^1.0.3"
    node-addon-api "^4.2.0"
    prebuild-install "^7.0.0"
    semver "^7.3.5"
    simple-get "^4.0.0"
    tar-fs "^2.1.1"
    tunnel-agent "^0.6.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/side-channel/-/side-channel-1.0.4.tgz"
  integrity "sha1-785cj9wQTudRslxY1CkAEfpeos8= sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw=="
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk= sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="

simple-concat@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/simple-concat/-/simple-concat-1.0.1.tgz"
  integrity "sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8= sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q=="

simple-get@^4.0.0:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/simple-get/-/simple-get-4.0.1.tgz"
  integrity "sha1-SjnbVJKHyXnTUhEvoD/Zn9a8NUM= sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA=="
  dependencies:
    decompress-response "^6.0.0"
    once "^1.3.1"
    simple-concat "^1.0.0"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.yarnpkg.com/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/slash/-/slash-3.0.0.tgz"
  integrity "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ= sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q=="

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz"
  integrity sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/source-list-map/-/source-list-map-2.0.1.tgz"
  integrity "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ= sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw=="

source-map-js@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/source-map-js/-/source-map-js-1.0.2.tgz"
  integrity "sha1-rbw2HZxi3zgBJefxYfccgm8eSQw= sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.yarnpkg.com/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity "sha1-BP58f54e0tZiIzwoyys1ufY/bk8= sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.5.6:
  version "0.5.6"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.6.tgz"
  integrity sha1-dc449SvwczxafwwRjYEzSiu19BI=

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.6.1.tgz"
  integrity "sha1-dHIq8y6WFOnCh6jQu95IteLxomM= sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g=="

source-map@^0.8.0-beta.0, source-map@~0.8.0-beta.0:
  version "0.8.0-beta.0"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.8.0-beta.0.tgz"
  integrity "sha1-1MG7QsP37pJfAFknuhBwng0dHxE= sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA=="
  dependencies:
    whatwg-url "^7.0.0"

source-map@~0.1.30:
  version "0.1.43"
  resolved "https://registry.yarnpkg.com/source-map/-/source-map-0.1.43.tgz"
  integrity sha1-wkvBRspRfBRx9drL4lcbK3+eM0Y=
  dependencies:
    amdefine ">=0.0.4"

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://registry.yarnpkg.com/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"
  integrity "sha1-6oBL2UhXQC5pktBaOO8a41qatMQ= sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA=="

split2@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/split2/-/split2-4.1.0.tgz"
  integrity sha512-VBiJxFkxiXRlUIeyMQi8s4hgvKCSjtknJv/LVYbrgALPwf5zSKmEwV9Lst25AkvMDnvxODugjdl6KZgwKM1WYQ==

ssr-window@^3.0.0, ssr-window@^3.0.0-alpha.1:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/ssr-window/-/ssr-window-3.0.0.tgz"
  integrity "sha1-/VuCgBY4lD4MxwTEaRgBQ1r3rDc= sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA=="

stack-generator@^2.0.5:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/stack-generator/-/stack-generator-2.0.5.tgz"
  integrity "sha1-+wDltO6X3mA+B3PqeM6UTYFZbDY= sha512-/t1ebrbHkrLrDuNMdeAcsvynWgoH/i4o8EGGfX7dEYDoTXOYVAkEpFdtshlvabzc6JlJ8Kf9YdFEoz7JkzGN9Q=="
  dependencies:
    stackframe "^1.1.1"

stackframe@^1.1.1:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/stackframe/-/stackframe-1.2.1.tgz"
  integrity "sha1-EDOjRz7mfwji8vyOumrvT4RRJOE= sha512-h88QkzREN/hy8eRdyNhhsO7RSJ5oyTqxxmmn0dzBIMUclZsjpfmrsg81vp8mjjAs2vAZ72nyWxRUwSwmh0e4xg=="

stacktrace-gps@^3.0.4:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/stacktrace-gps/-/stacktrace-gps-3.0.4.tgz"
  integrity "sha1-dojcL8Cf+zoTFl6+DbyvQbzwxpo= sha512-qIr8x41yZVSldqdqe6jciXEaSCKw1U8XTXpjDuy0ki/apyTn/r3w9hDAAQOhZdxvsC93H+WwwEu5cq5VemzYeg=="
  dependencies:
    source-map "0.5.6"
    stackframe "^1.1.1"

stacktrace-js@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/stacktrace-js/-/stacktrace-js-2.0.2.tgz"
  integrity "sha1-TKk+qfSUdS1VcJoIHUAP2uvuiXs= sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg=="
  dependencies:
    error-stack-parser "^2.0.6"
    stack-generator "^2.0.5"
    stacktrace-gps "^3.0.4"

string-width@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-1.0.2.tgz"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0:
  version "4.2.3"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-4.2.3.tgz"
  integrity "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA= sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g=="
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.matchall@^4.0.6:
  version "4.0.7"
  resolved "https://registry.yarnpkg.com/string.prototype.matchall/-/string.prototype.matchall-4.0.7.tgz"
  integrity "sha1-jm7LDYofsf2kcNgazsstugV6SB0= sha512-f48okCX7JiwVi1NXCVWcFnZgADDC/n2vePlQ/KUCNqCikLLilQvwjMO8+BHVKvgzH0JB0J9LEPgxOGT02RoETg=="
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.4.1"
    side-channel "^1.0.4"

string.prototype.trimend@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/string.prototype.trimend/-/string.prototype.trimend-1.0.5.tgz"
  integrity "sha1-kUpluqqyX73U7ikcp93lfoacuNA= sha512-I7RGvmjV4pJ7O3kdf+LXFpVfdNOxtCW/2C8f6jNiW4+PQchwxkCDzlk1/7p+Wl4bqFIZeF47qAHXLuHHWKAxog=="
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

string.prototype.trimstart@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.5.tgz"
  integrity "sha1-VGbZO6WM+iE0g5+B1/QkN+jAH+8= sha512-THx16TJCGlsN0o6dl2o6ncWUsdgnLRSA23rRE5pyGBw/mLr3Ej/R2LaqCtgP8VNMGZsvMWnf9ooZPyY2bHvUFg=="
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.19.5"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4= sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA=="
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g= sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/stringify-object/-/stringify-object-3.3.0.tgz"
  integrity "sha1-cDBlrvyhkwDTzoivT1s5VtdVZik= sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw=="
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-comments@^2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/strip-comments/-/strip-comments-2.0.1.tgz"
  integrity "sha1-StEcP7ysF3pnpArCJMoznKHBups= sha512-ZprKx+bBLXv067WTCALv8SSz5l2+XhpYCsVtSqlMnkAXMWDq+/ekVbl1ghqP9rUHTzv6sm/DwCOiYutU/yp1fw=="

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

style-value-types@5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/style-value-types/-/style-value-types-5.0.0.tgz"
  integrity "sha1-dsNfDleYQ9UjGHmJ2oZnKUEfyK0= sha512-08yq36Ikn4kx4YU6RD7jWEv27v4V+PUsOGa4n/as8Et3CuODMJQ00ENeAVXAeydX4Z2j1XHZF1K2sX4mGl18fA=="
  dependencies:
    hey-listen "^1.0.8"
    tslib "^2.1.0"

styled-jsx@5.0.7:
  version "5.0.7"
  resolved "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.0.7.tgz"
  integrity sha512-b3sUzamS086YLRuvnaDigdAewz1/EFYlHpYBP5mZovKEdQQOIIYq8lApylub3HHZ6xFjV051kkGU7cudJmrXEA==

stylis@4.0.13:
  version "4.0.13"
  resolved "https://registry.yarnpkg.com/stylis/-/stylis-4.0.13.tgz"
  integrity "sha1-9dszLjdtE8yE7P5drOmipR2VTJE= sha512-xGPXiFVl4YED9Jh7Euv2V220mriG9u4B2TA6Ybjc1catrstKD2PpIdU3U0RKpkVBC2EhmL/F0sPCr9vrFTNRag=="

stylis@^4.0.6:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/stylis/-/stylis-4.1.1.tgz"
  integrity "sha1-5Gxqm798WNseZbtzC+FXMRrh/hI= sha512-lVrM/bNdhVX2OgBFNa2YJ9Lxj7kPzylieHd3TNjuGE0Re9JB7joL5VUKOVH1kdNNJTgGPpT8hmwIAPLaSyEVFQ=="

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-5.5.0.tgz"
  integrity "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8= sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-7.2.0.tgz"
  integrity "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo= sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-8.1.1.tgz"
  integrity "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw= sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q=="
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity "sha1-btpL00SjyUrqN21MwxvHcxEDngk= sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="

svg-arc-to-cubic-bezier@^3.0.0, svg-arc-to-cubic-bezier@^3.2.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/svg-arc-to-cubic-bezier/-/svg-arc-to-cubic-bezier-3.2.0.tgz"
  integrity "sha1-OQxFADWuHEoBBNkGUDBMO8gUq+Y= sha512-djbJ/vZKZO+gPoSDThGNpKDO+o+bAeA4XQKovvkNCqnIS2t+S4qnLAGQhyyrulhCFRl1WWzAp0wUDV8PpTVU3g=="

swiper@^6.7.1:
  version "6.8.4"
  resolved "https://registry.yarnpkg.com/swiper/-/swiper-6.8.4.tgz"
  integrity "sha1-k4/tQUT015Uvv5xE5YMtEzpN55Q= sha512-O+buF9Q+sMA0H7luMS8R59hCaJKlpo8PXhQ6ZYu6Rn2v9OsFd4d1jmrv14QvxtQpKAvL/ZiovEeANI/uDGet7g=="
  dependencies:
    dom7 "^3.0.0"
    ssr-window "^3.0.0"

tailwindcss-rtl@^0.7.3:
  version "0.7.3"
  resolved "https://registry.yarnpkg.com/tailwindcss-rtl/-/tailwindcss-rtl-0.7.3.tgz"
  integrity "sha1-2NMjbkRv11BNkkA2auqKWYfmbBI= sha512-4lOuA5HJj/a9qBmnPmzdxJybqmQUKhA4/iDBiGs7YZqJ7eqSziKR035er3ZhGOl08s3FaKwlgNPlgQ13vMs/Tw=="

tailwindcss@^2.2.17:
  version "2.2.19"
  resolved "https://registry.yarnpkg.com/tailwindcss/-/tailwindcss-2.2.19.tgz"
  integrity "sha1-VA5GSDLNRiu5ZJwUhLCjgxXCZTw= sha512-6Ui7JSVtXadtTUo2NtkBBacobzWiQYVjYW0ZnKaP9S1ZCKQ0w7KVNz+YSDI/j7O7KCMHbOkz94ZMQhbT9pOqjw=="
  dependencies:
    arg "^5.0.1"
    bytes "^3.0.0"
    chalk "^4.1.2"
    chokidar "^3.5.2"
    color "^4.0.1"
    cosmiconfig "^7.0.1"
    detective "^5.2.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.2.7"
    fs-extra "^10.0.0"
    glob-parent "^6.0.1"
    html-tags "^3.1.0"
    is-color-stop "^1.1.0"
    is-glob "^4.0.1"
    lodash "^4.17.21"
    lodash.topath "^4.5.2"
    modern-normalize "^1.1.0"
    node-emoji "^1.11.0"
    normalize-path "^3.0.0"
    object-hash "^2.2.0"
    postcss-js "^3.0.3"
    postcss-load-config "^3.1.0"
    postcss-nested "5.0.6"
    postcss-selector-parser "^6.0.6"
    postcss-value-parser "^4.1.0"
    pretty-hrtime "^1.0.3"
    purgecss "^4.0.3"
    quick-lru "^5.1.1"
    reduce-css-calc "^2.1.8"
    resolve "^1.20.0"
    tmp "^0.2.1"

tar-fs@^2.0.0, tar-fs@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/tar-fs/-/tar-fs-2.1.1.tgz"
  integrity "sha1-SJoVq4Xx8L76uzcLfeT561y+h4Q= sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng=="
  dependencies:
    chownr "^1.1.1"
    mkdirp-classic "^0.5.2"
    pump "^3.0.0"
    tar-stream "^2.1.4"

tar-stream@^2.1.4:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/tar-stream/-/tar-stream-2.2.0.tgz"
  integrity "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc= sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ=="
  dependencies:
    bl "^4.0.3"
    end-of-stream "^1.4.1"
    fs-constants "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^3.1.1"

tar@^6.1.11:
  version "6.1.11"
  resolved "https://registry.npmjs.org/tar/-/tar-6.1.11.tgz"
  integrity sha512-an/KZQzQUkZCkuoAA64hM92X0Urb6VpRhAFllDzz44U2mcD5scmT3zBc4VgVpkugF580+DQn8eAFSyoQt0tznA==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^3.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

temp-dir@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/temp-dir/-/temp-dir-2.0.0.tgz"
  integrity "sha1-vekrBb3+sVFugEycAK1FF38xMh4= sha512-aoBAniQmmwtcKp/7BzsH8Cxzv8OL736p7v1ihGb5e9DJ9kTwGWHrQrVB5+lfVDzfGrdRzXch+ig7LHaY1JTOrg=="

tempy@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/tempy/-/tempy-0.6.0.tgz"
  integrity "sha1-ZeLDWrwG8RJKl/OHsIMDRCveWfM= sha512-G13vtMYPT/J8A4X2SjdtBTphZlrp1gKv6hZiOjw14RCWg6GbHuQBGtjlx75xLbYV/wEc0D7G5K4rxKP/cXk8Bw=="
  dependencies:
    is-stream "^2.0.0"
    temp-dir "^2.0.0"
    type-fest "^0.16.0"
    unique-string "^2.0.0"

terser-webpack-plugin@^5.3.1:
  version "5.3.1"
  resolved "https://registry.yarnpkg.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.1.tgz"
  integrity "sha1-AyDcwnCtU3LB6Jk/q72SeSl3PlQ= sha512-GvlZdT6wPQKbDNW/GDQzZFg/j4vKU96yl2q6mcUkzKOgW4gwf1Z8cZToUCrz31XHlPWH8MVb1r2tFtdDtTGJ7g=="
  dependencies:
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"
    terser "^5.7.2"

terser@^5.0.0, terser@^5.7.2:
  version "5.13.1"
  resolved "https://registry.yarnpkg.com/terser/-/terser-5.13.1.tgz"
  integrity "sha1-ZjMs3FoBsEoiTJ+tRJ/BoY6qF5k= sha512-hn4WKOfwnwbYfe48NgrQjqNOH9jzLqRcIfbYytOXCOv46LBfWr9bDS17MQqOi+BWGD0sJK3Sj5NC/gJjiojaoA=="
  dependencies:
    acorn "^8.5.0"
    commander "^2.20.0"
    source-map "~0.8.0-beta.0"
    source-map-support "~0.5.20"

throttle-debounce@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/throttle-debounce/-/throttle-debounce-3.0.1.tgz"
  integrity "sha1-MvlNhN+olPeGyaHykOemRbahmrs= sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg=="

through2@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/through2/-/through2-4.0.2.tgz"
  integrity sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw==
  dependencies:
    readable-stream "3"

through@~2.3.4:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tiny-inflate@^1.0.0, tiny-inflate@^1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/tiny-inflate/-/tiny-inflate-1.0.3.tgz"
  integrity "sha1-EicVSUkToYBRZqr3yTRnkz7qJsQ= sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw=="

tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/tiny-warning/-/tiny-warning-1.0.3.tgz"
  integrity "sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q= sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA=="

tmp@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/tmp/-/tmp-0.2.1.tgz"
  integrity "sha1-hFf8MDfc9HGcJRNnoa9lAO4czxQ= sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ=="
  dependencies:
    rimraf "^3.0.0"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.yarnpkg.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ= sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/toggle-selection/-/toggle-selection-1.0.6.tgz"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/toposort/-/toposort-2.0.2.tgz"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-1.0.1.tgz"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.yarnpkg.com/tr46/-/tr46-0.0.3.tgz"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tree-kill@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz"
  integrity sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==

ts-easing@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/ts-easing/-/ts-easing-0.2.0.tgz"
  integrity "sha1-yKijUCUQVWZYjYfb2gXdf7+lpOw= sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ=="

tslib@^2.0.1, tslib@^2.1.0, tslib@^2.4.0:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/tslib/-/tslib-2.4.0.tgz"
  integrity "sha1-fOyqfwc85oCgWEeqd76UEJjzbcM= sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ=="

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "https://registry.yarnpkg.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

type-fest@^0.16.0:
  version "0.16.0"
  resolved "https://registry.yarnpkg.com/type-fest/-/type-fest-0.16.0.tgz"
  integrity "sha1-MkC4kaeLDerpENvrhlU+VSoUiGA= sha512-eaBzG6MxNzEn9kiwvtre90cXaNLkmadMWa1zQMs3XORCXNbsH/OewwbxC5ia9dCxIxnTAsSxXJaa/p5y8DlvJg=="

type-fest@^1.2.1:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/type-fest/-/type-fest-1.4.0.tgz"
  integrity "sha1-6fuBP+O/F0TsNZ1V0a/++nbxS+E= sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA=="

typescript@^4.4.4:
  version "4.6.4"
  resolved "https://registry.yarnpkg.com/typescript/-/typescript-4.6.4.tgz"
  integrity "sha1-yqeLvDpZ5qXFENNXA/agmHfORek= sha512-9ia/jWHIEbo49HfjrLGfKbZSuWo9iTMwXO+Ca3pRsSpbsMbc7/IU8NKdCZVRRBafVPGnoJeFL76ZOAA84I9fEg=="

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  integrity "sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54= sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw=="
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  integrity "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw= sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ=="

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM= sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q=="
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz"
  integrity "sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ= sha512-7Yhkc0Ye+t4PNYzOGKedDhXbYIBe1XEQYQxOPyhcXNMJ0WCABqqj6ckydd6pWRZTHV4GuCPKdBAUiMc60tsKVw=="

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz"
  integrity "sha1-CjbLmlhcT2q9Ua0d7dsoXBZSl8g= sha512-5Zfuy9q/DFr4tfO7ZPeVXb1aPoeQSdeFMLpYuFebehDAhbuevLs5yxSZmIFN1tP5F9Wl4IpJrYojg85/zgyZHQ=="

unicode-trie@^0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/unicode-trie/-/unicode-trie-0.3.1.tgz"
  integrity sha1-1nHd3YkQGgi6w3tqUWEBBgIFIIU=
  dependencies:
    pako "^0.2.5"
    tiny-inflate "^1.0.0"

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/unique-string/-/unique-string-2.0.0.tgz"
  integrity "sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0= sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg=="
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-2.0.0.tgz"
  integrity "sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc= sha512-hAZsKq7Yy11Zu1DE0OzWjw7nnLZmJZYTDZZyEFHZdUhV8FkH5MCfoU1XMaxXovpyW5nq5scPqq0ZDP9Zyl04oQ=="

unload@2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/unload/-/unload-2.2.0.tgz"
  integrity "sha1-zMiP3K00X6oGqSA57A+AtIiIDvc= sha512-B60uB5TNBLtN6/LsgAf3udH9saB5p7gqJwcFfbOEZ8BcBHnGwCf6G/TGiEqkRAxX7zAFIUtzdrXQSdL3Q/wqNA=="
  dependencies:
    "@babel/runtime" "^7.6.2"
    detect-node "^2.0.4"

untildify@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/untildify/-/untildify-4.0.0.tgz"
  integrity sha512-KK8xQ1mkzZeg9inewmFVDNkg3l5LUhoq9kN6iWYB/CC9YMG8HA+c1Q8HwDe6dEX7kErrEVNVBO3fWsVq5iDgtw==

upath@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/upath/-/upath-1.2.0.tgz"
  integrity "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ= sha512-aZwGpamFO61g3OlfT7OQCHqhGnW43ieH9WZeP7QxN/G/jS4jfqUkZxoryvJgVPEcrl5NL/ggHsSmLMHuH64Lhg=="

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.yarnpkg.com/uri-js/-/uri-js-4.4.1.tgz"
  integrity "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34= sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  dependencies:
    punycode "^2.1.0"

use-sync-external-store@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz"
  integrity sha512-eEgnFxGQ1Ife9bzYs6VLi8/4X6CObHMw9Qr9tPY43iKwsPw8xE8+EFsf/2cFZ5S3esXgpWgtSCtLNS41F+sKPA==

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

void-elements@3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/void-elements/-/void-elements-3.1.0.tgz"
  integrity sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=

warning@^4.0.2:
  version "4.0.3"
  resolved "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz"
  integrity sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==
  dependencies:
    loose-envify "^1.0.0"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/webidl-conversions/-/webidl-conversions-4.0.2.tgz"
  integrity "sha1-qFWYCx8LazWbodXZ+zmulB+qY60= sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg=="

webpack-sources@^1.4.3:
  version "1.4.3"
  resolved "https://registry.yarnpkg.com/webpack-sources/-/webpack-sources-1.4.3.tgz"
  integrity "sha1-7t2OwLko+/HL/plOItLYkPMwqTM= sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ=="
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "https://registry.yarnpkg.com/whatwg-url/-/whatwg-url-7.1.0.tgz"
  integrity "sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY= sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg=="
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  integrity "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY= sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg=="
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.0:
  version "1.1.5"
  resolved "https://registry.yarnpkg.com/wide-align/-/wide-align-1.1.5.tgz"
  integrity "sha1-3x1MIGhUNp7PPJpImPGyP72dFdM= sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg=="
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

workbox-background-sync@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-background-sync/-/workbox-background-sync-6.5.3.tgz"
  integrity "sha1-fGbBg2rspvN2LcSNF6GFKjOzFow= sha512-0DD/V05FAcek6tWv9XYj2w5T/plxhDSpclIcAGjA/b7t/6PdaRkQ7ZgtAX6Q/L7kV7wZ8uYRJUoH11VjNipMZw=="
  dependencies:
    idb "^6.1.4"
    workbox-core "6.5.3"

workbox-broadcast-update@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-broadcast-update/-/workbox-broadcast-update-6.5.3.tgz"
  integrity "sha1-/CrXnPUH4ilQzam68emgzMQ/Mbw= sha512-4AwCIA5DiDrYhlN+Miv/fp5T3/whNmSL+KqhTwRBTZIL6pvTgE4lVuRzAt1JltmqyMcQ3SEfCdfxczuI4kwFQg=="
  dependencies:
    workbox-core "6.5.3"

workbox-build@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-build/-/workbox-build-6.5.3.tgz"
  integrity "sha1-OOPyhtY9J0W/9NFHi7Omq1yLEXA= sha512-8JNHHS7u13nhwIYCDea9MNXBNPHXCs5KDZPKI/ZNTr3f4sMGoD7hgFGecbyjX1gw4z6e9bMpMsOEJNyH5htA/w=="
  dependencies:
    "@apideck/better-ajv-errors" "^0.3.1"
    "@babel/core" "^7.11.1"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.2"
    "@rollup/plugin-babel" "^5.2.0"
    "@rollup/plugin-node-resolve" "^11.2.1"
    "@rollup/plugin-replace" "^2.4.1"
    "@surma/rollup-plugin-off-main-thread" "^2.2.3"
    ajv "^8.6.0"
    common-tags "^1.8.0"
    fast-json-stable-stringify "^2.1.0"
    fs-extra "^9.0.1"
    glob "^7.1.6"
    lodash "^4.17.20"
    pretty-bytes "^5.3.0"
    rollup "^2.43.1"
    rollup-plugin-terser "^7.0.0"
    source-map "^0.8.0-beta.0"
    stringify-object "^3.3.0"
    strip-comments "^2.0.1"
    tempy "^0.6.0"
    upath "^1.2.0"
    workbox-background-sync "6.5.3"
    workbox-broadcast-update "6.5.3"
    workbox-cacheable-response "6.5.3"
    workbox-core "6.5.3"
    workbox-expiration "6.5.3"
    workbox-google-analytics "6.5.3"
    workbox-navigation-preload "6.5.3"
    workbox-precaching "6.5.3"
    workbox-range-requests "6.5.3"
    workbox-recipes "6.5.3"
    workbox-routing "6.5.3"
    workbox-strategies "6.5.3"
    workbox-streams "6.5.3"
    workbox-sw "6.5.3"
    workbox-window "6.5.3"

workbox-cacheable-response@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-cacheable-response/-/workbox-cacheable-response-6.5.3.tgz"
  integrity "sha1-sfjCvFmae+j348JiU1YpxVhzjkc= sha512-6JE/Zm05hNasHzzAGKDkqqgYtZZL2H06ic2GxuRLStA4S/rHUfm2mnLFFXuHAaGR1XuuYyVCEey1M6H3PdZ7SQ=="
  dependencies:
    workbox-core "6.5.3"

workbox-core@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-core/-/workbox-core-6.5.3.tgz"
  integrity "sha1-vKA4qe8NemNKbbKmD0UxPtIqwkk= sha512-Bb9ey5n/M9x+l3fBTlLpHt9ASTzgSGj6vxni7pY72ilB/Pb3XtN+cZ9yueboVhD5+9cNQrC9n/E1fSrqWsUz7Q=="

workbox-expiration@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-expiration/-/workbox-expiration-6.5.3.tgz"
  integrity "sha1-78CBHzcaLt4QUrneHE8HK3HVBQM= sha512-jzYopYR1zD04ZMdlbn/R2Ik6ixiXbi15c9iX5H8CTi6RPDz7uhvMLZPKEndZTpfgmUk8mdmT9Vx/AhbuCl5Sqw=="
  dependencies:
    idb "^6.1.4"
    workbox-core "6.5.3"

workbox-google-analytics@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-google-analytics/-/workbox-google-analytics-6.5.3.tgz"
  integrity "sha1-zIw6YfRJExZgpO0vU2LZo1mbGP4= sha512-3GLCHotz5umoRSb4aNQeTbILETcrTVEozSfLhHSBaegHs1PnqCmN0zbIy2TjTpph2AGXiNwDrWGF0AN+UgDNTw=="
  dependencies:
    workbox-background-sync "6.5.3"
    workbox-core "6.5.3"
    workbox-routing "6.5.3"
    workbox-strategies "6.5.3"

workbox-navigation-preload@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-navigation-preload/-/workbox-navigation-preload-6.5.3.tgz"
  integrity "sha1-gbdPWYsRqgfizxwhr3qCak8PcLM= sha512-bK1gDFTc5iu6lH3UQ07QVo+0ovErhRNGvJJO/1ngknT0UQ702nmOUhoN9qE5mhuQSrnK+cqu7O7xeaJ+Rd9Tmg=="
  dependencies:
    workbox-core "6.5.3"

workbox-precaching@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-precaching/-/workbox-precaching-6.5.3.tgz"
  integrity "sha1-yHAxKy75AdeQq55I2ghOd2xir0c= sha512-sjNfgNLSsRX5zcc63H/ar/hCf+T19fRtTqvWh795gdpghWb5xsfEkecXEvZ8biEi1QD7X/ljtHphdaPvXDygMQ=="
  dependencies:
    workbox-core "6.5.3"
    workbox-routing "6.5.3"
    workbox-strategies "6.5.3"

workbox-range-requests@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-range-requests/-/workbox-range-requests-6.5.3.tgz"
  integrity "sha1-5iSsgv8mal5PI20FV5fe8HlJ2UE= sha512-pGCP80Bpn/0Q0MQsfETSfmtXsQcu3M2QCJwSFuJ6cDp8s2XmbUXkzbuQhCUzKR86ZH2Vex/VUjb2UaZBGamijA=="
  dependencies:
    workbox-core "6.5.3"

workbox-recipes@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-recipes/-/workbox-recipes-6.5.3.tgz"
  integrity "sha1-Fb6snYrno6HBACGAlKgktN0/1Zo= sha512-IcgiKYmbGiDvvf3PMSEtmwqxwfQ5zwI7OZPio3GWu4PfehA8jI8JHI3KZj+PCfRiUPZhjQHJ3v1HbNs+SiSkig=="
  dependencies:
    workbox-cacheable-response "6.5.3"
    workbox-core "6.5.3"
    workbox-expiration "6.5.3"
    workbox-precaching "6.5.3"
    workbox-routing "6.5.3"
    workbox-strategies "6.5.3"

workbox-routing@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-routing/-/workbox-routing-6.5.3.tgz"
  integrity "sha1-oKaZ2MyQtWkr098kZ5rLvaORN3c= sha512-DFjxcuRAJjjt4T34RbMm3MCn+xnd36UT/2RfPRfa8VWJGItGJIn7tG+GwVTdHmvE54i/QmVTJepyAGWtoLPTmg=="
  dependencies:
    workbox-core "6.5.3"

workbox-strategies@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-strategies/-/workbox-strategies-6.5.3.tgz"
  integrity "sha1-S+qaSP7hbPQ3ZuDYE4KWdzyKl4M= sha512-MgmGRrDVXs7rtSCcetZgkSZyMpRGw8HqL2aguszOc3nUmzGZsT238z/NN9ZouCxSzDu3PQ3ZSKmovAacaIhu1w=="
  dependencies:
    workbox-core "6.5.3"

workbox-streams@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-streams/-/workbox-streams-6.5.3.tgz"
  integrity "sha1-toYCkAMcqn0ORq1xQjFclDWceAs= sha512-vN4Qi8o+b7zj1FDVNZ+PlmAcy1sBoV7SC956uhqYvZ9Sg1fViSbOpydULOssVJ4tOyKRifH/eoi6h99d+sJ33w=="
  dependencies:
    workbox-core "6.5.3"
    workbox-routing "6.5.3"

workbox-sw@6.5.3:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-sw/-/workbox-sw-6.5.3.tgz"
  integrity "sha1-zS8MCG9ElqzSV3TtAsSFBBib690= sha512-BQBzm092w+NqdIEF2yhl32dERt9j9MDGUTa2Eaa+o3YKL4Qqw55W9yQC6f44FdAHdAJrJvp0t+HVrfh8AiGj8A=="

workbox-webpack-plugin@^6.5.2:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-webpack-plugin/-/workbox-webpack-plugin-6.5.3.tgz"
  integrity "sha1-w3uzI75JUjEVZcB9tRBU/lnIfXM= sha512-Es8Xr02Gi6Kc3zaUwR691ZLy61hz3vhhs5GztcklQ7kl5k2qAusPh0s6LF3wEtlpfs9ZDErnmy5SErwoll7jBA=="
  dependencies:
    fast-json-stable-stringify "^2.1.0"
    pretty-bytes "^5.4.1"
    upath "^1.2.0"
    webpack-sources "^1.4.3"
    workbox-build "6.5.3"

workbox-window@6.5.3, workbox-window@^6.5.2:
  version "6.5.3"
  resolved "https://registry.yarnpkg.com/workbox-window/-/workbox-window-6.5.3.tgz"
  integrity "sha1-St5wBWy3NHfvHNj+p8/Q7L2CXH8= sha512-GnJbx1kcKXDtoJBVZs/P7ddP0Yt52NNy4nocjBpYPiRhMqTpJCNrSL+fGHZ/i/oP6p/vhE8II0sA6AZGKGnssw=="
  dependencies:
    "@types/trusted-types" "^2.0.2"
    workbox-core "6.5.3"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.npmjs.org/xml2js/-/xml2js-0.4.23.tgz"
  integrity sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@^15.1.1:
  version "15.1.1"
  resolved "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz"
  integrity sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz"
  integrity sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==

xtend@^4.0.2:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.2.tgz"
  integrity "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q= sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-4.0.0.tgz"
  integrity "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI= sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="

yaml@^1.10.0, yaml@^1.10.2, yaml@^1.7.2:
  version "1.10.2"
  resolved "https://registry.yarnpkg.com/yaml/-/yaml-1.10.2.tgz"
  integrity "sha1-IwHF/78StGfejaIzOkWeKeeSDks= sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg=="

yauzl@^2.10.0:
  version "2.10.0"
  resolved "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz"
  integrity sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"

yup@^0.32.9:
  version "0.32.11"
  resolved "https://registry.yarnpkg.com/yup/-/yup-0.32.11.tgz"
  integrity "sha1-1n+4Pu+kaYYHmC5j98pMXtPPGMU= sha512-Z2Fe1bn+eLstG8DRR6FTavGD+MeAwyfmouhHsIUgaADz8jvFKbO/fXc2trJKZg+5EBjh4gGm3iU/t3onKlXHIg=="
  dependencies:
    "@babel/runtime" "^7.15.4"
    "@types/lodash" "^4.14.175"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    nanoclone "^0.2.1"
    property-expr "^2.0.4"
    toposort "^2.0.2"
