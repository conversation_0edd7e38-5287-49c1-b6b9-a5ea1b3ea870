{"compilerOptions": {"baseUrl": ".", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "downlevelIteration": true, "jsx": "preserve", "paths": {"@components/*": ["src/components/*"], "@containers/*": ["src/containers/*"], "@contexts/*": ["src/contexts/*"], "@framework/gql/*": ["src/framework/graphql/*"], "@framework/*": ["src/framework/basic-rest/*"], "@settings/*": ["src/settings/*"], "@styles/*": ["src/styles/*"], "@utils/*": ["src/utils/*"], "@assets/*": ["src/assets/*"]}, "incremental": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src/utils/use-tng.js", "src/utils/meta-pixel-track.js"], "exclude": ["node_modules"]}