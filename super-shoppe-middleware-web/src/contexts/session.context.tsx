import AuthProcessor from '@components/auth/auth-processor';
import React from 'react';
import PageLoader from '@components/ui/loaders/page-loader';
import { useCustomerQuery } from '../data-graphql/customer';

export const SessionProvider: React.FC = (props) => {
  const { data, isLoading } = useCustomerQuery();

  if (isLoading || !data) {
    return <PageLoader />;
  }

  return <AuthProcessor auth={data.me}>{props.children}</AuthProcessor>;
};
