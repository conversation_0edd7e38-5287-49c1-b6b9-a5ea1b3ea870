import { CURRENCY_ENUM } from '@framework/types';
import React, { useMemo } from 'react';

export interface State {
  siteTitle: string;
  siteSubtitle: string;
  currency: string;
  logo: any;
  category: {
    category_size: number;
  };
}

const initialState: State = {
  siteTitle: 'Inventory Management System',
  siteSubtitle: '',
  currency: CURRENCY_ENUM.MY,
  logo: {
    id: 1,
    thumbnail: '/assets/images/logo.png',
    original: '/assets/images/logo.png',
  },
  category: {
    category_size: 280,
  },
};

export const SettingsContext = React.createContext<State | any>(initialState);

SettingsContext.displayName = 'SettingsContext';

export const SettingsProvider: React.FC = ({ ...props }) => {
  const [state, updateSettings] = React.useState(initialState);

  const value = useMemo(
    () => ({
      ...state,
      updateSettings,
    }),
    [state]
  );
  return <SettingsContext.Provider value={value} {...props} />;
};

export const useSettings = () => {
  const context = React.useContext(SettingsContext);
  if (context === undefined) {
    throw new Error(`useSettings must be used within a SettingsProvider`);
  }
  return context;
};
