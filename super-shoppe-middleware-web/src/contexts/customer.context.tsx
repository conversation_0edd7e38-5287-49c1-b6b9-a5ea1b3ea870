import { useCustomerQuery } from '../data-graphql/customer';
import React, { useMemo } from 'react';
export interface State {
  customerLoading: boolean;
  customer: any;
}

const initialState = {
  customerLoading: false,
  customer: null,
};

type Action =
  | {
      type: 'UPDATE_CUSTOMER_LOADING';
      payload: boolean;
    }
  | {
      type: 'UPDATE_CUSTOMER';
      payload: any;
    };

export const CustomerContext = React.createContext<State | any>(initialState);

CustomerContext.displayName = 'CustomerContext';

function customerReducer(state: State, action: Action) {
  switch (action.type) {
    case 'UPDATE_CUSTOMER_LOADING': {
      return {
        ...state,
        customerLoading: action.payload,
      };
    }
    case 'UPDATE_CUSTOMER': {
      return {
        ...state,
        customer: action.payload,
      };
    }
  }
}

export const CustomerProvider: React.FC = ({ ...props }) => {
  const { data, isLoading } = useCustomerQuery();
  const [state, dispatch] = React.useReducer(customerReducer, initialState);

  const updateCustomerLoading = (payload: boolean) =>
    dispatch({ type: 'UPDATE_CUSTOMER_LOADING', payload });
  const updateCustomer = (payload: any) =>
    dispatch({ type: 'UPDATE_CUSTOMER', payload });

  React.useEffect(() => {
    updateCustomerLoading(isLoading);
  }, [isLoading]);

  React.useEffect(() => {
    if (data) {
      updateCustomer(data.me ?? null);
    }
  }, [data]);

  const value = useMemo(
    () => ({
      ...state,
      updateCustomerLoading,
      updateCustomer,
    }),
    [state]
  );
  return <CustomerContext.Provider value={value} {...props} />;
};

export const useCustomer = () => {
  const context = React.useContext(CustomerContext);
  if (context === undefined) {
    throw new Error(`useCustomer must be used within a CustomerProvider`);
  }
  return context;
};
