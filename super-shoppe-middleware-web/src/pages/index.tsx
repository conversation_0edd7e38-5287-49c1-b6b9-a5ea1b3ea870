import React from 'react';
import { useCurrentUser } from '../hooks/use-current-user';

const HomePage = () => {
  const { user, isLoading } = useCurrentUser();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600">No user data available.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Content */}
      <div className="px-4 py-6">
        <div className="grid grid-cols-1 gap-6">
          {/* User Info Card */}
          <div className="bg-white overflow-hidden shadow-sm rounded-lg">
            <div className="px-4 py-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                User Information
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Name:</span>
                  <span className="font-medium">{user.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Email:</span>
                  <span className="font-medium">{user.email || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Phone:</span>
                  <span className="font-medium">{user.phone || 'N/A'}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Status:</span>
                  <span
                    className={`px-2 py-1 text-xs rounded ${
                      user.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {user.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Roles Card */}
          <div className="bg-white overflow-hidden shadow-sm rounded-lg">
            <div className="px-4 py-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Roles</h3>
              <div>
                {(user as any).roles && (user as any).roles.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {(user as any).roles.map((role: any) => (
                      <span
                        key={role.id}
                        className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"
                      >
                        {role.name}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No roles assigned</p>
                )}
              </div>
            </div>
          </div>

          {/* Permissions Card */}
          <div className="bg-white overflow-hidden shadow-sm rounded-lg">
            <div className="px-4 py-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Permissions
              </h3>
              <div>
                {(user as any).permissions &&
                (user as any).permissions.length > 0 ? (
                  <div className="max-h-40 overflow-y-auto">
                    <div className="flex flex-wrap gap-1">
                      {(user as any).permissions
                        .slice(0, 15)
                        .map((permission: any) => (
                          <span
                            key={permission.id}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                          >
                            {permission.name}
                          </span>
                        ))}
                      {(user as any).permissions.length > 15 && (
                        <span className="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded">
                          +{(user as any).permissions.length - 15} more
                        </span>
                      )}
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">No permissions assigned</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
