import Layout from '@components/layout/layout';
import Container from '@components/ui/container';
import Seo from '@components/seo/seo';
import Divider from '@components/ui/divider';
import OrderCancelDetails from '@components/order/order-cancel-details';
import BackButton from '@components/common/back-button';

export default function CancelOrderPage() {
  return (
    <>
      <Seo
        title="Order cancellation"
        description="View order cancellation details"
        path="/cancel-order"
      />
      <Container>
        <BackButton text="Back" className="flex" />
        <OrderCancelDetails className="p-0" />
      </Container>
      <Divider />
    </>
  );
}

CancelOrderPage.Layout = Layout;
