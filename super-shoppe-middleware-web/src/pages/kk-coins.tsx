import Layout from '@components/layout/layout';
import Container from '@components/ui/container';
import Seo from '@components/seo/seo';
import DailyCheckIn from '@components/wallet/daily-check-in';
import EarningWaysList from '@components/wallet/earning-ways-list';
import ProfileQRCode from '@components/wallet/profile-qr-code';
import VoucherList from '@components/voucher/voucher-list';

export default function KKCoinsPage() {
  return (
    <>
      <Seo title="KK Coins" description="KK Coins" path="kk-coins" />
      <Container className="border-b border-skin-base py-5">
        <ProfileQRCode />
        <hr className="my-6" />
        <DailyCheckIn />
        <hr className="my-6" />
        <EarningWaysList />
        <hr className="my-6" />
        <div id="vouchers">
          <VoucherList />
        </div>
      </Container>
    </>
  );
}

KKCoinsPage.Layout = Layout;
