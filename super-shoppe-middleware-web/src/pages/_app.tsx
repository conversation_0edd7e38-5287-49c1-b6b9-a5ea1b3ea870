import type { AppProps } from 'next/app';
import { useRouter } from 'next/router';
import { ManagedUIContext } from '@contexts/ui.context';
import ManagedModal from '@components/common/modal/managed-modal';
import ManagedDrawer from '@components/common/drawer/managed-drawer';
import { useEffect, useRef } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Hydrate } from 'react-query/hydration';
import { ToastContainer } from 'react-toastify';
import { ReactQueryDevtools } from 'react-query/devtools';
import { DefaultSeo } from '@components/seo/default-seo';
import GlobalLayout from '@components/layout/global-layout';

// external
import 'react-toastify/dist/ReactToastify.css';
import 'swiper/swiper-bundle.css';

// base css file
import '@styles/scrollbar.css';
import '@styles/swiper-carousel.css';
import '@styles/page-loader-module.css';
import '@styles/checkbox-module.css';
import '@styles/custom-plugins.css';
import 'overlayscrollbars/css/OverlayScrollbars.css';
import '@styles/tailwind.css';
import { getDirection } from '@utils/get-direction';
import { queryClientConfig } from '@settings/query.settings';
import ManagedDeviceState from '@components/common/managed-device-state';

const Noop: React.FC = ({ children }) => <>{children}</>;

const CustomApp = ({ Component, pageProps }: AppProps) => {
  const queryClientRef = useRef<any>();
  if (!queryClientRef.current) {
    queryClientRef.current = new QueryClient(queryClientConfig);
  }
  const router = useRouter();
  const dir = getDirection(router.locale);
  useEffect(() => {
    document.documentElement.dir = dir;
  }, [dir]);
  const Layout = (Component as any).Layout || Noop;
  const hideFooter = (Component as any).HideFooter || false;
  const isHome = (Component as any).IsHome || false;
  const withSearchBar = (Component as any).WithSearchBar || false;
  const withBackButton = (Component as any).WithBackButton || false;

  return (
    <QueryClientProvider client={queryClientRef.current}>
      <Hydrate state={pageProps.dehydratedState}>
        <ManagedUIContext>
          {/* <DefaultSeo /> */}
          <GlobalLayout>
            <Component {...pageProps} key={router.route} />
          </GlobalLayout>
          <ToastContainer autoClose={2000} hideProgressBar={true} />
          <ManagedDeviceState />
          <ManagedModal />
          <ManagedDrawer />
        </ManagedUIContext>
      </Hydrate>
    </QueryClientProvider>
  );
};

export default CustomApp;
