import Layout from '@components/layout/layout';
import LoginForm from '@components/auth/login-form';
import Divider from '@components/ui/divider';
import Seo from '@components/seo/seo';
import { useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { getStatus, requestPermission } from '@utils/ios-app-tracking';
import { SessionProvider } from '@contexts/session.context';

export default function SignInPage() {
  useEffect(() => {
    if (Capacitor.isNativePlatform() && Capacitor.getPlatform() === 'ios') {
      getStatus().then(({ status }) => {
        if (status != 'authorized') {
          requestPermission().then(({ status }) => {});
        }
      });
    }
  }, []);
  return (
    <>
      <Seo title="Sign In" description="Customer login page." path="signin" />
      <Divider />
      <div className="flex justify-center items-center">
        <div className="py-12 sm:py-16 lg:py-20">
          <LoginForm isPopup={false} />
        </div>
      </div>
      <Divider />
    </>
  );
}

SignInPage.Layout = Layout;
