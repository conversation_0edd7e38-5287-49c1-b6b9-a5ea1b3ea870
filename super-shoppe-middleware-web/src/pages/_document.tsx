import Document, {
  Html,
  Head,
  Main,
  NextScript,
  DocumentContext,
} from 'next/document';

const FB_PIXEL_ID = process.env.NEXT_PUBLIC_FB_PIXEL_ID;
const ENV = process.env.NEXT_PUBLIC_NODE_ENV;

export default class CustomDocument extends Document {
  static async getInitialProps(ctx: DocumentContext) {
    return Document.getInitialProps(ctx);
  }
  render() {
    return (
      <Html>
        <Head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
          <link rel="preconnect" href="https://fonts.gstatic.com" />
          <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Manrope:wght@600;700;800&display=swap"
            rel="stylesheet"
          />
          {!!FB_PIXEL_ID && (
            <>
              {/* Meta Pixel Code */}
              <script
                dangerouslySetInnerHTML={{
                  __html: `!function(f,b,e,v,n,t,s)
                    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                    n.queue=[];t=b.createElement(e);t.async=!0;
                    t.src=v;s=b.getElementsByTagName(e)[0];
                    s.parentNode.insertBefore(t,s)}(window, document,'script',
                    'https://connect.facebook.net/en_US/fbevents.js');
                    fbq('init', '${FB_PIXEL_ID}');
                    fbq('track', 'PageView');`,
                }}
              ></script>
              <noscript>
                <img height="1" width="1" style={{ display: "none" }}
                  src={`https://www.facebook.com/tr?id=${FB_PIXEL_ID}&ev=PageView&noscript=1`}
                />
              </noscript>
              {/* End Meta Pixel Code */}
            </>
          )}
          <script
            id="tngwebviewscript"
            src="/assets/scripts/web-view.min.js"
          ></script>
          <script
            dangerouslySetInnerHTML={{
              __html: `if (my) {
                document.writeln('<script' + '>' + 'const tngwebviewmy = my;' + '<' + '/' + 'script>');
              }`,
            }}
          ></script>
          {ENV !== 'production' && (
            <>
              <script src="//cdn.jsdelivr.net/npm/eruda"></script>
              <script>eruda.init();</script>
            </>
          )}
        </Head>
        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
