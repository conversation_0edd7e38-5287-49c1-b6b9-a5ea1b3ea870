import React, { useEffect, useState } from 'react';
import Seo from '@components/seo/seo';
import { APIProvider } from '@vis.gl/react-google-maps';
import Layout from '@components/layout/layout';
import { GoogleAPISettingsData, LatLng, Store } from '@framework/types';
import Container from '@components/ui/container';
import { ImSpinner2 } from 'react-icons/im';
import StoreList from '@components/stores/store-list';
import LocationsMap from '@components/stores/locations-map';
import StoreFilterHead from '@components/stores/store-filter-head';
import StoreInitCredentialsLoader from '@components/stores/store-credentials-loader';
import { useStoresQuery } from '@framework/gql/store/get-all-stores';
import { FormValues as StoreSearchValues } from '@components/stores/store-filter-view';

const DEFAULT_ZOOM = 16;
const NATIONWIDE_ZOOM = 14;

const Stores = () => {
  const [currentZoom, setCurrentZoom] = useState<number>(DEFAULT_ZOOM);
  const [selectAll, setSelectAll] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Store | null>(null);
  const [currentPosition, setCurrentPosition] = useState<LatLng | null>(null);
  const [filters, setFilters] = useState<StoreSearchValues>();

  const { data, fetchNextPage, isFetchingNextPage, isFetching, isLoading, hasNextPage } =
    useStoresQuery({
      ...(filters?.name || filters?.state ? {
        text: filters.name,
        state_id: filters.state?.id,
      } : {
        center: selectAll ? undefined : currentPosition!,
        orderByDistance: selectAll ? currentPosition : undefined,
      }),
      limit: 99999
    }, { enabled: !!currentPosition });

  useEffect(() => {
    if (!selectAll && currentZoom <= NATIONWIDE_ZOOM) {
      setSelectAll(true);
    }
  }, [currentZoom]);

  function handleClearFilters() {
    setFilters(undefined);
  }

  return (
    <>
      <Seo title="Locations" description="Locations" path="locations" />
      <Container
        className="mx-auto max-w-[1920px] bg-gray-100 pb-4 space-y-4"
        clean
      >
        <StoreInitCredentialsLoader>
          {({ google_api_settings }: { google_api_settings: GoogleAPISettingsData; }) => (
            <div
              id="store-list"
              className="flex flex-col-reverse lg:flex-row lg:h-[600px] xl:h-[700px]"
            >
              <div className="lg:w-1/3 flex flex-col lg:h-full h-[600px] flex-auto ">
                <StoreFilterHead filters={filters} onFilterChange={setFilters} />
                {filters?.name || filters?.state?.name ? (
                  <div>
                    <div className="flex flex-wrap items-center justify-center mb-1">
                      <p className="text-sm text-black">Currently searching:</p>
                    </div>
                    <div className="flex flex-wrap items-center justify-center gap-1 mb-1">
                      {filters.name ? (
                        <span className="flex flex-shrink-0 items-center border border-skin-base bg-white rounded-lg text-13px px-2.5 py-1.5 capitalize text-skin-base">
                          Location/Road: {filters.name}
                        </span>
                      ) : <></>}
                      {filters.state?.name ? (
                        <span className="flex flex-shrink-0 items-center border border-skin-base bg-white rounded-lg text-13px px-2.5 py-1.5 capitalize text-skin-base">
                          State: {filters.state.name}
                        </span>
                      ) : <></>}
                    </div>
                    <div className="flex flex-wrap items-center justify-center mb-2">
                      <p className="text-sm text-skin-primary underline cursor-pointer transition duration-200 ease-in-out hover:text-gray-600" onClick={handleClearFilters}>Clear search</p>
                    </div>
                  </div>
                ) : <></>}
                {isLoading ? (
                  <>
                    <div className="flex items-center justify-center">
                      <ImSpinner2 className="animate-spin h-5 w-5 text-skin-primary" />
                    </div>
                  </>
                ) : (
                  <StoreList
                    stores={data}
                    selectedLocation={selectedLocation}
                    setSelectedLocation={setSelectedLocation}
                    filters={filters}
                    isLoading={isFetching || isLoading}
                    loadMoreItems={() => {
                      hasNextPage && fetchNextPage();
                    }}
                    isLoadMoreLoading={isFetchingNextPage}
                  />
                )}
              </div>

              {google_api_settings?.api_key && google_api_settings?.map_id ? (
                <APIProvider apiKey={google_api_settings.api_key}>
                  <LocationsMap
                    mapId={google_api_settings.map_id}
                    stores={data}
                    selectedLocation={selectedLocation}
                    setSelectedLocation={setSelectedLocation}
                    setCurrentPosition={setCurrentPosition}
                    defaultZoom={DEFAULT_ZOOM}
                    setZoom={setCurrentZoom}
                  />
                </APIProvider>
              ) : <></>}
            </div>
          )}
        </StoreInitCredentialsLoader>
      </Container>
    </>
  );
};
export default Stores;
Stores.Layout = Layout;
