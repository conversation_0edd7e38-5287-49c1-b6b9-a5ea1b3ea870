import Layout from '@components/layout/layout';
import OrderDetails from '@components/order/order-details';
import Container from '@components/ui/container';
import Seo from '@components/seo/seo';
import Divider from '@components/ui/divider';
import BackButton from '@components/common/back-button';

export default function TrackOrderPage() {
  return (
    <>
      <Seo
        title="Track order"
        description="Track order status and details"
        path="/track-order/view"
      />
      <Container>
        <BackButton text="Back" className="flex" />
        <OrderDetails className="p-0" />
      </Container>
      <Divider />
    </>
  );
}

TrackOrderPage.Layout = Layout;
