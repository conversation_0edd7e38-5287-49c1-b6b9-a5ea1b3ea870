import Container from '@components/ui/container';
import Layout from '@components/layout/layout';
import Seo from '@components/seo/seo';
import PageHeroSection from '@components/ui/page-hero-section';
import { useStaticBlockQuery } from '@framework/static-block/get-static-block.query';
import PageLoader from '@components/ui/loaders/page-loader';
import ErrorInformation from '@components/404/error-information';
import { useRouter } from 'next/router';

interface CustomPageProps {}

const CustomPage = ({}: CustomPageProps) => {
  const {
    query: { _s: slug },
  } = useRouter();

  if (!slug) {
    return <PageLoader />;
  }

  const {
    data: page_details,
    isLoading,
    error,
  } = useStaticBlockQuery(slug as string);
  if (error) {
    return <ErrorInformation />;
  }

  if (isLoading || !page_details) {
    return <PageLoader />;
  }

  return (
    <>
      <Seo
        title={page_details.name}
        description={page_details.description ?? ''}
        path={page_details.key}
      />
      {/* <PageHeroSection
        heroTitle={page_details.name}
        custom_breadcrumbs={[
          { href: page_details.key, name: page_details.name as string },
        ]}
      /> */}
      <div className="py-12 lg:py-16 2xl:py-20">
        <Container>
          <div
            key={page_details.name}
            className="w-full xl:max-w-[1200px] mx-auto mb-8 lg:mb-12 last:mb-0 order-list-enable"
          >
            <div
              className="w-full"
              dangerouslySetInnerHTML={{
                __html: page_details.value ?? '',
              }}
            />
          </div>
        </Container>
      </div>
    </>
  );
};

CustomPage.Layout = Layout;
CustomPage.WithBackButton = true;

export default CustomPage;
