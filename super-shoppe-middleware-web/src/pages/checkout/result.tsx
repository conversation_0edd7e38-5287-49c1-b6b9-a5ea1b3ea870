import { ROUTES } from '@utils/routes';
import { useQueryClient } from 'react-query';
import { useEffect, useState } from 'react';
import { useCheckout } from '@contexts/checkout.context';
import { useRouter } from 'next/router';
import Layout from '@components/layout/layout';
import Seo from '@components/seo/seo';
import PageLoader from '@components/ui/loaders/page-loader';
import CheckoutResult from '@components/checkout/checkout-result';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import Container from '@components/ui/container';
import { getStorage, STORAGE_KEY_ENUM } from '@utils/use-local-storage';

export default function CheckoutSuccessPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [order_id, setOrderId] = useState(null);

  const { clearCheckoutData } = useCheckout();

  useEffect(() => {
    getStorage(STORAGE_KEY_ENUM.CHECKOUT, null)
      .then((checkout: any) => {
        const { current_order_id } = checkout ?? { current_order_id: null };
        if (!current_order_id) {
          router.push(ROUTES.HOME);
        } else {
          queryClient.invalidateQueries(API_ENDPOINTS.ME);
          queryClient.invalidateQueries(API_ENDPOINTS.CART);
          clearCheckoutData();
          setOrderId(current_order_id);
        }
      });
  }, []);

  return (
    <>
      <Seo
        title="Order result"
        description="Order Result"
        path="checkout/result"
      />
      <Container>
        {order_id ? <CheckoutResult order_id={order_id} /> : <PageLoader />}
      </Container>
    </>
  );
}
CheckoutSuccessPage.Layout = Layout;
