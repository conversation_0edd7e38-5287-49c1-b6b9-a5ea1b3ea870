import AutoSubmitForm from '@components/common/auto-submit-form';
import Seo from '@components/seo/seo';
import PageLoader from '@components/ui/loaders/page-loader';
import { decodeSearchString } from '@framework/utils/data-mappers';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

interface WebViewPaymentProps {
    url: string;
    data: string;
    method: string;
    form_submit: string;
}

export const WebViewPayment: React.FC<WebViewPaymentProps> = ({ url, data, method, form_submit }) => {
    const payment_url = decodeSearchString(url);
    const payment_data = data ? JSON.parse(data as string) : {};

    useEffect(() => {
        if(form_submit === "0") {
            window.location.assign(payment_url);
        }
    }, [form_submit]);

    return (
        form_submit === "1" ? (
            <AutoSubmitForm hidden method={method} action={payment_url}>
                {Object.keys(payment_data).map((key: string, idx) => (
                    <input
                        key={idx}
                        hidden
                        readOnly
                        name={key}
                        value={payment_data[key]}
                    />
                ))}
            </AutoSubmitForm>
        ) : (
            <></>
        )
    );
}

export default function WebViewPaymentPage() {
    const router = useRouter();
    const { query } = router;
    const { pay_url, pay_data, pay_method, form: is_form } = query;

    return (
        <>
            <Seo
                title="Payment processing in webview"
                description="Payment processing in webview"
                path="checkout/web-payment"
            />
            <PageLoader />
            {(pay_url && pay_data && pay_method && is_form !== undefined) && (
                <WebViewPayment
                    url={pay_url as string}
                    data={pay_data as string}
                    method={pay_method as string}
                    form_submit={is_form as string}
                />
            )}
        </>
    );
}
