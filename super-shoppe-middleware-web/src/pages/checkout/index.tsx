import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Layout from '@components/layout/layout';
import Container from '@components/ui/container';
import Divider from '@components/ui/divider';
import Seo from '@components/seo/seo';
import Link from '@components/ui/link';
import { ROUTES } from '@utils/routes';
import { useCart } from '@contexts/cart/cart.context';
import { useCheckout } from '@contexts/checkout.context';
import { useModalAction } from '@components/common/modal/modal.context';
import { useQueryClient } from 'react-query';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { getIsTNG, useTNGMy } from '@utils/use-tng';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';
import OverlayLoader from '@components/ui/loaders/overlay-loader';
import { Capacitor } from '@capacitor/core';
import { getStatus, requestPermission } from '@utils/ios-app-tracking';

const EmptyCart = dynamic(() => import('@components/cart/empty-cart'), {
  ssr: false,
});

const CheckoutDetails = dynamic(() => import('@components/checkout/checkout-details'), {
  ssr: false,
});

export default function CheckoutPage() {
  const { items, isEmpty } = useCart();
  const [is_init, setIsInit] = useState(false);
  const router = useRouter();
  const { checkout_loading } = useCheckout();
  const tngmy = useTNGMy();
  const queryClient = useQueryClient();
  const { openModal } = useModalAction();

  useEffect(() => {
    setIsInit(true);
    if (getIsTNG()) {
      try {
        tngmy.postMessage({
          type: 'SS_USER_INFO',
        });
      } catch (e: any) {
        toast.error('An error has occured, please try again later', {
          autoClose: 5000,
        });
        router.push(ROUTES.HOME);
      }
    }
    else if(Capacitor.isNativePlatform() && Capacitor.getPlatform() === "ios") {
      getStatus()
        .then(({ status }) => {
          if(status != "authorized") {
            requestPermission()
              .then(({ status }) => {});
          }
        });
    }
  }, []);

  useEffect(() => {
    if (items.filter((item) => item.checkout).length < 1) {
      queryClient.invalidateQueries(API_ENDPOINTS.CART);
      return openModal('INVALID_CHECKOUT', null, false);
    }
  }, [items]);

  return (
    <>
      <Seo title="Checkout" description="Checkout page" path="checkout" />
      {checkout_loading && <OverlayLoader />}
      <Container
        className="mx-auto max-w-[1920px] px-0 md:px-4 lg:px-8 2xl:px-10 py-2 2xl:py-4 border-t border-skin-base bg-skin-thumbnail checkout"
        clean
      >
        <div className="flex xl:max-w-screen-xl mx-auto flex-col">
          {isEmpty ? (
            <>
              <EmptyCart />
              <h4 className="text-center mt-4 font-semibold">
                Click{' '}
                <Link href={ROUTES.HOME} className="text-skin-primary">
                  here
                </Link>{' '}
                to continue shopping.
              </h4>
            </>
          ) : (
            is_init && (
              <div className="w-full">
                <CheckoutDetails />
              </div>
            )
          )}
        </div>
      </Container>
      <Divider />
    </>
  );
}

CheckoutPage.Layout = Layout;
