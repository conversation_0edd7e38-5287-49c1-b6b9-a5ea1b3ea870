import PageLoader from '@components/ui/loaders/page-loader';
import Seo from '@components/seo/seo';
import { useCheckout } from '@contexts/checkout.context';
import TNGCallback from '@components/common/tng-callback';
import { useOrderPaymentStatusQuery } from '@framework/order/use-order-payment.query';
import { useEffect } from 'react';
import { useTNGMy } from '@utils/use-tng';
import { useRouter } from 'next/router';
import { ROUTES } from '@utils/routes';

export default function CheckoutCallbackPage() {
  const tngmy = useTNGMy();

  return (
    <>
      <Seo
        title="Payment processing"
        description="Processing order payment"
        path="checkout/callback"
      />
      <PageLoader />
      {!tngmy ? (
        <PaymentProcessCallback />
      ) : (
        <TNGProcessCallback />
      )}
    </>
  );
}

export const PaymentProcessCallback = () => {
  const router = useRouter();

  useEffect(() => {
    setTimeout(() => {
      router.push(ROUTES.CHECKOUT_RESULT);
    }, 3000);
  }, []);

  return <></>;
}

export const TNGProcessCallback = () => {
  const { current_order_id } = useCheckout();

  const queryResult = useOrderPaymentStatusQuery(current_order_id);

  return <TNGCallback result={queryResult} />;
}
