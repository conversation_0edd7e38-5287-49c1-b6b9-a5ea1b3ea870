import AutoSubmitForm from '@components/common/auto-submit-form';
import OpenPaymentWebview from '@components/common/open-payment-webview';
import PaymentPopup from '@components/common/payment-popup';
import PromptTNGPayment from '@components/common/prompt-tng-payment-form';
import Seo from '@components/seo/seo';
import PageLoader from '@components/ui/loaders/page-loader';
import { useCheckout } from '@contexts/checkout.context';
import { useOrderPaymentQuery } from '@framework/order/use-order-payment.query';
import { ROUTES } from '@utils/routes';
import { useTNGMy } from '@utils/use-tng';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';

export default function CheckoutPaymentPage() {
  const router = useRouter();
  const tngmy = useTNGMy();

  const { current_order_id } = useCheckout();

  const {
    data,
    isLoading: loading,
    error,
  } = useOrderPaymentQuery(current_order_id! as string);
  if (loading) {
    return <PageLoader />;
  }
  if (error) {
    toast.warning('Cannot process payment');
    router.replace(ROUTES.HOME);
    return <PageLoader />;
  }

  if (data && data.payment_required === false) {
    router.replace(ROUTES.CHECKOUT_RESULT);
    return <PageLoader />;
  }

  if (!data.status && (!data.payload || !data.payment_url)) {
    toast.warning('Failed to process payment');
    router.replace(ROUTES.HOME);
    return <PageLoader />;
  }

  const { payload, form_submit, open_webview, payment_url, form_method } = data;

  return (
    <>
      <Seo
        title="Payment processing"
        description="Payment processing"
        path="checkout/payment"
      />
      <PageLoader />
      {!tngmy ? (
        <>
        {open_webview ? (
          <OpenPaymentWebview
            url={payment_url}
            data={payload}
            method={form_method}
            form_submit={form_submit}
          />
        ) : (
          form_submit ? (
            <AutoSubmitForm hidden method={form_method} action={payment_url}>
              {Object.keys(payload).map((key: string, idx) => (
                <input
                  key={idx}
                  hidden
                  readOnly
                  name={key}
                  value={payload[key]}
                />
              ))}
            </AutoSubmitForm>
          ) : (
            <PaymentPopup url={payment_url} />
          )
        )}
        </>
      ) : (
        <PromptTNGPayment redirect_url={payment_url} />
      )}
    </>
  );
}
