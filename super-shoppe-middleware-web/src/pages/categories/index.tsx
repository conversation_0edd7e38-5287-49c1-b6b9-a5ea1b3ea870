import Container from '@components/ui/container';
import Layout from '@components/layout/layout';
import { Element } from 'react-scroll';
import Seo from '@components/seo/seo';
import { useNavCategoriesQuery } from '@framework/category/get-all-categories';
import { Category } from '@framework/types';
import PageLoader from '@components/ui/loaders/page-loader';
import ErrorInformation from '@components/404/error-information';
import CategoryLanding from '@components/category/category-landing-container';
import CategoryLandingLayout from '@components/category/category-landing-layout';

interface CategoryListingPageProps {
  category: Category;
}

const CategoryListingPage = ({}: CategoryListingPageProps) => {
  const {
    data: categoriesData,
    isLoading,
    error: categoryError,
  } = useNavCategoriesQuery({});

  if (!isLoading && categoryError) {
    return <ErrorInformation />;
  }

  return (
    <>
      <Seo
        title="Categories"
        description="Categories page."
        path="categories"
      />
      <Container className="mx-auto max-w-[1920px] safe-area-bottom" clean>
        <CategoryLandingLayout>
          {isLoading || !categoriesData ? (
            <PageLoader />
          ) : (
            <Element name="grid" className="flex pb-16 lg:pb-0">
              <CategoryLanding data={categoriesData.categories.data} />
            </Element>
          )}
        </CategoryLandingLayout>
      </Container>
    </>
  );
};

CategoryListingPage.Layout = Layout;
CategoryListingPage.HideFooter = true;
CategoryListingPage.WithSearchBar = true;

export default CategoryListingPage;
