import Container from '@components/ui/container';
import Layout from '@components/layout/layout';
import { ShopFilters } from '@components/search/filters';
import { Element } from 'react-scroll';
import Seo from '@components/seo/seo';
import {
  useCategoriesQuery,
  useSearchFilterCategoriesQuery,
} from '@framework/category/get-all-categories';
import { Category } from '@framework/types';
import { useRouter } from 'next/router';
import PageLoader from '@components/ui/loaders/page-loader';
import { generateBreadcrumbFromCategory } from '@utils/use-breadcrumb';
import CategoryListCardLoader from '@components/ui/loaders/category-list-card-loader';
import ErrorInformation from '@components/404/error-information';
import Link from '@components/ui/link';
import { ROUTES } from '@utils/routes';
import { IoIosArrowBack } from 'react-icons/io';
import dynamic from 'next/dynamic';

const Breadcrumb = dynamic(() => import('@components/ui/breadcrumb'));
const CategoryList = dynamic(() => import('@components/main/category-list'));
const CategoryBannerSection = dynamic(() => import('@components/category/category-banner-section'));
const ProductListingSection = dynamic(() => import('@components/product/product-listing-section'));

interface CategoryPageProps {
  category: Category;
}

const CategoryPage = ({}: CategoryPageProps) => {
  const router = useRouter();

  const { query } = router;
  const {
    data,
    isLoading: filterLoading,
    error: searchFilterError,
  } = useSearchFilterCategoriesQuery({
    category_slugs: query?.categories,
    ...(query?.text || query?.category || query?.brand
      ? {
          keyword: query?.text as string,
          category_id: query?.category as string,
          brand_id: query?.brand as string,
        }
      : {}),
    limit: 30,
  });

  const {
    data: categoriesData,
    isLoading,
    error: categoryError,
  } = useCategoriesQuery({
    category_slugs: query?.categories,
    limit: 30,
  });

  if (!isLoading && !filterLoading && (searchFilterError || categoryError)) {
    return <ErrorInformation />;
  }

  return (
    <>
      <Seo
        title="Catalog"
        description="Catalog page."
        path="/categories/view"
      />
      <Container>
        {isLoading || !categoriesData ? (
          <PageLoader />
        ) : (
          <Element name="grid" className="pt-7 lg:pt-11 pb-16 lg:pb-20">
            {/* <div className="mb-4 md:mb-6">
              <Link
                href={ROUTES.CATEGORY}
                className="flex md:hidden items-center text-xs text-skin-primary gap-2"
              >
                <IoIosArrowBack />
                Categories
              </Link>
              <Link
                href={ROUTES.CATEGORY}
                className="hidden md:flex items-center text-sm text-skin-primary gap-2"
              >
                <IoIosArrowBack />
                Back to Categories
              </Link>
            </div> */}
            <div className="flex relative">
              <div className="flex-shrink-0 pe-8 hidden lg:block w-1/3 xl:w-1/4 sticky top-24 h-full">
                <ShopFilters />
              </div>
              <div className="w-full lg:w-2/3 xl:w-3/4 lg:-mt-1">
                <div className="mb-2">
                  <Breadcrumb
                    custom_breadcrumbs={
                      categoriesData.categories.data &&
                      categoriesData.categories.data.length > 0
                        ? generateBreadcrumbFromCategory(
                            categoriesData.categories.data[0]
                          )
                        : []
                    }
                  />
                </div>
                <CategoryBannerSection
                  bannerData={
                    categoriesData.categories.data &&
                    categoriesData.categories.data.length > 0 &&
                    categoriesData.categories.data[0].gallery
                      ? categoriesData.categories.data[0].gallery
                      : []
                  }
                />
                {/* <div className="block lg:hidden mb-6">
                  {filterLoading || !data ? (
                    <div className="">
                      <div className="w-72 mt-8 px-2">
                        <CategoryListCardLoader uniqueKey="category-list-card-loader" />
                      </div>
                    </div>
                  ) : (
                    <CategoryList
                      type="horizontal"
                      data={{
                        categories: { data: [
                          ...data.categories.data.categories,
                          ...data.categories.data.brands,
                        ] },
                      }}
                      className="border rounded-md border-skin-base"
                    />
                  )}
                </div> */}
                {!filterLoading && data ? (
                  <ProductListingSection
                    defaultSorting={
                      data.categories.data.current_category
                        ? data.categories.data.current_category.meta
                          ? data.categories.data.current_category.meta
                              .default_sorting
                          : null
                        : null
                    }
                  />
                ) : (
                  <></>
                )}
              </div>
            </div>
          </Element>
        )}
      </Container>
    </>
  );
};

CategoryPage.Layout = Layout;
CategoryPage.WithSearchBar = true;

export default CategoryPage;
