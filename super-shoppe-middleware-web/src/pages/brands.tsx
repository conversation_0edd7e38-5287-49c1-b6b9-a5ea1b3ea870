import Layout from '@components/layout/layout';
import Seo from '@components/seo/seo';
import Container from '@components/ui/container';
import SortedBrandList from '@components/brand/sorted-brand-list';
import { useBrandCategoriesQuery } from '@framework/category/get-all-categories';
import PageLoader from '@components/ui/loaders/page-loader';
import CategoryLandingLayout from '@components/category/category-landing-layout';

const BrandsPage = () => {
  const { data: brandData, isLoading } = useBrandCategoriesQuery();
  return (
    <>
      <Seo title="Official Brands" description="Brands list" path="brands" />
      <Container className="mx-auto max-w-[1920px]" clean>
        <CategoryLandingLayout>
          {!isLoading && brandData ? (
            <div className="pb-16 lg:pb-20">
              <SortedBrandList brandCategories={brandData.categories.data} />
            </div>
          ) : (
            <PageLoader />
          )}
        </CategoryLandingLayout>
      </Container>
    </>
  );
};

BrandsPage.Layout = Layout;
BrandsPage.HideFooter = true;

export default BrandsPage;
