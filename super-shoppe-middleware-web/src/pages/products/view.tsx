import Container from '@components/ui/container';
import Layout from '@components/layout/layout';
import ProductSingleDetails from '@components/product/product';
import RelatedProductFeed from '@components/product/feeds/related-product-feed';
import Breadcrumb from '@components/ui/breadcrumb';
import Divider from '@components/ui/divider';
import { useRouter } from 'next/router';
import { useProductQuery } from '@framework/product/get-product';
import Seo from '@components/seo/seo';
import { ROUTES } from '@utils/routes';
import PageLoader from '@components/ui/loaders/page-loader';
import ErrorInformation from '@components/404/error-information';

const ProductPage = () => {
  const router = useRouter();
  const { query } = router;

  const {
    data: current_product,
    isLoading,
    error,
  } = useProductQuery({
    slug: query?.slug as string,
    _details: true,
  });

  if (!isLoading && error) {
    return <ErrorInformation />;
  }

  return (
    <>
      <Seo
        title={current_product ? current_product.name : 'Product details page'}
        description={
          current_product ? current_product.description : 'Product description'
        }
        path={`${ROUTES.PRODUCTS_VIEW}/${
          query && query.slug ? query.slug : ''
        }`}
        images={
          current_product
            ? current_product.gallery
              ? current_product.gallery.map(({ original }, index) => ({
                  url: original,
                  alt: `${current_product.name} Image ${index + 1}`,
                }))
              : current_product.image
              ? [
                  {
                    url: current_product.image.original,
                    alt: `${current_product.name} Image`,
                  },
                ]
              : []
            : []
        }
        additionalMetaTags={[]}
      />
      <Divider />
      {!current_product || isLoading ? (
        <PageLoader />
      ) : (
        <>
          <div className="px-4 md:px-12 lg:px-36 pt-6 lg:pt-7">
            <Container className="mx-auto max-w-[1920px]" clean={true}>
              <Breadcrumb
                custom_breadcrumbs={[
                  { href: router.asPath, name: current_product.name },
                ]}
              />
              <ProductSingleDetails data={current_product} />
            </Container>
          </div>

          {/* <RelatedProductFeed
            relatedProductsData={
              current_product?.related_products
                ? current_product?.related_products
                : []
            }
            uniqueKey="related-products"
            className="md:px-12 lg:px-36 2xl:ps-36 2xl:pe-36 4xl:ps-36 4xl:pe-36"
          /> */}
        </>
      )}
    </>
  );
};

ProductPage.Layout = Layout;
ProductPage.WithSearchBar = true;
ProductPage.WithBackButton = true;

export default ProductPage;
