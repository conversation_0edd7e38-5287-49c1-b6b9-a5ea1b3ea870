import ErrorInformation from '@components/404/error-information';
import Layout from '@components/layout/layout';
import ProductSingleDetails from '@components/product/product';
import Seo from '@components/seo/seo';
import Breadcrumb from '@components/ui/breadcrumb';
import Container from '@components/ui/container';
import Divider from '@components/ui/divider';
import PageLoader from '@components/ui/loaders/page-loader';
import { usePreviewProductQuery } from '@framework/product/get-product-preview';
import { ROUTES } from '@utils/routes';
import { useRouter } from 'next/router';

const ProductPage = ({}: {}) => {
  const router = useRouter();
  const { query } = router;

  const {
    data: current_product,
    isLoading,
    error,
  } = usePreviewProductQuery(query.key as string);

  if (!isLoading && error) {
    return <ErrorInformation />;
  }

  return (
    <>
      <Seo
        title={current_product ? current_product.name : 'Product details page'}
        description={
          current_product ? current_product.description : 'Product description'
        }
        path={`${ROUTES.PRODUCTS_PREVIEW}/${
          query && query.key ? query.key : ''
        }`}
        images={
          current_product && current_product.gallery
            ? current_product.gallery.map(({ original }, index) => ({
                url: original,
                alt: `${current_product.name} Image ${index + 1}`,
              }))
            : current_product && current_product.image
            ? [
                {
                  url: current_product.image.original,
                  alt: `${current_product.name} Image`,
                },
              ]
            : []
        }
        additionalMetaTags={[]}
      />
      <Divider />
      {!current_product || isLoading ? (
        <PageLoader />
      ) : (
        <>
          <div className="px-4 md:px-12 lg:px-36 pt-6 lg:pt-7">
            <Container className="mx-auto max-w-[1920px]" clean={true}>
              <Breadcrumb
                custom_breadcrumbs={[
                  { href: ROUTES.PRODUCTS, name: 'Products' },
                  { href: router.asPath, name: current_product.name },
                ]}
              />
              <ProductSingleDetails data={current_product} preview />
            </Container>
          </div>
        </>
      )}
    </>
  );
};

ProductPage.Layout = Layout;

export default ProductPage;
