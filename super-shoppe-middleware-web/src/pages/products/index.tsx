import Container from '@components/ui/container';
import Layout from '@components/layout/layout';
import { ShopFilters } from '@components/search/filters';
import { Element } from 'react-scroll';
import Divider from '@components/ui/divider';
import Seo from '@components/seo/seo';
import ProductListingSection from '@components/product/product-listing-section';
import Breadcrumb from '@components/ui/breadcrumb';

export default function Search() {
  return (
    <>
      <Seo title="Search" description="Products search" path="search" />
      <Divider />
      <Container>
        <Element
          name="grid"
          className="relative flex pt-7 lg:pt-11 pb-16 lg:pb-20"
        >
          {/* <div className="flex-shrink-0 pe-8 xl:pe-16 hidden lg:block w-80 xl:w-96 sticky top-20 h-full">
            <ShopFilters />
          </div> */}
          <div className="w-full lg:-ms-2 xl:-ms-8 lg:-mt-1">
            <div className="mb-6">
              <Breadcrumb />
            </div>
            <ProductListingSection />
          </div>
        </Element>
      </Container>
    </>
  );
}

Search.Layout = Layout;
Search.WithSearchBar = true;
