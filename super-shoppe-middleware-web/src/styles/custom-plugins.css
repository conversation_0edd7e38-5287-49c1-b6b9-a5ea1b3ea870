@font-face {
  font-family: 'Lato';
  src: url('/assets/fonts/Lato.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Lato Bold';
  src: url('/assets/fonts/Lato-Bold.ttf');
  font-weight: bold;
  font-style: normal;
}

.font-lato {
  font-family: 'Lato';
}

.font-lato-bold {
  font-family: 'Lato Bold';
}

/* Toastify */
/* .Toastify .fancy-progress-bar {
  background: rgba(255, 255, 255, 0.45);
}
.Toastify .Toastify__toast {
  background-color: #02b290;
  font-family: 'Inter', sans-serif;
}
.Toastify .Toastify__toast .Toastify__toast-body,
.Toastify .Toastify__toast .Toastify__close-button {
  color: #ffffff;
}
.Toastify .Toastify__toast .Toastify__close-button {
  opacity: 0.7;
}
.Toastify .Toastify__toast .Toastify__close-button:hover {
  opacity: 1;
} */

.Toastify .Toastify__toast-container--top-left,
.Toastify .Toastify__toast-container--top-center,
.Toastify .Toastify__toast-container--top-right {
  top: calc(1em + var(--safe-area-inset-top));
}

@media (max-width: 480px) {
  .Toastify .Toastify__toast-container--top-left,
  .Toastify .Toastify__toast-container--top-center,
  .Toastify .Toastify__toast-container--top-right {
    top: var(--safe-area-inset-top);
  }
}

/* Drawer */
.drawer .drawer-content-wrapper {
  width: 100%;
}
.drawer .drawer-mask {
  cursor: pointer;
}
@media (min-width: 500px) {
  .drawer .drawer-content-wrapper {
    width: 500px;
  }
}

/*
* React Date Picker
*/
.react-datepicker-popper {
  z-index: 9999 !important;
}
.react-datepicker-wrapper {
  width: 100%;
  display: flex;
}
.react-datepicker-wrapper .react-datepicker__input-container input {
  font-size: 14px;
  color: #1f2937;
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  position: relative;
  border-radius: 5px;
  background-color: #fff;
  border: 1px solid rgb(var(--color-border-two));
}
.react-datepicker-wrapper .react-datepicker__input-container input:disabled {
  background-color: rgb(243, 244, 246);
}
.react-datepicker-wrapper .react-datepicker__input-container input:focus {
  outline: none;
  box-shadow: none;
  border-width: 2px;
  border-color: rgb(var(--color-primary));
}
.react-datepicker-wrapper
  .react-datepicker__input-container
  .react-datepicker__close-icon::after {
  background-color: rgb(var(--color-primary));
}
.react-datepicker-popper[data-placement^='bottom'] {
  margin-top: 5px !important;
}
.react-datepicker-popper[data-placement^='top'] {
  margin-bottom: 5px !important;
}
.react-datepicker {
  /* font-family: "Open Sans" !important; */
  font-size: 13px !important;
  background-color: #fff !important;
  color: #6b7280 !important;
  border: 1px solid rgb(var(--color-border-two)) !important;
  border-radius: 5px !important;
  direction: ltr;
}
.react-datepicker .react-datepicker__triangle {
  display: none;
}
.react-datepicker .react-datepicker__navigation {
  /* background: none;
  line-height: 1.7rem;
  text-align: center;
  cursor: pointer;
  position: absolute; */
  top: 12px;
  /* padding: 0;
  border: 0;
  z-index: 1;
  height: 10px;
  width: 8px;
  display: flex;
  background-repeat: no-repeat;
  background-position: center center;
  text-indent: -999em;
  font-size: 1px;
  overflow: hidden;
  outline: 0;
  box-shadow: none; */
}
/* .react-datepicker .react-datepicker__navigation--previous {
  background-image: url("/arrow-previous.svg");
  left: 19px;
}
.react-datepicker .react-datepicker__navigation--next {
  background-image: url("/arrow-next.svg");
  right: 19px;
} */
.react-datepicker .react-datepicker__month-container {
  padding: 5px;
}
.react-datepicker .react-datepicker__header {
  text-align: center;
  background-color: transparent;
  border-bottom: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  padding-top: 9px;
  position: relative;
}
.react-datepicker .react-datepicker__header .react-datepicker__current-month {
  font-size: 13px;
  color: #1f2937;
  font-weight: 600;
}
.react-datepicker .react-datepicker__header .react-datepicker__day-names {
  margin-top: 5px;
}
.react-datepicker .react-datepicker__header .react-datepicker__day-name,
.react-datepicker .react-datepicker__header .react-datepicker__day,
.react-datepicker .react-datepicker__header .react-datepicker__time-name {
  text-transform: uppercase;
  font-size: 11px;
  color: #6b7280;
  display: inline-block;
  width: 24px;
  font-weight: 600;
  line-height: 24px;
  text-align: center;
  margin: 2px;
}
.react-datepicker .react-datepicker__month {
  margin: 0 4px;
}
.react-datepicker .react-datepicker__month .react-datepicker__day-name,
.react-datepicker .react-datepicker__month .react-datepicker__day,
.react-datepicker .react-datepicker__month .react-datepicker__time-name {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  display: inline-block;
  width: 24px;
  line-height: 24px;
  text-align: center;
  margin: 2px;
  border-radius: 50%;
  box-shadow: none;
  outline: 0;
}
.react-datepicker .react-datepicker__month .react-datepicker__day--today,
.react-datepicker .react-datepicker__month .react-datepicker__month-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--today,
.react-datepicker .react-datepicker__month .react-datepicker__year-text--today {
  font-weight: 700;
  color: rgb(var(--color-primary));
  background-color: #fff;
}
.react-datepicker .react-datepicker__month .react-datepicker__day:hover,
.react-datepicker .react-datepicker__month .react-datepicker__month-text:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text:hover,
.react-datepicker .react-datepicker__month .react-datepicker__year-text:hover {
  background-color: #dfdfdf;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--keyboard-selected,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--keyboard-selected,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--keyboard-selected,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--keyboard-selected {
  background-color: #dfdfdf;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--keyboard-selected.react-datepicker__day--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--keyboard-selected.react-datepicker__day--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--keyboard-selected.react-datepicker__day--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--keyboard-selected.react-datepicker__day--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--keyboard-selected.react-datepicker__month-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--keyboard-selected.react-datepicker__month-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--keyboard-selected.react-datepicker__month-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--keyboard-selected.react-datepicker__month-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--keyboard-selected.react-datepicker__quarter-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--keyboard-selected.react-datepicker__quarter-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--keyboard-selected.react-datepicker__quarter-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--keyboard-selected.react-datepicker__quarter-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--keyboard-selected.react-datepicker__year-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--keyboard-selected.react-datepicker__year-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--keyboard-selected.react-datepicker__year-text--today,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--keyboard-selected.react-datepicker__year-text--today {
  background-color: #fff;
}
.react-datepicker .react-datepicker__month .react-datepicker__day--selected,
.react-datepicker .react-datepicker__month .react-datepicker__day--in-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--selected,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--in-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--selected,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--in-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--selected,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--in-range {
  color: #fff;
  background-color: rgb(var(--color-primary));
  opacity: 1;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--selected:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--in-range:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--selected:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--in-range:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--selected:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--in-range:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--selected:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--in-range:hover {
  color: #fff;
  background-color: rgb(var(--color-primary));
}
.react-datepicker .react-datepicker__month .react-datepicker__day--selected,
.react-datepicker .react-datepicker__month .react-datepicker__day--in-range {
  color: #fff;
  background-color: rgb(var(--color-primary));
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--selected:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--in-range:hover {
  color: #fff;
  background-color: rgb(var(--color-primary));
}
.react-datepicker .react-datepicker__month .react-datepicker__day--highlighted {
  color: #fff;
  background-color: rgb(var(--secondary));
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--highlighted:hover {
  color: #fff;
  background-color: rgb(var(--secondary));
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--in-selecting-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--in-selecting-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--in-selecting-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--in-selecting-range {
  color: #fff;
  background-color: rgb(var(--color-primary));
  opacity: 0.75;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--in-selecting-range:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--in-selecting-range:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--in-selecting-range:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--in-selecting-range:hover {
  color: #fff;
  background-color: rgb(var(--color-primary));
  opacity: 0.75;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--selecting-range-start,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--selecting-range-start,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--selecting-range-start,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--selecting-range-start {
  opacity: 1;
  color: #fff;
  background-color: rgb(var(--color-primary));
}
.react-datepicker .react-datepicker__month .react-datepicker__day--disabled,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--disabled,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--disabled,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--disabled {
  cursor: default;
  color: #a2a5ac;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--disabled:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--disabled:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--disabled:hover,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--disabled:hover {
  background-color: #fff;
}
.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--outside-month {
  opacity: 0;
  visibility: hidden;
}

.react-datepicker .react-datepicker__month .react-datepicker__day--disabled,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--disabled,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--disabled,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--disabled {
  color: #bebebe;
}

.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--disabled.react-datepicker__day--in-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__month-text--disabled.react-datepicker__day--in-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__quarter-text--disabled.react-datepicker__day--in-range,
.react-datepicker
  .react-datepicker__month
  .react-datepicker__year-text--disabled.react-datepicker__day--in-range {
  background-color: #e5e7eb;
  color: #9ca3af;
}

.react-datepicker
  .react-datepicker__month
  .react-datepicker__day--keyboard-selected.react-datepicker__day--today {
  color: #fff;
  background-color: rgb(var(--color-primary));
}

/* pagination */
.rc-pagination.order-table-pagination .rc-pagination-prev,
.rc-pagination.order-table-pagination .rc-pagination-next {
  height: 30px;
  width: 30px;
  padding: 8px 5px;
  background-color: #f3f6f9;
  border-radius: 3px;
}
.rc-pagination.order-table-pagination .rc-pagination-prev {
  margin-right: 15px;
}
.rc-pagination.order-table-pagination .rc-pagination-next {
  margin-left: 15px;
}
.rc-pagination.order-table-pagination li.rc-pagination-disabled {
  opacity: 0.3;
}
.rc-pagination.order-table-pagination li {
  height: auto;
  min-width: auto;
  border: 0;
  border-radius: 0;
}
.rc-pagination.order-table-pagination li svg {
  width: 100%;
}
.rc-pagination.order-table-pagination li.rc-pagination-item-active a {
  opacity: 1;
}
.rc-pagination.order-table-pagination li a {
  padding: 0;
  color: rgba(0, 0, 0, 1);
  opacity: 0.7;
}

/* Others */
.dv-star-rating > label {
  margin-right: 4px;
}

@media (max-width: 767px) {
  .g-map > div {
    border-radius: 0 0 1rem 1rem;
    overflow: hidden;
  }
}
