import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { useQuery } from 'react-query';
import { getToken, removeToken } from '@framework/utils/get-token';
import { useUI } from '@contexts/ui.context';
import { Me } from '@framework/types';
import { STORAGE_KEY_ENUM, useLocalStorage } from '@utils/use-local-storage';
import graphql from '@framework/gql/utils/graphql';

const meQuery = {
  query: `
    query Me {
      me {
        id
        is_guest
        ref_id
        customer_group_id
        commission_group_id
        firstname
        lastname
        name
        email
        email_verified_at
        phone
        country_code
        phone_verified_at
        gender
        dob
        dob_updated_at
        is_active
        is_book_blocked
        is_new
        shop_id
        created_at
        updated_at
        deleted_at
        profile {
          id
          avatar
          bio
          socials
          race
          street_address
          city
          zip
          state_id
        }
        permissions {
          id
          name
          guard_name
          created_at
          updated_at
        }
        roles {
          id
          name
          guard_name
          created_at
          updated_at
        }
      }
    }
  `,
};

export const fetchMe = async () => {
  const token = await getToken();
  if (!token) {
    return { me: null };
  }

  try {
    const { data } = await graphql.post('', meQuery);
    return { me: data.data.me };
  } catch (error) {
    console.error('Error fetching user data:', error);
    return { me: null };
  }
};

export const useCustomerQuery = () => {
  const [authToken] = useLocalStorage(STORAGE_KEY_ENUM.AUTH_TOKEN, null);
  const { unauthorize } = useUI();
  return useQuery<Me, Error>(API_ENDPOINTS.ME, fetchMe, {
    staleTime: 60 * 1000,
    retry: 0,
    onSettled: (data: any) => {
      if (!data || !data.me) {
        if (authToken) {
          removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
          unauthorize ? unauthorize() : null;
        }
      }
    },
  });
};
