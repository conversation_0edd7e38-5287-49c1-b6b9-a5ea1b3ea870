import { useUI } from '@contexts/ui.context';
import { useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';
import http from '@framework/utils/http';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { setAuthToken } from '@framework/utils/get-token';
import { renderErrors } from '@framework/utils/data-mappers';

export interface LoginInputType {
  email: string;
  password: string;
}
async function login(input: any) {
  const { data } = await http.post(API_ENDPOINTS.LOGIN, input);
  return data;
}
export const useLoginMutation = () => {
  const queryClient = useQueryClient();
  const { authorize } = useUI();
  return useMutation(
    (input: LoginInputType) =>
      login({
        email: input.email,
        password: input.password,
      }),
    {
      onSuccess: (data) => {
        if (data.data && data.data.token) {
          setAuthToken(data.data.token);
          authorize();
          // Invalidate and refetch user data
          queryClient.invalidateQueries(API_ENDPOINTS.ME);
          queryClient.invalidateQueries(API_ENDPOINTS.CART);
          queryClient.invalidateQueries(API_ENDPOINTS.REFER_CODE);
          // Trigger immediate refetch of user data
          queryClient.refetchQueries(API_ENDPOINTS.ME);
        }
      },
      onError: (error: any) => {
        toast.error(renderErrors(error.response) ?? 'Login failed');
      },
    }
  );
};
