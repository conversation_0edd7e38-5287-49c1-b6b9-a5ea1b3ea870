import { PaginationType, QueryParamsType, Store } from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { CoreApi, ParamsType } from '@framework/utils/core.api';
import { stringify } from 'querystring';
import { useInfiniteQuery, useQuery } from 'react-query';

type StoresQueryOptionsType = {
  name?: string;
  state_id?: string;
  is_featured?: boolean;
  page: number;
  limit: number;
};
const StoreService = new CoreApi(API_ENDPOINTS.STORES);
const formatStores = (stores?: Store[]) => {
  return stores?.map((store) => ({
    position: {
      lat: parseFloat(store.latitude),
      lng: parseFloat(store.longitude),
    },
    ...store,
  }));
};
export const fetchStores = async ({ queryKey, pageParam }: QueryParamsType) => {
  const [_key, params] = queryKey;
  const searchParams = params as ParamsType;

  const searchString = stringify({
    ...searchParams,
    ...(searchParams.name && { name: searchParams.name }),
    ...(searchParams.state && { state_id: searchParams.state.id }),
    ...(searchParams.is_featured && { is_featured: searchParams.is_featured }),
    page: pageParam,
  });

  const { data } = await StoreService.find({
    ...searchParams,
    ...(searchParams.name && { text: searchParams.name }),
    ...(searchParams.state && { state_id: searchParams.state.id }),
    page: pageParam,
    sorted_by: 'desc',
    order_by: 'is_featured',
  });

  return { ...data, data: formatStores(data.data) };
};
export const useStoreListQuery = (params: StoresQueryOptionsType) => {
  return useInfiniteQuery<PaginationType<Store>, Error>(
    [API_ENDPOINTS.STORES, params],
    fetchStores,
    {
      staleTime: 60 * 1000,
      getNextPageParam: (lastPage) => {
        return lastPage.next_page_url ? lastPage.current_page + 1 : false;
      },
    }
  );
};
