import { SORT_TYPE } from '@framework/types';

export enum ORDER_BY_OPTIONS {
  FEATURED = "manual",
  CREATED = "created_at",
  SALES = "sales-highest",
  NAME = "name",
  PRICE = "price",
  IS_FEATURED = "is_featured",
};

export const productSortingOptions = [
  {
    label: 'text-featured',
    query_value: 'featured',
    sort_value: 'manual',
    value: {
      order_by: ORDER_BY_OPTIONS.FEATURED,
      sorted_by: SORT_TYPE.DESC,
    },
  },
  {
    label: 'text-new-arrival',
    query_value: 'new-arrival',
    sort_value: 'date-desc',
    value: {
      order_by: ORDER_BY_OPTIONS.CREATED,
      sorted_by: SORT_TYPE.DESC,
    },
  },
  {
    label: 'text-title-best-sellers',
    query_value: 'best-sellers',
    sort_value: 'sales-highest',
    value: {
      order_by: ORDER_BY_OPTIONS.SALES,
      sorted_by: SORT_TYPE.DESC,
    },
  },
  {
    label: 'text-asc-name',
    query_value: 'title-asc',
    sort_value: 'name-asc',
    value: {
      order_by: ORDER_BY_OPTIONS.NAME,
      sorted_by: SORT_TYPE.ASC,
    },
  },
  {
    label: 'text-desc-name',
    query_value: 'title-desc',
    sort_value: 'name-desc',
    value: {
      order_by: ORDER_BY_OPTIONS.NAME,
      sorted_by: SORT_TYPE.DESC,
    },
  },
  {
    label: 'text-lowest-price',
    query_value: 'price-lowest',
    sort_value: 'price-lowest',
    value: {
      order_by: ORDER_BY_OPTIONS.PRICE,
      sorted_by: SORT_TYPE.ASC,
    },
  },
  {
    label: 'text-highest-price',
    query_value: 'price-highest',
    sort_value: 'price-highest',
    value: {
      order_by: ORDER_BY_OPTIONS.PRICE,
      sorted_by: SORT_TYPE.DESC,
    },
  },
];
