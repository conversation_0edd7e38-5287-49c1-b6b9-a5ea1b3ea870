import { AdditionalSettingsData, QueryParamsType } from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { CoreApi } from '@framework/utils/core.api';
import { useQuery } from 'react-query';

type AdditionalSettingsQueryOptions = {
  key: string;
};

export const fetchAdditionalSettings = async ({ queryKey }: QueryParamsType) => {
  const [_key, params] = queryKey;
  const { data } = await new CoreApi(`${_key}/${params.key}`).findAll();
  return data;
};

export const useAdditionalSettingsQuery = (params: AdditionalSettingsQueryOptions) => {
  return useQuery<AdditionalSettingsData, Error>([API_ENDPOINTS.SETTINGS, params], fetchAdditionalSettings, {
    staleTime: 60 * 1000,
  });
};
