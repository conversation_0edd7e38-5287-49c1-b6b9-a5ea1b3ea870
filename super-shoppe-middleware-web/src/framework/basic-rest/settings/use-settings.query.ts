import { QueryParamsType } from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { CoreApi } from '@framework/utils/core.api';
import { useQuery } from 'react-query';

type SettingsQueryOptions = {
  name?: string;
};

export const fetchSettings = async ({ queryKey }: QueryParamsType) => {
  const [_key, params] = queryKey;
  const { data } = await new CoreApi(_key as string).findAll();
  return data;
};

export const useSettingsQuery = (params?: SettingsQueryOptions) => {
  return useQuery<any, Error>([API_ENDPOINTS.SETTINGS, params], fetchSettings, {
    staleTime: 60 * 1000,
  });
};
