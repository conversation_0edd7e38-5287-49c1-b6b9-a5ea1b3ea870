import { Voucher, QueryOptionsType } from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { useQuery } from 'react-query';
import { CoreApi } from '@framework/utils/core.api';

export const fetchVoucher = async ({ queryKey }: any) => {
  const [_key, _params] = queryKey;
  if (_params._details) {
    const { data } = await new CoreApi(API_ENDPOINTS.PRODUCTS).fetchUrl(
      `${API_ENDPOINTS.PRODUCTS}/${_params.slug}?_details=1`
    );
    return data;
  } else {
    const { data } = await new CoreApi(API_ENDPOINTS.PRODUCTS).findOne(
      _params.slug
    );
    return data;
  }
};
export const useVoucherQuery = (options: QueryOptionsType) => {
  return useQuery<Voucher, Error>(
    [API_ENDPOINTS.VOUCHERS, options],
    fetchVoucher,
  );
};
