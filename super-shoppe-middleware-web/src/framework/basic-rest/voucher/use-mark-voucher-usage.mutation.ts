import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import http from '@framework/utils/http';
import { useMutation } from 'react-query';

const markVoucherUsage = (id: string | number) => {
  return http.post(`${API_ENDPOINTS.ACTIVATE_VOUCHER}/${id}`).then((res) => res.data);
};

export const useMarkVoucherUsageMutation = () => {
  return useMutation((id: string | number) => markVoucherUsage(id));
};
