import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from 'react-query';
import { PRODUCT_TYPE, QueryParamsType, Voucher } from '@framework/types';
import { CoreApi, ParamsType } from '@framework/utils/core.api';
import { mapPaginatorData } from '@framework/utils/data-mappers';

const VoucherService = new CoreApi(API_ENDPOINTS.PRODUCTS);
export type PaginatedVoucher = {
  data: Voucher[];
  paginatorInfo: any;
};
export const fetchVouchers = async ({
  queryKey,
  pageParam,
}: QueryParamsType): Promise<PaginatedVoucher> => {
  const [_key, params] = queryKey;
  let fetchedData: any = {};
  if (pageParam) {
    const response = await VoucherService.fetchUrl(pageParam);
    fetchedData = response.data;
  } else {

    const response = await VoucherService.find({ ...(params as ParamsType), product_type: PRODUCT_TYPE.VOUCHER });
    fetchedData = response.data;
  }
  const { data, ...rest } = fetchedData;
  return { data, paginatorInfo: mapPaginatorData({ ...rest }) };
};
export const useVouchersQuery = (
  params: ParamsType,
  options?: UseInfiniteQueryOptions<
    PaginatedVoucher,
    Error,
    PaginatedVoucher,
    PaginatedVoucher,
    QueryKey
  >
) => {
  return useInfiniteQuery<PaginatedVoucher, Error>(
    [API_ENDPOINTS.VOUCHERS, params],
    fetchVouchers,
    {
      staleTime: 0,
      refetchOnWindowFocus: false,
      getNextPageParam: ({ paginatorInfo }) => paginatorInfo.nextPageUrl,
    }
  );
};
