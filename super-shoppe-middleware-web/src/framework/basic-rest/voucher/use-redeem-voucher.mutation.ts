import { useCart } from '@contexts/cart/cart.context';
import { Item } from '@contexts/cart/cart.utils';
import { CartService } from '@framework/cart/cart.service';
import { fetchCart } from '@framework/cart/get-cart';
import { OrderService } from '@framework/checkout/checkout.service';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { renderErrors } from '@framework/utils/data-mappers';
import { useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-toastify';

export const useRedeemVoucherMutation = () => {
  const queryClient = useQueryClient();
  const {
    items,
  } = useCart();
  return useMutation(async (voucherProduct: Item) => {
    const uncheckedItems = items.map((_item) => ((_item.checkout || _item.id == voucherProduct.id) ? { ..._item, checkout: _item.id == voucherProduct.id} : undefined)).filter((_item) => !!_item);
    const inList = uncheckedItems.find(({ id }) => id == voucherProduct.id);
    if(!inList) {
      uncheckedItems.push({ id: voucherProduct.id, price: voucherProduct.price, quantity: 1, checkout: true });
    }
    const result = await CartService.updateCart({ items: uncheckedItems });
    if(!result.success) {
      return new Promise((resolve, reject) => { reject({ response: { data: result.errors.length > 0 ? result.errors[0] : result.message } })});
    }
    await fetchCart();
    await OrderService.updateCheckout({
      use_points: true,
    });
    await OrderService.orderCheckout({});
    queryClient.invalidateQueries(API_ENDPOINTS.ME);
    queryClient.invalidateQueries(API_ENDPOINTS.VOUCHERS);
  }, {
    onSuccess: () => {
      toast.success("Voucher is successfully redeemed. You can use it from My Vouchers page.");
      queryClient.invalidateQueries(API_ENDPOINTS.PRODUCTS);
    },
    onError: (error: any) => {
      toast.error(renderErrors(error.response) ?? "An error has occured when redeem voucher");
    },
    onSettled: (data, error, voucherProduct: Item) => {
      CartService.updateCart({ items: [{ id: voucherProduct.id, quantity: -1 }] })
        .finally(() => queryClient.invalidateQueries(API_ENDPOINTS.CART));
    }
  });
};
