import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from 'react-query';
import { QueryParamsType, WalletTransaction } from '@framework/types';
import { CoreApi, ParamsType } from '@framework/utils/core.api';
import { mapPaginatorData } from '@framework/utils/data-mappers';

const WalletTransactionService = new CoreApi(API_ENDPOINTS.WALLET_TRANSACTIONS);
export type PaginatedWalletTransaction = {
  data: WalletTransaction[];
  paginatorInfo: any;
};
export const fetchWalletTransactions = async ({
  queryKey,
  pageParam,
}: QueryParamsType): Promise<PaginatedWalletTransaction> => {
  const [_key, params] = queryKey;
  let fetchedData: any = {};
  if (pageParam) {
    const response = await WalletTransactionService.fetchUrl(pageParam);
    fetchedData = response.data;
  } else {
    const response = await WalletTransactionService.find(params as ParamsType);
    fetchedData = response.data;
  }
  const { data, ...rest } = fetchedData;
  return { data, paginatorInfo: mapPaginatorData({ ...rest }) };
};
export const useWalletTransactionsQuery = (
  params: ParamsType,
  options?: UseInfiniteQueryOptions<
    PaginatedWalletTransaction,
    Error,
    PaginatedWalletTransaction,
    PaginatedWalletTransaction,
    QueryKey
  >
) => {
  return useInfiniteQuery<PaginatedWalletTransaction, Error>(
    [API_ENDPOINTS.WALLET_TRANSACTIONS, params],
    fetchWalletTransactions,
    {
      staleTime: 0,
      refetchOnWindowFocus: false,
      getNextPageParam: ({ paginatorInfo }) => paginatorInfo.nextPageUrl,
    }
  );
};
