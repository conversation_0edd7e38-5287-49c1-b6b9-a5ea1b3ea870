import { UserWallet, WalletGroup } from "@framework/types";
import moment from "moment";

export function useIsWalletExpiring(
    wallet: UserWallet | null,
    wallet_group: WalletGroup | null,
) {
    if(!wallet || !wallet_group || !wallet_group.expire_months || !wallet.start_earning_at) {
        return {
            expire_date: null,
            expire_in_months: null,
        }
    }
    const expire_date = moment(wallet.start_earning_at).add(wallet_group.expire_months, "months");
    return {
        expire_date: expire_date.format("DD-MM-YYYY"),
        expire_in_months: expire_date.diff(moment(), "months"),
    };
}