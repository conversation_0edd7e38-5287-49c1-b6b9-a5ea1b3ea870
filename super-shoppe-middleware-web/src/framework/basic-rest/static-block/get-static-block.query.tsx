import { StaticBlock } from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import http from '@framework/utils/http';
import { useQuery } from 'react-query';

export const fetchStaticBlock = async (slug: string) => {
  const { data } = await http.get(`${API_ENDPOINTS.STATIC_BLOCKS}/${slug}`);
  return data;
};

export const useStaticBlockQuery = (slug: string) => {
  return useQuery<StaticBlock, Error>([API_ENDPOINTS.STATIC_BLOCKS, slug], () =>
    fetchStaticBlock(slug),
    {
      retry: 2,
      cacheTime: 5 * 60
    }
  );
};
