import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { useQuery } from 'react-query';
import {
  QueryParamsType,
  StaticBlock,
  StaticBlocksQueryOptionsType,
} from '@framework/types';
import { CoreApi, ParamsType } from '@framework/utils/core.api';

export const fetchStaticBlocks = async ({ queryKey }: QueryParamsType) => {
  const [_key, params] = queryKey;
  const { data: fetchedData } = await new CoreApi(_key as string).find(
    params as ParamsType
  );
  const { data } = fetchedData;
  return data;
};
export const useStaticBlocksQuery = (options: StaticBlocksQueryOptionsType) => {
  return useQuery<StaticBlock[], Error>(
    [API_ENDPOINTS.STATIC_BLOCKS, ...[options]],
    fetchStaticBlocks,
    { staleTime: 60 * 1000, retry: 0 }
  );
};
