import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { getSessionId } from '@framework/utils/get-token';
import { useQuery } from 'react-query';

export const fetchSessionId = async (session_id?: string) => {
  const response = await fetch(`${process.env.NEXT_PUBLIC_REST_API_ENDPOINT}${API_ENDPOINTS.SESSION_ID}`, {
    headers: {
      ...(session_id ? {
        'x-session': session_id
      } : {})
    }
  });
  const { data } = await response.json();
  return data
};

export const useSessionIdQuery = () => {
  return useQuery(API_ENDPOINTS.SESSION_ID, getSessionId, {
    enabled: false
  });
}