import axios from 'axios';
import { getIsTNG } from '@utils/use-tng';
import { getToken } from './get-token';
import Router from 'next/router';
import { ROUTES } from '@utils/routes';

const http = axios.create({
  baseURL: process.env.NEXT_PUBLIC_REST_API_ENDPOINT,
  timeout: 30000,
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
});

// Change request data/error here
http.interceptors.request.use(
  async (config) => {
    const token = await getToken();
    // const is_tng = getIsTNG();
    config.headers = {
      ...config.headers,
      Authorization: token ? `Bearer ${token}` : 'Bearer ',
      // ...(is_tng ? {
      //   'X-Branch': 'tng',
      //   'X-Platform': 'TNG'
      // } : {}),
      ...(config.baseURL?.includes("ngrok") ? {
        'ngrok-skip-browser-warning': "1"
      } : {})
    };
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

http.interceptors.response.use(function (response) {
  return response;
}, function (error) {
  if(error.response && error.response.status == 403) {
    Router.push({
      pathname: ROUTES.LOGOUT,
      query: {
        force: 1
      }
    });
  }
  return Promise.reject(error);
});

export default http;
