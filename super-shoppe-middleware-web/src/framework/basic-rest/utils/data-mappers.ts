import { Category, Order, Product } from '@framework/types';
import { ROUTES } from '@utils/routes';
import camelcaseKeys from 'camelcase-keys';
import startsWith from 'lodash/startsWith';

interface PaginatorInfo {
  [key: string]: unknown;
}
type PaginatorOutputType = {
  hasMorePages: boolean;
  nextPageUrl: string;
  [key: string]: unknown;
};
export const mapPaginatorData = (obj: PaginatorInfo): PaginatorOutputType => {
  const formattedValues = camelcaseKeys(obj) as any;
  return {
    ...(formattedValues as PaginatorOutputType),
    hasMorePages: formattedValues?.lastPage !== formattedValues?.currentPage,
  };
};
export const generateCategoryLinkHref = (category: Category, query?: any) => {
  const linkQuery = { ...query };
  const { id, slug, ancestors, meta } = category;
  if (meta?.show_in_brand) {
    return {
      pathname: ROUTES.CATEGORY_VIEW,
      query: {
        ...linkQuery,
        brand: id,
      },
    };
  }
  let category_slugs = slug;
  if (ancestors && ancestors.length > 0) {
    category_slugs =
      ancestors.map((c) => c.slug).join(',') + ',' + category_slugs;
  } else if (query && query.categories) {
    category_slugs =
      (typeof query.categories === 'string'
        ? query.categories
        : query.categories.join(',')) +
      ',' +
      category_slugs;
  }

  if (
    query?.categories &&
    (typeof query.categories === 'string'
      ? query.categories
      : query.categories.join(',')) != category_slugs
  ) {
    delete linkQuery.brand;
    delete linkQuery.category;
  }

  return {
    pathname: ROUTES.CATEGORY_VIEW,
    query: {
      ...(linkQuery
        ? {
            ...linkQuery,
            categories: category_slugs,
            // _cat: `${slug}.${id}`
          }
        : {
            categories: category_slugs,
            // _cat: `${slug}.${id}`
          }),
    },
  };
};
export const generateProductLinkHref = (product: Product) => {
  const { slug } = product;

  return {
    pathname: ROUTES.PRODUCTS_VIEW,
    query: {
      slug,
    },
  };
};
export const generateOrderLinkHref = (
  id: string | number,
  pathname = ROUTES.ORDERS_VIEW,
  query?: any
) => ({
  pathname,
  query: {
    ...query,
    order_id: id,
  },
});

export function renderErrors(errors: any): any {
  if (errors.data) {
    if (typeof errors.data === 'string') {
      return errors.data;
    }
    if (typeof errors.data === 'object') {
      // Handle backend error response structure
      if (errors.data.error) {
        return errors.data.error;
      }
      if (errors.data.message) {
        return errors.data.message;
      }
    }
  }
  // if(errors.type && errors.message) {
  //   return errors.message;
  // }
  // const arr = Object.entries(errors);
  // if(arr.length > 1) {
  //   return arr.map((error) => {
  //     const [type, message] = Object.entries(error);
  //     if(typeof message === "string") {
  //       return message[1];
  //     }
  //     return renderErrors(message[1]);
  //   })
  // }
  // const [type, message] = arr[0];
  // if(typeof message === "string") {
  //   return message;
  // }
  // return renderErrors(message);
}

export const remapPhoneNumber = (phone_number: string | null) => {
  if (!phone_number) {
    return null;
  }
  phone_number = phone_number.replaceAll(' ', '').replaceAll('-', '');

  if (phone_number.length <= 0) {
    return null;
  }

  if (startsWith(phone_number, '+60')) {
    return phone_number.replace('+60', '60');
  }

  if (startsWith(phone_number, '01')) {
    return phone_number.replace('01', '601');
  }

  return phone_number;
};
export const remapEmail = (email: string | null) => {
  if (!email) {
    return null;
  }
  return email.replaceAll(' ', '').toLowerCase();
};
export const encodeSearchString = (value: string) => {
  return encodeURIComponent(value.replace(/"/g, '\\"').replace(/'/g, "\\'"));
};
export const decodeSearchString = (value: string) => {
  return decodeURIComponent(value.replace(/\\\"/g, '"').replace(/\\\'/g, "'"));
};
