import { LatLng, SORT_TYPE } from '@framework/types';
import pickBy from 'lodash/pickBy';
import { encodeSearchString } from './data-mappers';
import Axios from './http';
type NumberOrString = number | string;
export type ParamsType = {
  type?: string;
  action_type?: string;
  text?: string;
  category?: string;
  slug?: string;
  status?: string;
  is_active?: string;
  keyword?: string;
  brand_id?: string;
  category_id?: string;
  category_slugs?: string | string[];
  currency?: string | string[];
  banner_type?: string;
  display_id?: string;
  barcode?: string;
  shop_id?: string;
  show_in_nav?: any;
  show_available?: boolean;
  show_in_sidemenu?: any;
  show_in_footer?: any;
  product_type?: string;
  order_type?: string;
  parent_slug?: string;
  status_slug?: string;
  state_id?: string;
  orderByDistance?: LatLng;
  center?: LatLng;
  _search?: boolean;
  order_by?: string;
  sorted_by?: SORT_TYPE;
  limit?: number;
  page?: string | number;
  orderType?: string;
};
export class CoreApi {
  http = Axios;
  constructor(public _base_path: string) {}
  private stringifySearchQuery(values: any) {
    const parsedValues = pickBy(values);
    return Object.keys(parsedValues)
      .map((k) => {
        if (k === 'type') {
          return `${k}.slug:${parsedValues[k]};`;
        }
        if (k === 'order_type') {
          return `type:${parsedValues[k]};`;
        }
        if (k === 'show_in_nav') {
          return `${k}:${parsedValues[k]};`;
        }
        if (k === 'parent_slug') {
          return `parent.slug:${parsedValues[k]};`;
        }
        if (k === 'status_slug') {
          return `status.slug:${parsedValues[k]};`;
        }
        if (k === 'category') {
          return `categories.slug:${parsedValues[k]};`;
        }
        if (k === 'currency') {
          return `currency:${
            typeof parsedValues[k] === 'string'
              ? parsedValues[k]
              : parsedValues[k].join(',')
          };`;
        }
        return `${k}:${parsedValues[k]};`;
      })
      .join('')
      .slice(0, -1)
      .replaceAll(';', '%3B');
  }
  private stringifyQuery(values: any) {
    const parsedValues = pickBy(values);
    return Object.keys(parsedValues)
      .map((k) => {
        return `${k}=${parsedValues[k]}`;
      })
      .join('&');
  }
  find(params: ParamsType) {
    const {
      type,
      action_type,
      text: name,
      category,
      slug,
      status,
      is_active,
      display_id,
      shop_id,
      show_in_nav,
      parent_slug,
      status_slug,
      keyword,
      category_id,
      brand_id,
      category_slugs,
      currency,
      show_available,
      show_in_sidemenu,
      show_in_footer,
      product_type,
      order_type,
      state_id,
      _search,
      order_by,
      sorted_by,
      page,
      limit = 30,
      orderType,
    } = params;
    const search_text = name ? encodeSearchString(name) : name;
    const searchString = this.stringifySearchQuery({
      type,
      action_type,
      name: search_text,
      category,
      slug,
      status,
      display_id,
      shop_id,
      is_active,
      product_type,
      order_type,
      currency,
      show_in_nav,
      parent_slug,
      status_slug,
      state_id,
    });
    let queryString = `?search=${searchString}&searchJoin=and&limit=${limit}`;
    if (category_slugs) {
      queryString += `&category_slugs=${category_slugs}`;
    }
    if (search_text) {
      queryString += `&tagged_name=${search_text}`;
    }
    if (keyword) {
      queryString += `&keyword=${
        keyword ? encodeSearchString(keyword) : keyword
      }`;
    }
    if (category_id) {
      queryString += `&category_id=${category_id}`;
    }
    if (brand_id) {
      queryString += `&brand_id=${brand_id}`;
    }
    if (currency) {
      queryString += '&searchFields=currency:in';
    }
    if (page) {
      queryString += `&page=${page}`;
    }
    if (show_available) {
      queryString += `&show_available=${show_available}`;
    }
    if (typeof show_in_sidemenu != 'undefined') {
      queryString += `&show_in_sidemenu=${show_in_sidemenu ? 'true' : 'false'}`;
    }
    if (typeof show_in_footer != 'undefined') {
      queryString += `&show_in_footer=${show_in_footer ? 'true' : 'false'}`;
    }
    if (_search) {
      queryString += '&_search=1';
    }
    if (order_by) {
      queryString += `&orderBy=${order_by}`;
    }
    if (sorted_by) {
      queryString += `&sortedBy=${sorted_by}`;
    }
    if (orderType) {
      queryString += `&orderType=${orderType}`;
    }

    return this.http.get(this._base_path + queryString);
  }
  findNoSearch(params: ParamsType) {
    const { type, text: name, category, banner_type, status, shop_id } = params;
    const searchString = this.stringifyQuery({
      type,
      name: name ? encodeSearchString(name) : name,
      banner_type,
      category,
      status,
      shop_id,
    });
    return this.http.get(this._base_path + '?' + searchString);
  }
  queryByType(type?: string) {
    const queryString = !!type ? `?type=${type}` : '';
    return this.http.get(this._base_path + queryString);
  }
  findAll() {
    return this.http.get(this._base_path);
  }
  fetchUrl(url: string) {
    return this.http.get(url);
  }
  postUrl(url: string, data: any) {
    return this.http.post(url, data);
  }
  findOne(id: NumberOrString) {
    return this.http.get(`${this._base_path}/${id}`);
  }
  create(data: any, options?: any) {
    return this.http
      .post(this._base_path, data, options)
      .then((res) => res.data);
  }
  update(id: NumberOrString, data: any) {
    return this.http
      .put(`${this._base_path}/${id}`, data)
      .then((res) => res.data);
  }
  delete(id: NumberOrString) {
    return this.http.delete(`${this._base_path}/${id}`);
  }
}
