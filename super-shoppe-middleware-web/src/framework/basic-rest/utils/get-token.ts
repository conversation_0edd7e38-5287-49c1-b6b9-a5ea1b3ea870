import { fetchSessionId } from '@framework/session/use-session.query';
import { getStorage, removeStorageItem, setStorage, STORAGE_KEY_ENUM } from '@utils/use-local-storage';
import Cookies from 'js-cookie';

export const setAuthToken = (token: string) => {
  return setLocalStorage(STORAGE_KEY_ENUM.AUTH_TOKEN, token);
};

export const getToken = async () => {
  return new Promise<string | null>((resolve, reject) => {
    getLocalStorage(STORAGE_KEY_ENUM.AUTH_TOKEN, null)
      .then((value) => {
        resolve(value ?? getCookie(STORAGE_KEY_ENUM.AUTH_TOKEN));
      });
  });
};

export const getGuestToken = async () => {
  return new Promise<string | null>((resolve, reject) => {
    getLocalStorage(STORAGE_KEY_ENUM.GUEST_TOKEN, null)
      .then((value) => {
        resolve(value ?? getCookie(STORAGE_KEY_ENUM.GUEST_TOKEN));
      });
  });
};

export const setGuestToken = (token: string) => {
  return setLocalStorage(STORAGE_KEY_ENUM.GUEST_TOKEN, token);
}

export const getSessionId = async (force = false) => {
  return new Promise<string | null>((resolve, reject) => {
    getLocalStorage('session_id', null)
      .then((value) => {
        const session_id = value ?? getCookie('session_id');
        if(session_id) {
          // has session stored locally
          // check is force fetch required
          if(force) {
            fetchSessionId(session_id)
              .then((token) => {
                setSessionId(token);
                resolve(token);
              });
          }
          else {
            resolve(session_id);
          }
        }
        else {
          fetchSessionId()
            .then((token) => {
              setSessionId(token);
              resolve(token);
            });
        }
      });
  });
};

export const setSessionId = (token: string) => {
  return setLocalStorage('session_id', token);
}

export const getCookie = (key: string) => {
  const cookie = Cookies.get(key);
  if(cookie && cookie.indexOf(";") > -1) {
    return cookie.split(";")[0];
  }
  return cookie;
}

export const getLocalStorage = (key: string, defaultValue: string|any) => {
  return getStorage(key, defaultValue);
}

export const setLocalStorage = (key: string, data: any) => {
  return setStorage(key, data);
}

export const removeLocalStorage = (key: string) => {
  removeStorageItem(key);
}

export const removeCookies = (key: string) => {
  Cookies.remove(key);
}

export const removeToken = (key: string) => {
  removeCookies(key);
  removeLocalStorage(key);
}