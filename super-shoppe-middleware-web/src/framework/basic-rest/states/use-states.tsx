import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { CoreApi } from '@framework/utils/core.api';
import { useQuery } from 'react-query';

export interface STATES_TYPE {
  id: string | number;
  code: string;
  name: string;
}

const StatesService = new CoreApi(API_ENDPOINTS.STATES);

export const fetchStates = async () => {
  const { data } = await StatesService.fetchUrl(API_ENDPOINTS.STATES);
  return data;
};
export const useStatesQuery = () => {
  return useQuery<STATES_TYPE[], Error>(API_ENDPOINTS.STATES, fetchStates, {
    staleTime: 60 * 1000,
  });
};
