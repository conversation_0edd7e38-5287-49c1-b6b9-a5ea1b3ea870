import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { CoreApi } from '@framework/utils/core.api';
import { useQuery } from 'react-query';

const ShippingService = new CoreApi(API_ENDPOINTS.SHIPPINGS);

export const fetchShippingMethods = async () => {
  const { data } = await ShippingService.fetchUrl(API_ENDPOINTS.SHIPPINGS);
  return data;
};
export const useShippingMethodsQuery = () => {
  return useQuery<any, Error>(API_ENDPOINTS.SHIPPINGS, fetchShippingMethods, {
    staleTime: 60 * 1000,
  });
};
