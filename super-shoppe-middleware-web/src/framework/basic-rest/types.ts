import { QueryKey } from 'react-query';

export type PaginationType<T> = {
  current_page: number;
  data: T[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: {
    url: string;
    label: string;
    active: boolean;
  }[];
  next_page_url: string;
};

export type CollectionsQueryOptionsType = {
  text?: string;
  collection?: string;
  status?: string;
  limit?: number;
};

export type BannersQueryOptionsType = {
  banner_type?: string;
  status?: string;
};
export type CategoriesQueryOptionsType = {
  text?: string;
  keyword?: string;
  type?: string;
  category?: string;
  category_slugs?: string | string[];
  category_link_slug?: string;
  category_id?: string;
  brand_id?: string;
  status?: string;
  limit?: number;
  orderType?: string;
};
export type ProductsQueryOptionsType = {
  type?: string;
  text?: string;
  category?: string;
  category_slugs?: string | string[];
  category_id?: string;
  with_category_slug_info?: boolean;
  brand_id?: string;
  product_type?: string;
  status?: string;
  show_available?: boolean;
  mockup?: boolean;
  _search?: boolean;
  order_by?: string;
  sorted_by?: SORT_TYPE;
  limit?: number;
  min_price?: string;
  max_price?: string;
};
export type VouchersQueryOptionsType = {
  category_id?: string;
  status?: string;
  order_by?: string;
  sorted_by?: SORT_TYPE;
  limit?: number;
};
export type StoresQueryOptionsType = {
  text?: string;
  state_id?: string | number;
  order_by?: string;
  sorted_by?: SORT_TYPE;
  center?: LatLng;
  limit?: number;
  page?: number;
};
export type StaticBlocksQueryOptionsType = {
  show_in_sidemenu?: boolean;
  show_in_footer?: boolean;
  limit?: number;
  order_by?: string;
  sorted_by?: SORT_TYPE;
};
export type OrdersQueryOptionsType = {
  display_id?: string;
  status_slug?: string;
} & QueryOptionsType;

export type NotificationsQueryOptionsType = {
  order_by?: string;
  sorted_by?: SORT_TYPE;
} & QueryOptionsType;

export type SettingsQueryOptionsType = {
  language: string;
};

export type QueryOptionsType = {
  text?: string;
  slug?: string;
  category?: string;
  category_slugs?: string | string[];
  status?: string;
  mockup?: boolean;
  _details?: boolean;
  limit?: number;
};

export type QueryParamsType = {
  queryKey: QueryKey;
  pageParam?: string;
};
export type Attachment = {
  id: string | number;
  thumbnail: string;
  original: string;
};
export type Category = {
  id: number | string;
  name: string;
  slug: string;
  parent?: Category | number;
  ancestors?: Category[];
  path?: string;
  descendants?: Category[];
  details?: string;
  image?: Attachment;
  gallery?: Attachment[];
  icon?: string;
  children?: [Category];
  meta?: CategoryMeta;
  products?: Product[];
  productCount?: number;
  [key: string]: unknown;
};
export type CategoryMeta = {
  nav_order?: number;
  default_sorting?: string;
  show_in_brand?: boolean;
};
export type Collection = {
  id: number | string;
  name: string;
  slug: string;
  details?: string;
  image?: Attachment;
  icon?: string;
  products?: Product[];
  productCount?: number;
};
export type Banner = {
  id: number | string;
  title?: string;
  banner_type: string;
  image: {
    desktop: BannerMedia[];
    mobile: BannerMedia[];
    is_same_desktop: boolean;
    web?: {
      desktop: BannerMedia[];
      mobile: BannerMedia[];
      is_same_desktop: boolean;
    };
    android?: {
      desktop: BannerMedia[];
      mobile: BannerMedia[];
      is_same_desktop: boolean;
    };
    ios?: {
      desktop: BannerMedia[];
      mobile: BannerMedia[];
      is_same_desktop: boolean;
    };
  };
};
export declare type BannerMedia = {
  id?: number | string;
  url: string;
  type: string;
  hidden_in_native?: boolean;
  path?: string;
  duration?: number;
};
export type Brand = {
  id: number | string;
  name: string;
  slug: string;
  image?: Attachment;
  [key: string]: unknown;
};
export type Dietary = {
  id: number | string;
  name: string;
  slug: string;
  [key: string]: unknown;
};
export type Tag = {
  id: string | number;
  name: string;
  slug: string;
};
export declare type TierPrice = {
  id: number | string;
  qty: number | null;
  tier_price: number | null;
  group_price: number | null;
};
export type Product = {
  id: number | string;
  name: string;
  slug: string;
  price: number;
  quantity: number;
  sold: number;
  unit: string;
  sale_price?: number;
  min_price?: number;
  max_price?: number;
  max_redeemable_points?: number;
  group_price?: number;
  image: Attachment;
  banner?: Attachment;
  sku?: string;
  gallery?: Attachment[];
  category?: Category;
  categories: Category[];
  tag?: Tag[];
  tier_prices?: TierPrice[];
  meta?: ProductMeta;
  brand?: Brand;
  setting: ProductSetting;
  description?: string;
  inventory: Inventory;
  related_products: Product[];
  variant?: ProductVariant;
  variations?: object;
  variation_options?: ProductVariant[];
  [key: string]: unknown;
};
export type ProductMeta = {
  seo_title?: string;
  seo_keywords?: string;
  seo_description?: string;
  show_discount?: boolean;
  show_quantity?: boolean;
  show_bottom_banner?: boolean;
};
export type ProductSetting = {
  points: number;
  limit_per_customer: number;
  max_redeemable_points: string;
};
export type ProductVariant = {
  id: number | string;
  product_id: number | string;
  title: string;
  image: Attachment | null;
  price: number;
  sale_price?: number;
  group_price?: number;
  max_redeemable_points: number;
  quantity?: number;
  is_disable: boolean;
  sku: string;
  width?: number;
  height?: number;
  length?: number;
  weight?: number;
  inventory: Inventory;
  tier_prices?: TierPrice[];
  options?: Array<{ name: string; value: string }>;
};
export type Voucher = {
  id: number | string;
  product_id: number | string;
  product: Product;
  order_product: OrderItem;
  barcode: string;
  status: string;
  used_at: string | null;
  expired_at: string | null;
  created_at: string;
  updated_at: string;
};
export type SearchTerm = {
  id: number | string;
  search_text: string;
  last_result_count: number;
  search_count: number;
  show_in_terms: boolean;
  is_active: boolean;
};
export type Inventory = {
  id: number | string;
  quantity: number;
  is_continuous_selling: boolean;
  min_qty: number;
  min_sale_qty: number;
  max_sale_qty: number;
  max_sale_qty_per_user: number;
  in_stock: boolean;
  manage_stock: boolean;
};
export type OrderItem = {
  id: number | string;
  product: Product | null;
  product_id: number | string;
  product_type: string;
  variation_option_id: number | string | null;
  name: string;
  sku: string;
  order_quantity: number;
  invoiced_quantity: number;
  shipped_quantity: number;
  canceled_quantity: number;
  refunded_quantity: number;
  unit_price: number;
  subtotal: number;
  image: string | null;
  banner: string | null;
  max_redeemable_points: number;
  coupons?: Array<Coupon>;
};
export type Coupon = {
  id: string | number;
  code: string;
  is_active: boolean;
}
export type Order = {
  id: string | number;
  display_id: string | number;
  name: string;
  slug: string;
  products: OrderItem[];
  status: OrderStatus;
  total: number;
  amount: number;
  coupon_discount: number;
  discount: number;
  points_earn: number;
  points_used: string;
  points_discount: number;
  points_refunded: number;
  points_discount_refunded: number;
  golds_earn: number;
  golds_used: number;
  golds_discount: number;
  store_credit_earn: number;
  store_credit: number;
  delivery_fee: number;
  delivery_remarks?: string;
  sales_tax: number;
  tracking_number: string;
  customer: {
    id: number;
    email: string;
  };
  can_cancel?: boolean;
  refund?: OrderRefund;
  customer_email: string;
  customer_contact: string;
  shipping_method: any;
  shipping_fee: number;
  payment_method: any;
  payment_gateway: string;
  billing_address: any;
  shipping_address: any;
  created_at: string;
  updated_at: string;
};

export type OrderStatus = {
  id: string | number;
  name: string;
  slug: string;
  serial: number;
  color: string;
  default: boolean;
  created_at: string;
  updated_at: string;
};

export type OrderRefund = {
  title: string;
  description?: string;
  status: string;
  amount: number;
  images?: Attachment[];
};

export type Notification = {
  id: string;
  type: string;
  notifiable_type: string;
  notifiable_id: string | number;
  data: NotificationData;
  read_at: string | null;
  created_at: string;
  updated_at: string;
};

export type NotificationData = {
  title: string;
  body: string;
};

export type ShopsQueryOptionsType = {
  text?: string;
  shop?: Shop;
  status?: string;
  limit?: number;
};

export type Shop = {
  id: string | number;
  owner_id: string | number;
  owner_name: string;
  address: string;
  phone: string;
  website: string;
  ratings: string;
  name: string;
  slug: string;
  description: string;
  cover_image: Attachment;
  logo: Attachment;
  socialShare: any;
  created_at: string;
  updated_at: string;
};

export type StaticBlock = {
  id: string | number;
  name: string;
  key: string;
  description: string | null;
  value: string | null;
  show_in_sidemenu: boolean;
  created_at: string;
  updated_at: string;
};

export declare type Address = {
  id: string | number;
  title?: string;
  default?: boolean;
  address?: UserAddress;
  type?: string;
  customer?: User;
};

export declare type Me = {
  me: User | null;
};

export declare type User = {
  id: string | number;
  name: string;
  is_active: boolean;
  email: string;
  phone: string;
  address: Array<Address>;
  wishlist: Array<Product>;
  wallets: Array<UserWallet>;
  orders_count?: number;
  require_password?: boolean;
  created_at: string;
  updated_at: string;
  // profile?: Maybe<Profile>;
};

export declare type UserAddress = {
  fax?: string;
  zip: string;
  city: string;
  state: string;
  company?: string;
  country: string;
  recipient: string;
  contact_number: string;
  street_address: string;
  email: string;
};

export type UserProfile = {
  avatar?: Attachment;
  bio?: string;
  socials?: any[];
  contact?: string;
};

export type UserWallet = {
  id: string | number;
  wallet_group: WalletGroup;
  wallet_group_id: string | number;
  balance: number;
  locked_balance: number;
  total_spent: number;
  start_earning_at: Date | null;
};

export type Referral = {
  id: string | number;
  referrer: User;
  referee: User;
  status: string;
  created_at: string;
  updated_at: string;
};

export declare type WalletGroup = {
  id: string | number;
  name: string;
  type: WALLET_GROUP_TYPE;
  status: boolean;
  expire_months: number | null;
  meta: object | null;
  wallet_rules?: Array<WalletRule>;
  created_at: string;
  updated_at: string;
};

export declare type WalletRule = {
  id: string | number;
  wallet_group_id: string;
  wallet_group: WalletGroup;
  name: string;
  action: string;
  action_type: string;
  amount_type: string;
  amount: number;
  priority: number;
  start_date: string;
  end_date: string;
  status: boolean;
  created_at: string;
  updated_at: string;
};

export declare type WalletTransaction = {
  id: string | number;
  wallet_id: string | number;
  wallet: UserWallet;
  customer_id: string | number;
  order_id: string | number | null;
  order?: Order;
  title: string;
  action: string;
  action_type: string;
  currency: WALLET_GROUP_TYPE;
  amount: number;
  amount_used: number;
  prev_balance: number;
  after_balance: number;
  status: string;
  created_at: string;
  updated_at: string;
};

export declare type Setting = {
  id: string | number;
  name: string;
  key: string;
  value: string;
  created_at: string;
  updated_at: string;
};

export enum VERIFY_ACTION_TYPE {
  REGISTER = 'register',
  ACCOUNT = 'account',
  UPDATE_PROFILE = 'profile',
}

export enum SORT_TYPE {
  ASC = 'asc',
  DESC = 'desc',
}

export enum WALLET_GROUP_TYPE {
  POINT = 'point',
  STORE_CREDIT = 'store_credit',
}

export enum PRODUCT_TYPE {
  SIMPLE = 'simple',
  VARIABLE = 'variable',
  VOUCHER = 'voucher',
}

export enum ORDER_STATUSES {
  PENDING = 'pending',
  PROCESSING = 'processing',
  PACKED = 'packed',
  DELIVERY = 'delivery',
  COMPLETE = 'complete',
  CANCELED = 'canceled',
}

export enum PAYMENT_GATEWAYS {
  COD = 'cod',
  CASHONDELIVERY = 'cashondelivery',
}

export enum VOUCHER_STATUSES {
  ACTIVE = 'active',
  USED = 'used',
}

export enum META_PIXEL_STANDARD_EVENT_ENUM {
  VIEW_CONTENT = 'ViewContent',
  ADD_TO_CART = 'AddToCart',
  INITIATE_CHECKOUT = 'InitiateCheckout',
  ADD_PAYMENT_INFO = 'AddPaymentInfo',
  PURCHASE = 'Purchase',
}

export enum META_PIXEL_CONTENT_TYPE_ENUM {
  PRODUCT = 'product',
  PRODUCT_GROUP = 'product_group',
}

export enum CURRENCY_ENUM {
  MY = 'MYR',
}

export enum API_SETTINGS_ENUM {
  GOOGLE = 'google',
}

export type Store = {
  id: string | number;
  name: string;
  address: string;
  phone: string;
  latitude: number;
  longitude: number;
  place_id: string;
  state_id: string | number;
  state: {
    id: string | number;
    name: string;
  };
  is_featured: boolean;
};

export type LatLng = {
  lat: number;
  lng: number;
};

export type GoogleAPISettingsData = {
  api_key: string;
  map_id: string;
};

export type BaseAPISettingsData = GoogleAPISettingsData;

export type AdditionalSettingsData = {
  data: BaseAPISettingsData;
};
