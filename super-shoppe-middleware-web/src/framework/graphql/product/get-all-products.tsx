import {
  Category,
  Product,
  ProductsQueryOptionsType,
} from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { ParamsType } from '@framework/utils/core.api';
import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from 'react-query';
import { generateProductQuery } from '../query/product-query';
import graphql from '../utils/graphql';

export type GQLQueryParamsType = {
  queryKey: QueryKey;
  pageParam?: ProductsQueryOptionsType & {
    page?: number;
  };
}

export type PaginatorInfoType = {
  currentPage: number;
  hasMorePages: boolean;
  lastPage: number;
  total: number;
}

export type PaginatedProduct = {
  data: {
    category?: Category;
    products: {
      data: Product[]
      paginatorInfo: PaginatorInfoType;
    }
  };
};
const fetchProducts = async ({
  queryKey,
  pageParam,
}: GQLQueryParamsType): Promise<PaginatedProduct> => {
  const [_key, params] = queryKey;
  const searchParams = {
    ...params as ParamsType,
    page: pageParam && pageParam.page ? pageParam.page + 1 : 1
  };
  const query = generateProductQuery(searchParams);
  const response = await graphql.post("", query);
  const { data } = response.data;
  return { data };
};

const useProductsQuery = (
  params: ProductsQueryOptionsType,
  options?: UseInfiniteQueryOptions<
    PaginatedProduct,
    Error,
    PaginatedProduct,
    PaginatedProduct,
    QueryKey
  >
) => {
  return useInfiniteQuery<PaginatedProduct, Error>(
    [API_ENDPOINTS.PRODUCTS, params],
    fetchProducts,
    {
      ...options,
      refetchOnWindowFocus: false,
      getNextPageParam: ({ data }) => {
        return data.products.paginatorInfo.hasMorePages ? {
          ...params,
          page: data.products.paginatorInfo.currentPage
        } : null;
      },
    }
  );
};

export { useProductsQuery, fetchProducts };
