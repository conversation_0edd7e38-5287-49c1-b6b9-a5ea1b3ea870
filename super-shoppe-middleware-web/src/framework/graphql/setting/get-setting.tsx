import {
  SettingsQueryOptionsType,
  Setting,
} from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import {
  QueryKey,
  useQuery,
} from 'react-query';
import graphql from '../utils/graphql';
import { generateSettingQuery } from '../query/setting-query';

const fetchSettings = async (pageParam: SettingsQueryOptionsType) => {
  const query = generateSettingQuery(pageParam);
  const response = await graphql.post("", query);
  return response.data;
};

const useSettingsQuery = (
  params: SettingsQueryOptionsType,
  options?: any
) => {
  return useQuery<{ settings: Setting; }, Error>(
    [API_ENDPOINTS.SETTINGS, params],
    () => fetchSettings(params),
    {
      ...options,
      refetchOnWindowFocus: false,
    }
  );
};

export { useSettingsQuery, fetchSettings };
