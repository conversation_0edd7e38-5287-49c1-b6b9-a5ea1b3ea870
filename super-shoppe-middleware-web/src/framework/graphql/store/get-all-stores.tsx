import {
  Store,
  StoresQueryOptionsType,
} from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { ParamsType } from '@framework/utils/core.api';
import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from 'react-query';
import graphql from '../utils/graphql';
import { generateStoreQuery } from '../query/store-query';

export type GQLQueryParamsType = {
  queryKey: QueryKey;
  pageParam?: StoresQueryOptionsType & {
    page?: number;
  };
}

export type PaginatorInfoType = {
  currentPage: number;
  hasMorePages: boolean;
  lastPage: number;
  total: number;
}

export type PaginatedStore = {
  data: {
    stores: Store[]
  }
};
const fetchStores = async ({
  queryKey,
  pageParam,
}: GQLQueryParamsType): Promise<PaginatedStore> => {
    const [_key, params] = queryKey;
    const searchParams = {
      ...params as ParamsType,
      page: pageParam && pageParam.page ? pageParam.page + 1 : 1
    };
    const query = generateStoreQuery(searchParams);
    const response = await graphql.post("", query);
    const { data } = response.data;
    return { data };
};

const useStoresQuery = (
  params: StoresQueryOptionsType,
  options?: UseInfiniteQueryOptions<
    PaginatedStore,
    Error,
    PaginatedStore,
    PaginatedStore,
    QueryKey
  >
) => {
  return useInfiniteQuery<PaginatedStore, Error>(
    [API_ENDPOINTS.STORES, params],
    fetchStores,
    {
      ...options,
      refetchOnWindowFocus: false,
    }
  );
};

export { useStoresQuery, fetchStores };
