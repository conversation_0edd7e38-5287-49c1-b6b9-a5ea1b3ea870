import {
  Voucher,
  VouchersQueryOptionsType,
} from '@framework/types';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { ParamsType } from '@framework/utils/core.api';
import {
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from 'react-query';
import graphql from '../utils/graphql';
import { generateVoucherQuery } from '../query/voucher-query';
import { getToken } from '@framework/utils/get-token';

export type GQLQueryParamsType = {
  queryKey: QueryKey;
  pageParam?: VouchersQueryOptionsType & {
    page?: number;
  };
}

export type PaginatorInfoType = {
  currentPage: number;
  hasMorePages: boolean;
  lastPage: number;
  total: number;
}

export type PaginatedVoucher = {
  data: {
    vouchers: {
      data: Voucher[]
      paginatorInfo: PaginatorInfoType;
    }
  };
};
const fetchVouchers = async ({
  queryKey,
  pageParam,
}: GQLQueryParamsType): Promise<PaginatedVoucher> => {
  if (await getToken()) {
    const [_key, params] = queryKey;
    const searchParams = {
      ...params as ParamsType,
      page: pageParam && pageParam.page ? pageParam.page + 1 : 1
    };
    const query = generateVoucherQuery(searchParams);
    const response = await graphql.post("", query);
    const { data } = response.data;
    return { data };
  }
  return { data: { vouchers: { data: [], paginatorInfo: { currentPage: 0, hasMorePages: false, lastPage: 0, total: 0, } } } };
};

const useVouchersQuery = (
  params: VouchersQueryOptionsType,
  options?: UseInfiniteQueryOptions<
    PaginatedVoucher,
    Error,
    PaginatedVoucher,
    PaginatedVoucher,
    QueryKey
  >
) => {
  return useInfiniteQuery<PaginatedVoucher, Error>(
    [API_ENDPOINTS.VOUCHERS, params],
    fetchVouchers,
    {
      ...options,
      refetchOnWindowFocus: false,
      getNextPageParam: ({ data }) => {
        return data.vouchers.paginatorInfo.hasMorePages ? {
          ...params,
          page: data.vouchers.paginatorInfo.currentPage
        } : null;
      },
    }
  );
};

export { useVouchersQuery, fetchVouchers };
