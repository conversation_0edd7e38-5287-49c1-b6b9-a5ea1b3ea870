import axios from 'axios';
import { getIsTNG } from '@utils/use-tng';
import { getGuestToken, getToken } from '@framework/utils/get-token';
import { Device } from '@capacitor/device';

const graphql = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_REST_API_ENDPOINT}/graphql`,
  timeout: 30000,
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
});

// Change request data/error here
graphql.interceptors.request.use(
  async (config) => {
    const token = await getToken();
    const guestToken = await getGuestToken();
    const is_tng = getIsTNG();
    const info = await Device.getInfo();
    config.headers = {
      ...config.headers,
      Authorization: token ? `Bearer ${token}` : guestToken ?? 'Bearer ',
      ...(is_tng ? {
        'X-Branch': 'tng',
        'X-Platform': 'TNG'
      } : {
        'X-Platform': info.platform
      }),
    };
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default graphql;
