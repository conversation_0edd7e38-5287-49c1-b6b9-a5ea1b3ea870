import { ParamsType } from "@framework/utils/core.api";
import { gql } from "graphql-request";
import { ORDER_BY_OPTIONS } from "@framework/static/sorting-options";
import { LatLng } from "@framework/types";

type GQLQueryParamsType = ParamsType & { center?: LatLng; page: number };

const generateOrderBy = (params: GQLQueryParamsType) => {
    const { order_by, sorted_by, orderByDistance } = params;
    if (orderByDistance) {
        return `
            orderByDistance: {
                lat: ${orderByDistance.lat}
                lng: ${orderByDistance.lng}
            }
        `
    }
    switch(order_by) {
        case ORDER_BY_OPTIONS.IS_FEATURED:
        case ORDER_BY_OPTIONS.CREATED:
            return `
                orderBy: {
                    order: ${sorted_by?.toUpperCase()}
                    column: ${order_by.toUpperCase()}
                }
            `;
    }
    return "";
}

const generateQueryData = (params: GQLQueryParamsType) => {
    let query = "";
    if(params.text || params.state_id) {
        query += `
            search: {
                ${params.state_id ? `
                    AND: [
                        { column: STATE_ID, value: ${params.state_id} }
                    ]
                ` : ""}
                ${params.text ? `
                    OR: [
                        { column: NAME, operator: LIKE, value: "%${params.text}%" }
                        { column: ADDRESS, operator: LIKE, value: "%${params.text}%" }
                    ]
                ` : ""}
            }
        `;
    }
    if(params.center) {
        query += `
            center: {
                lat: ${params.center.lat}
                lng: ${params.center.lng}
            }
        `;
    }
    query += generateOrderBy(params);
    return query;
}

export const generateStoreQuery = (params: GQLQueryParamsType) => {
    const query_data = generateQueryData(params);
    return {
        query: gql`
            query {
                stores(
                    ${query_data}
                ) {
                    id
                    name
                    address
                    phone
                    latitude
                    longitude
                    place_id
                }
            }
        `
    }
};