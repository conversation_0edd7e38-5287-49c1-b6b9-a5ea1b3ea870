import { ParamsType } from '@framework/utils/core.api';
import { gql } from 'graphql-request';
import { ORDER_BY_OPTIONS } from '@framework/static/sorting-options';
import {
  PRODUCT_TYPE,
  ProductsQueryOptionsType,
  SORT_TYPE,
} from '@framework/types';

type GQLQueryParamsType = ParamsType &
  ProductsQueryOptionsType & { page: number };

const generateOrderBy = (params: GQLQueryParamsType) => {
  const { order_by, sorted_by } = params;
  switch (order_by) {
    case ORDER_BY_OPTIONS.FEATURED:
      return `
                featuredOrdering: { ${
                  params.category_slugs
                    ? `category_slugs: "${params.category_slugs}"`
                    : ''
                } is_featured: true }
            `;
    case ORDER_BY_OPTIONS.SALES:
      return `
                orderBy: {
                    order: DESC
                    orders: {
                        aggregate: COUNT
                    }
                }
            `;
    case ORDER_BY_OPTIONS.PRICE:
      return `
                orderBy: {
                    order: ${sorted_by?.toUpperCase()}
                    column: ${
                      sorted_by?.toLowerCase() === SORT_TYPE.ASC
                        ? 'MIN_PRICE'
                        : 'MAX_PRICE'
                    }
                }
            `;
    case ORDER_BY_OPTIONS.NAME:
    case ORDER_BY_OPTIONS.CREATED:
      return `
                orderBy: {
                    order: ${sorted_by?.toUpperCase()}
                    column: ${order_by.toUpperCase()}
                }
            `;
  }
  return '';
};

const generateQueryData = (params: GQLQueryParamsType) => {
  let query = ``;
  if (params.text) {
    query += `
            search: { value: "${params.text}" ${
      params._search ? '_search: true' : ''
    } }
        `;
  }
  if (params.category_slugs) {
    query = `
            category_slugs: "${params.category_slugs}"
        `;
  }
  if (params.show_available) {
    query += `
            show_available: true
        `;
  }
  if (params.barcode) {
    query += `
            barcode: "${params.barcode}"
        `;
  }
  if (params.product_type) {
    query += `
            product_type: "${params.product_type}"
        `;
  }
  if (params.category_id || params.brand_id) {
    query += `
            hasCategories: {
                column: ID
                operator: IN
                value: [${params.category_id ?? ''}${
      params.brand_id
        ? params.category_id
          ? ',' + params.brand_id
          : params.brand_id
        : ''
    }]
            }
        `;
  }
  if (params.min_price || params.max_price) {
    query += `
            price_range: {
               ${params.min_price ? `from: ${params.min_price}` : ''}
                ${params.max_price ? `to: ${params.max_price}` : ''}

            }
        `;
  }

  query += generateOrderBy(params);
  return query;
};

export const generateProductQuery = (params: GQLQueryParamsType) => {
  const query_data = generateQueryData(params);
  return {
    query: gql`
            query {
                ${
                  params.with_category_slug_info && params.category_slugs
                    ? `
                category(
                    slug: "${params.category_slugs}"
                ) {
                    id
                    name
                    slug
                    products_count
                }
                `
                    : ''
                }
                products(
                    first: ${params.limit}
                    page: ${params.page}
                    ${query_data}
                ) {
                    data {
                        id
                        name
                        slug
                        status
                        barcode
                        product_type
                        inventory {
                          quantity
                          min_qty
                          is_continuous_selling
                        }
                        price
                        sale_price
                        group_price
                        min_price
                        max_price
                        max_redeemable_points
                        banner {
                          original
                          thumbnail
                        }
                        image {
                          original
                          thumbnail
                        }
                        variations {
                          value
                          attribute {
                            name
                          }
                        }
                        meta {
                          show_quantity
                          show_discount
                        }
                        variation_options {
                          title
                          sku
                          price
                          sale_price
                          group_price
                          max_redeemable_points
                          inventory {
                            quantity
                            min_qty
                            is_continuous_selling
                          }
                        }
                        tags {
                          name
                        }
                    }
                    paginatorInfo {
                        currentPage
                        hasMorePages
                        lastPage
                        total
                    }
                }
            }
        `,
  };
};
