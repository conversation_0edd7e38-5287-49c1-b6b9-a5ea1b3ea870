import { SettingsQueryOptionsType } from "@framework/types";
import { gql } from "graphql-request";

const generateQueryData = (params: SettingsQueryOptionsType) => {
    let query = "";
    if(params.language) {
        query += `
            language: "${params.language}"
        `;
    }
    return query;
}

export const generateSettingQuery = (params: SettingsQueryOptionsType) => {
    const query_data = generateQueryData(params);
    return {
        query: gql`
            query {
                settings(
                    ${query_data}
                ) {
                    name
                    key
                    value
                    created_at
                    updated_at
                }
            }
        `
    }
};