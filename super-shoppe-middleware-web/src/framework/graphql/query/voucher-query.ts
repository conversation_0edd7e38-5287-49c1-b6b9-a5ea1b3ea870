import { ParamsType } from "@framework/utils/core.api";
import { gql } from "graphql-request";
import { ORDER_BY_OPTIONS } from "@framework/static/sorting-options";
import { SORT_TYPE } from "@framework/types";

type GQLQueryParamsType = ParamsType & { page: number };

const generateOrderBy = (params: GQLQueryParamsType) => {
    const { order_by, sorted_by } = params;
    switch(order_by) {
        case ORDER_BY_OPTIONS.FEATURED:
            return `
                featuredOrdering: { ${params.category_slugs ? `category_slugs: "${params.category_slugs}"` : ""} is_featured: true }
            `;
        case ORDER_BY_OPTIONS.SALES:
            return `
                orderBy: {
                    order: DESC
                    orders: {
                        aggregate: COUNT
                    }
                }
            `;
        case ORDER_BY_OPTIONS.PRICE:
            return `
                orderBy: {
                    order: ${sorted_by?.toUpperCase()}
                    column: ${sorted_by?.toLowerCase() === SORT_TYPE.ASC ? "MIN_PRICE" : "MAX_PRICE"}
                }
            `;
        case ORDER_BY_OPTIONS.NAME:
        case ORDER_BY_OPTIONS.CREATED:
            return `
                orderBy: {
                    order: ${sorted_by?.toUpperCase()}
                    column: ${order_by.toUpperCase()}
                }
            `;
    }
    return "";
}

const generateQueryData = (params: GQLQueryParamsType) => {
    let query = "";
    if(params.text) {
        query += `
            search: { value: "${params.text}" ${params._search ? "_search: true" : ""} }
        `;
    }
    if(params.status) {
        query += `
            status: "${params.status}"
        `;
    }
    if(params.category_slugs) {
        query = `
            category_slugs: "${params.category_slugs}"
        `;
    }
    if(params.show_available) {
        query += `
            show_available: true
        `;
    }
    if(params.category_id || params.brand_id) {
        query += `
            hasCategories: {
                column: ID
                operator: IN
                value: [${params.category_id ?? ""}${params.brand_id ? (params.category_id ? "," + params.brand_id : params.brand_id) : ""}]
            }
        `;
    }
    query += generateOrderBy(params);
    return query;
}

export const generateVoucherQuery = (params: GQLQueryParamsType) => {
    const query_data = generateQueryData(params);
    return {
        query: gql`
            query {
                vouchers(
                    first: ${params.limit}
                    page: ${params.page}
                    ${query_data}
                ) {
                    data {
                        id
                        product{
                            id
                            name
                            image {
                                original
                                thumbnail
                            }
                        }
                        order_product{
                            id
                            product_id
                            name
                            image
                        }
                        barcode
                        status
                        used_at
                        created_at
                        updated_at
                        expired_at
                    }
                    paginatorInfo {
                        currentPage
                        hasMorePages
                        lastPage
                        total
                    }
                }
            }
        `
    }
};