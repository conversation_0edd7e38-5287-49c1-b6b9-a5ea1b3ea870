import { GraphQLClient, ClientError } from 'graphql-request';
import { getToken } from '@framework/utils/get-token';

// GraphQL endpoint - you may need to adjust this based on your setup
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_GRAPHQL_ENDPOINT || '/graphql';

// Create GraphQL client with interceptor
const createGraphQLClient = () => {
  const client = new GraphQLClient(GRAPHQL_ENDPOINT, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Override the request method to always include fresh auth headers
  const originalRequest = client.request.bind(client);
  client.request = async (document, variables, requestHeaders) => {
    const token = getToken();
    const headers = {
      'Content-Type': 'application/json',
      ...requestHeaders,
      ...(token && { Authorization: `Bearer ${token}` }),
    };

    try {
      return await originalRequest(document, variables, headers);
    } catch (error) {
      // Handle GraphQL errors
      if (error instanceof ClientError) {
        // Check for authentication errors
        if (error.response.status === 401) {
          // Token might be expired, you could trigger a logout here
          console.error('Authentication failed:', error.message);
          // You might want to redirect to login or refresh token here
        }

        // Check for permission errors
        if (
          error.response.errors?.some((err: any) =>
            err.message.includes('permission')
          )
        ) {
          console.error('Permission denied:', error.message);
        }
      }

      // Re-throw the error so it can be handled by React Query
      throw error;
    }
  };

  return client;
};

// Export singleton instance
export const graphqlClient = createGraphQLClient();

// Helper function to make requests with automatic auth and error handling
export const graphqlRequest = async (query: string, variables?: any) => {
  try {
    return await graphqlClient.request(query, variables);
  } catch (error) {
    // Additional error handling can be added here
    console.error('GraphQL request failed:', error);
    throw error;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = (): boolean => {
  return !!getToken();
};
