export interface UserProfile {
  id: string;
  avatar?: any;
  bio?: string;
  socials?: any;
  race?: string;
  street_address?: string;
  city?: string;
  zip?: string;
  state_id?: string;
}

export interface Permission {
  id: string;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

export interface Role {
  id: string;
  name: string;
  guard_name: string;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: string;
  is_guest?: boolean;
  ref_id?: string;
  customer_group_id?: string;
  commission_group_id?: string;
  firstname?: string;
  lastname?: string;
  name: string;
  email?: string;
  email_verified_at?: string;
  phone?: string;
  country_code?: string;
  phone_verified_at?: string;
  gender?: string;
  dob?: string;
  dob_updated_at?: string;
  is_active: boolean;
  is_book_blocked?: boolean;
  is_new?: boolean;
  shop_id?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  profile?: UserProfile;
  permissions?: Permission[];
  roles?: Role[];
}

export interface MeResponse {
  me: Customer | null;
}

// For backward compatibility with existing code
export interface Me {
  me: Customer | null;
}
