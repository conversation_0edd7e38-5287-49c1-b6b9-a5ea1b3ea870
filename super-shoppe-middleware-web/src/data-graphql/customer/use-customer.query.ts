import { useQuery, UseQueryOptions } from 'react-query';
import { graphqlRequest, isAuthenticated } from '../client';
import { GET_ME } from './customer.query';
import { MeResponse, Customer } from './customer.types';
import { useUI } from '@contexts/ui.context';
import { removeToken } from '@framework/utils/get-token';
import { STORAGE_KEY_ENUM, useLocalStorage } from '@utils/use-local-storage';

// Fetch current user data
export const fetchMe = async (): Promise<MeResponse> => {
  if (!isAuthenticated()) {
    return { me: null };
  }

  try {
    const response = await graphqlRequest(GET_ME);
    return response;
  } catch (error) {
    console.error('Error fetching user data:', error);
    return { me: null };
  }
};

// React Query hook for current user
export const useCustomerQuery = (
  options?: UseQueryOptions<MeResponse, Error>
) => {
  const [authToken] = useLocalStorage(STORAGE_KEY_ENUM.AUTH_TOKEN, null);
  const { unauthorize } = useUI();

  return useQuery<MeResponse, Error>(
    ['me'],
    fetchMe,
    {
      staleTime: 60 * 1000, // 1 minute
      retry: 0,
      enabled: !!authToken, // Only run query if auth token exists
      onSettled: (data: MeResponse | undefined) => {
        if (!data || !data.me) {
          if (authToken) {
            removeToken(STORAGE_KEY_ENUM.AUTH_TOKEN);
            unauthorize ? unauthorize() : null;
          }
        }
      },
      ...options,
    }
  );
};

// Alternative hook name for consistency with existing code
export const useMeQuery = useCustomerQuery;

// Helper hook to get just the customer data
export const useCustomer = () => {
  const { data, isLoading, error } = useCustomerQuery();
  
  return {
    customer: data?.me || null,
    customerLoading: isLoading,
    customerError: error,
    isAuthenticated: !!data?.me,
  };
};

// Helper function to check if user has specific permission
export const useHasPermission = (permission: string) => {
  const { customer } = useCustomer();
  
  return customer?.permissions?.some(p => p.name === permission) || false;
};

// Helper function to check if user has specific role
export const useHasRole = (role: string) => {
  const { customer } = useCustomer();
  
  return customer?.roles?.some(r => r.name === role) || false;
};

// Helper function to get user's role names
export const useUserRoles = (): string[] => {
  const { customer } = useCustomer();
  
  return customer?.roles?.map(r => r.name) || [];
};

// Helper function to get user's permission names
export const useUserPermissions = (): string[] => {
  const { customer } = useCustomer();
  
  return customer?.permissions?.map(p => p.name) || [];
};
