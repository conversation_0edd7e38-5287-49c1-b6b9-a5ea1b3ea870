const AccountHelpIcon: React.FC<React.SVGAttributes<{}>> = ({ ...rest }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M18.7782 3.2218C16.7005 1.14421 13.9382 0 11 0C8.0618 0 5.29947 1.14421 3.2218 3.2218C1.14421 5.29947 0 8.0618 0 11C0 13.9382 1.14421 16.7005 3.2218 18.7782C5.29947 20.8558 8.0618 22 11 22C13.9382 22 16.7005 20.8558 18.7782 18.7782C20.8558 16.7005 22 13.9382 22 11C22 8.0618 20.8558 5.29947 18.7782 3.2218ZM11 1.28906C13.4458 1.28906 15.6831 2.19832 17.3923 3.69613L14.3926 6.69591C13.4586 5.95818 12.2799 5.51727 11 5.51727C9.72018 5.51727 8.54141 5.95818 7.60749 6.69591L4.60771 3.69613C6.31692 2.19832 8.55418 1.28906 11 1.28906ZM15.1937 11C15.1937 13.3124 13.3124 15.1937 11 15.1937C8.68759 15.1937 6.80634 13.3124 6.80634 11C6.80634 8.68759 8.68759 6.80634 11 6.80634C13.3124 6.80634 15.1937 8.68759 15.1937 11ZM1.28906 11C1.28906 8.55413 2.19837 6.31688 3.69621 4.60763L6.69599 7.6074C5.95822 8.54137 5.51727 9.72013 5.51727 11C5.51727 12.2799 5.95818 13.4586 6.69591 14.3926L3.69613 17.3924C2.19832 15.6831 1.28906 13.4458 1.28906 11ZM11 20.7109C8.55413 20.7109 6.31688 19.8016 4.60758 18.3038L7.60736 15.304C8.54133 16.0418 9.72009 16.4827 11 16.4827C12.2798 16.4827 13.4586 16.0418 14.3926 15.304L17.3924 18.3037C15.6831 19.8016 13.4459 20.7109 11 20.7109ZM18.3039 17.3923L15.3041 14.3925C16.0418 13.4585 16.4827 12.2798 16.4827 11C16.4827 9.72013 16.0418 8.54137 15.3041 7.6074L18.3038 4.60763C19.8016 6.31688 20.7109 8.55413 20.7109 11C20.7109 13.4458 19.8017 15.6831 18.3039 17.3923Z"
        fill="#8C969F"
      />
    </svg>
  );
};

export default AccountHelpIcon;
