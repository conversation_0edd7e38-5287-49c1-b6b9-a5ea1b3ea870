const AccountAddressIcon: React.FC<React.SVGAttributes<{}>> = ({ ...rest }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M10.9998 0C6.60645 0 3.03223 3.57423 3.03223 7.96752C3.03223 13.4197 10.1624 21.4239 10.466 21.762C10.7511 22.0796 11.249 22.079 11.5336 21.762C11.8372 21.4239 18.9674 13.4197 18.9674 7.96752C18.9673 3.57423 15.3931 0 10.9998 0ZM10.9998 20.1837C8.60012 17.3332 4.46704 11.7095 4.46704 7.96761C4.46704 4.36537 7.39759 1.43481 10.9998 1.43481C14.602 1.43481 17.5325 4.36537 17.5325 7.96757C17.5325 11.7096 13.4001 17.3323 10.9998 20.1837Z"
        fill="#8C969F"
      />
      <path
        d="M11.0001 3.95898C8.78969 3.95898 6.99146 5.75727 6.99146 7.96767C6.99146 10.1781 8.78974 11.9764 11.0001 11.9764C13.2104 11.9764 15.0087 10.1781 15.0087 7.96767C15.0087 5.75727 13.2104 3.95898 11.0001 3.95898ZM11.0001 10.5415C9.58083 10.5415 8.42627 9.38693 8.42627 7.96767C8.42627 6.54841 9.58088 5.3938 11.0001 5.3938C12.4193 5.3938 13.5739 6.54841 13.5739 7.96767C13.5739 9.38693 12.4193 10.5415 11.0001 10.5415Z"
        fill="#8C969F"
      />
    </svg>
  );
};

export default AccountAddressIcon;
