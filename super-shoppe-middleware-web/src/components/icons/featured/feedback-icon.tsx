const FeedbackIcon = ({
  color = 'currentColor',
  width = '43px',
  height = '41px',
  className = '',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 43 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M38.8132 16.1527C39.5556 15.429 39.8177 14.3669 39.4974 13.3809C39.177 12.3949 38.3406 11.6898 37.3147 11.5408L28.3435 10.2372C28.1674 10.2117 28.0152 10.1011 27.9364 9.94142L23.9244 1.81228C23.4657 0.882577 22.5366 0.305054 21.4998 0.305054C20.463 0.305054 19.534 0.882577 19.0752 1.81228L15.0632 9.9415C14.9843 10.1011 14.8322 10.2117 14.656 10.2373L5.68502 11.5408C4.65906 11.6898 3.8228 12.3949 3.50233 13.3809C3.18186 14.3669 3.44403 15.429 4.18652 16.1527L10.6781 22.4804C10.8055 22.6047 10.8637 22.7836 10.8336 22.9591L9.30111 31.8939C9.1259 32.9158 9.53798 33.929 10.3767 34.5384C11.2155 35.1479 12.3068 35.2267 13.2243 34.7443L21.2482 30.5259C21.4058 30.443 21.5939 30.443 21.7516 30.5259L29.7756 34.7443C30.1747 34.9541 30.6065 35.0578 31.0363 35.0578C31.5945 35.0578 32.1491 34.8829 32.6229 34.5383C33.4616 33.9288 33.8738 32.9155 33.6985 31.8938L32.1661 22.959C32.136 22.7835 32.1942 22.6046 32.3216 22.4803L38.8132 16.1527ZM30.0343 23.3248L31.5668 32.2598C31.618 32.5588 31.4334 32.7293 31.3517 32.7886C31.2698 32.8479 31.0506 32.9709 30.7821 32.8297L22.7582 28.6112C22.3643 28.4042 21.9322 28.3006 21.5 28.3006C21.0678 28.3006 20.6356 28.4042 20.2418 28.6112L12.2179 32.8295C11.9493 32.9707 11.7301 32.8478 11.6484 32.7884C11.5666 32.7291 11.3819 32.5586 11.4333 32.2596L12.9657 23.3248C13.1162 22.4475 12.8256 21.5529 12.1881 20.9316L5.69655 14.6039C5.4793 14.392 5.52847 14.1456 5.55978 14.0495C5.59093 13.9533 5.69607 13.725 5.99628 13.6815L14.9672 12.3779C15.848 12.2499 16.6091 11.6971 17.0031 10.8988L21.0151 2.76954C21.1494 2.49751 21.3989 2.46812 21.5001 2.46812C21.601 2.46812 21.8506 2.49743 21.9849 2.76954V2.76962L25.9969 10.8988C26.3908 11.6971 27.1519 12.25 28.0327 12.378L37.0037 13.6815C37.304 13.7251 37.409 13.9534 37.4402 14.0496C37.4715 14.1458 37.5207 14.3922 37.3034 14.604L30.8119 20.9317C30.1744 21.5529 29.8838 22.4475 30.0343 23.3248Z"
        fill={color}
        stroke={color}
        strokeWidth="0.3"
      />
      <path
        d="M34.1349 1.81171C33.6516 1.46064 32.9753 1.56779 32.6242 2.05098L31.4762 3.63108C31.1251 4.11427 31.2322 4.79069 31.7154 5.14176C31.9075 5.28117 32.1299 5.34836 32.3503 5.34836C32.6848 5.34836 33.0145 5.19373 33.2261 4.90256L34.3742 3.32246C34.7253 2.83919 34.6182 2.16277 34.1349 1.81171Z"
        fill={color}
        stroke={color}
        strokeWidth="0.3"
      />
      <path
        d="M11.5193 3.62474L10.3712 2.04464C10.0202 1.56161 9.3439 1.45446 8.86055 1.80537C8.37736 2.15643 8.27021 2.83285 8.62128 3.31604L9.76936 4.89614C9.98101 5.18755 10.3108 5.3421 10.6453 5.3421C10.8658 5.3421 11.0882 5.27491 11.2801 5.13542C11.7633 4.78443 11.8705 4.10801 11.5193 3.62474Z"
        fill={color}
        stroke={color}
        strokeWidth="0.3"
      />
      <path
        d="M4.96802 24.5702C4.78344 24.0022 4.17332 23.6913 3.60517 23.8759L1.74759 24.4795C1.17944 24.6641 0.868656 25.2743 1.05324 25.8423C1.2017 26.2995 1.62571 26.59 2.0816 26.59C2.19235 26.59 2.30502 26.5728 2.41609 26.5367L4.27366 25.9331C4.84173 25.7485 5.1526 25.1384 4.96802 24.5702Z"
        fill={color}
        stroke={color}
        strokeWidth="0.3"
      />
      <path
        d="M21.5005 36.5786C20.9032 36.5786 20.4189 37.0628 20.4189 37.6601V39.6134C20.4189 40.2107 20.9032 40.6949 21.5005 40.6949C22.0978 40.6949 22.582 40.2107 22.582 39.6134V37.6601C22.582 37.0628 22.0979 36.5786 21.5005 36.5786Z"
        fill={color}
        stroke={color}
        strokeWidth="0.3"
      />
      <path
        d="M41.2525 24.4811L39.395 23.8775C38.8271 23.6931 38.2167 24.0038 38.0321 24.5719C37.8475 25.1401 38.1583 25.7502 38.7265 25.9348L40.5841 26.5384C40.6951 26.5745 40.8077 26.5916 40.9185 26.5916C41.3744 26.5916 41.7984 26.301 41.9469 25.8439C42.1315 25.2758 41.8206 24.6657 41.2525 24.4811Z"
        fill={color}
        stroke={color}
        strokeWidth="0.3"
      />
    </svg>
  );
};

export default FeedbackIcon;
