const CouponIcon = ({
  color = 'currentColor',
  width = '55px',
  height = '55px',
  className = '',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 55 55"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M20.0298 12.321L20.0298 12.321C20.1661 13.0493 20.8898 13.6264 21.747 13.4669C22.5787 13.2924 23.0495 12.496 22.8948 11.7498C22.7118 10.8775 21.8526 10.3929 21.0369 10.6361L20.0298 12.321ZM20.0298 12.321L20.0291 12.3179M20.0298 12.321L20.0291 12.3179M20.0291 12.3179C19.8797 11.605 20.3102 10.853 21.0369 10.6361L20.0291 12.3179Z"
        fill={color}
        stroke="white"
        strokeWidth="0.3"
      />
      <path
        d="M54.4541 34.9033L54.4538 34.9034L54.4575 34.9123C55.5255 37.4705 54.3405 40.4284 51.7944 41.5306C51.7942 41.5306 51.7941 41.5307 51.7939 41.5307L42.7757 45.3903L42.6847 45.4292V45.5282V47.9102C42.6847 50.7323 40.4009 53.0272 37.5958 53.0272H5.23889C2.43373 53.0272 0.15 50.7323 0.15 47.9102V23.2659C0.15 20.9897 1.10775 18.8035 2.77707 17.2681L2.77709 17.2681L17.9013 3.35133C17.9013 3.35132 17.9013 3.35131 17.9013 3.3513C19.3846 1.98708 21.4994 1.69308 23.0137 2.21753L23.0628 2.07579L23.0137 2.21754C23.0862 2.24262 23.4283 2.32881 23.9545 2.4574C24.4869 2.58749 25.2196 2.76373 26.0853 2.97052C27.8166 3.38411 30.0804 3.92011 32.3377 4.45391C32.9028 4.58752 33.4674 4.721 34.0233 4.85239C35.688 5.24592 37.2734 5.62069 38.5526 5.92424C39.4057 6.12669 40.1225 6.29742 40.6356 6.42088C40.8922 6.48262 41.0977 6.53248 41.2437 6.56856C41.3168 6.58661 41.3747 6.60112 41.4165 6.6119C41.4374 6.6173 41.4539 6.62166 41.4661 6.62499C41.4775 6.6281 41.4826 6.62965 41.4837 6.63L41.484 6.63006C43.6207 7.36734 45.3601 8.97958 46.2561 11.0532C46.2579 11.0573 46.2575 11.0553 46.257 11.0528L46.2579 11.0524C46.2711 11.0856 46.3759 11.3883 47.0791 13.4345C47.557 14.8251 48.3112 17.0206 49.5009 20.4842C50.6759 23.9049 52.2758 28.5625 54.4541 34.9033ZM46.2579 11.0523L46.257 11.0527C46.2569 11.0524 46.2569 11.0522 46.2568 11.0519C46.2562 11.0491 46.2556 11.0465 46.2579 11.0523ZM19.9316 5.50128L19.9315 5.50131C14.9647 10.0817 14.2994 10.6834 13.4449 11.456L13.3685 11.5251C12.4722 12.3357 11.2347 13.4576 4.75586 19.419C3.6859 20.4032 3.07274 21.8052 3.07274 23.2659V47.9102C3.07274 49.1183 4.04236 50.1045 5.23889 50.1045H37.5958C38.7922 50.1045 39.7618 49.1183 39.7619 47.9102V23.2659C39.7619 21.8052 39.1488 20.4033 38.079 19.4193C34.2335 15.8808 31.35 13.221 29.1808 11.2199C26.7128 8.94329 25.1692 7.51942 24.1849 6.62453C23.2625 5.78592 22.8249 5.40583 22.5762 5.22418C22.4497 5.13177 22.3638 5.08438 22.282 5.05512C22.2422 5.04091 22.2063 5.03205 22.1743 5.02438L22.1641 5.02195C22.1355 5.01511 22.1086 5.0087 22.0765 4.9991C21.2925 4.7628 20.5017 4.97582 19.9316 5.50128ZM50.6386 38.846L50.6391 38.8457C51.7224 38.377 52.2217 37.1042 51.7492 36.012L51.7491 36.0117C51.747 36.007 51.7475 36.0097 51.748 36.0122C51.748 36.0123 51.748 36.0124 51.7481 36.0125C51.7485 36.0146 51.7488 36.0163 51.7473 36.0128C51.7473 36.0127 51.7472 36.0126 51.7472 36.0126C51.7365 35.987 51.6407 35.7104 50.921 33.6163C50.4862 32.3508 49.8237 30.4221 48.8144 27.4838C47.6161 23.9954 45.9291 19.0841 43.5541 12.1704L43.5546 12.1702L43.5492 12.1581C42.9817 10.8858 41.9136 9.89299 40.6029 9.41861L40.5949 9.41569L40.5865 9.4137L28.9306 6.64794L28.3717 6.51533L28.7944 6.90427L40.0577 17.2683L40.0577 17.2683C41.7268 18.8035 42.6846 20.9897 42.6846 23.2659V42.0227V42.25L42.8936 42.1606L50.6386 38.846Z"
        fill={color}
        stroke="white"
        strokeWidth="0.3"
      />
      <path
        d="M25.9345 21.8703L25.9345 21.8703C26.6787 22.1826 27.0289 23.039 26.7166 23.7834L18.8721 42.4796C18.6374 43.0389 18.0951 43.3759 17.5238 43.3759C16.4879 43.3759 15.7713 42.3156 16.177 41.3487L24.0214 22.6525C24.3338 21.9083 25.1905 21.558 25.9345 21.8703Z"
        fill={color}
        stroke="white"
        strokeWidth="0.3"
      />
      <path
        d="M15.3678 26.3757V26.3757C15.3678 25.6891 15.1456 25.0523 14.7865 24.5843C14.4278 24.1169 13.9238 23.8083 13.363 23.8083C12.8022 23.8083 12.2982 24.1169 11.9395 24.5843C11.5805 25.0523 11.3582 25.6891 11.3582 26.3757C11.3582 27.0623 11.5805 27.6991 11.9396 28.1671C12.2983 28.6346 12.8023 28.9431 13.363 28.9431C13.9237 28.9431 14.4277 28.6346 14.7864 28.1671C15.1455 27.6991 15.3677 27.0623 15.3678 26.3757ZM13.3629 20.8856C16.0654 20.8856 18.2904 23.3334 18.2904 26.3757C18.2904 29.418 16.0655 31.8658 13.363 31.8658C10.6605 31.8658 8.43546 29.418 8.43546 26.3757C8.43546 23.3334 10.6604 20.8856 13.3629 20.8856Z"
        fill={color}
        stroke="white"
        strokeWidth="0.3"
      />
      <path
        d="M24.695 39.1578C24.695 36.1155 26.92 33.6677 29.6225 33.6677C32.3249 33.6677 34.5501 36.1155 34.5501 39.1578C34.5501 42.2001 32.3251 44.6479 29.6225 44.6479C26.92 44.6479 24.695 42.2001 24.695 39.1578ZM27.6177 39.1578C27.6177 39.8444 27.84 40.4812 28.1991 40.9492C28.5578 41.4167 29.0618 41.7252 29.6225 41.7252C30.1832 41.7252 30.6873 41.4167 31.0459 40.9492C31.405 40.4812 31.6273 39.8444 31.6273 39.1578C31.6273 38.4713 31.4051 37.8344 31.046 37.3664C30.6873 36.899 30.1833 36.5904 29.6225 36.5904C29.0618 36.5904 28.5577 36.899 28.1991 37.3664C27.84 37.8344 27.6177 38.4713 27.6177 39.1578Z"
        fill={color}
        stroke="white"
        strokeWidth="0.3"
      />
    </svg>
  );
};

export default CouponIcon;
