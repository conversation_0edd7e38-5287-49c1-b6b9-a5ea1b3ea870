const TwitterIcon: React.FC<React.SVGAttributes<{}>> = ({ ...rest }) => {
  return (
    <svg
      width="47"
      height="47"
      viewBox="0 0 47 47"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect
        x="1"
        y="1"
        width="45"
        height="45"
        rx="22.5"
        fill="white"
        stroke="#E2E8F0"
      />
      <g clipPath="url(#clip0)">
        <path
          d="M34 17.7988C33.2562 18.125 32.4637 18.3412 31.6375 18.4462C32.4875 17.9387 33.1363 17.1412 33.4413 16.18C32.6488 16.6525 31.7737 16.9863 30.8412 17.1725C30.0887 16.3713 29.0162 15.875 27.8462 15.875C25.5762 15.875 23.7487 17.7175 23.7487 19.9763C23.7487 20.3013 23.7762 20.6137 23.8438 20.9112C20.435 20.745 17.4188 19.1113 15.3925 16.6225C15.0387 17.2363 14.8313 17.9388 14.8313 18.695C14.8313 20.115 15.5625 21.3737 16.6525 22.1025C15.9937 22.09 15.3475 21.8988 14.8 21.5975C14.8 21.61 14.8 21.6262 14.8 21.6425C14.8 23.635 16.2212 25.29 18.085 25.6712C17.7512 25.7625 17.3875 25.8062 17.01 25.8062C16.7475 25.8062 16.4825 25.7912 16.2337 25.7362C16.765 27.36 18.2725 28.5537 20.065 28.5925C18.67 29.6837 16.8988 30.3412 14.9813 30.3412C14.645 30.3412 14.3225 30.3262 14 30.285C15.8162 31.4562 17.9688 32.125 20.29 32.125C27.835 32.125 31.96 25.875 31.96 20.4575C31.96 20.2762 31.9537 20.1013 31.945 19.9275C32.7587 19.35 33.4425 18.6288 34 17.7988Z"
          fill="#8693A4"
        />
      </g>
      <defs>
        <clipPath id="clip0">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(14 14)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TwitterIcon;
