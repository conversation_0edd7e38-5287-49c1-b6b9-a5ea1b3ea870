const AccountPaymentIcon: React.FC<React.SVGAttributes<{}>> = ({ ...rest }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M19.5938 3.4375H2.40625C1.07946 3.4375 0 4.51696 0 5.84375V16.1562C0 17.483 1.07946 18.5625 2.40625 18.5625H19.5938C20.9205 18.5625 22 17.483 22 16.1562V5.84375C22 4.51696 20.9205 3.4375 19.5938 3.4375ZM2.40625 4.8125H19.5938C20.1624 4.8125 20.625 5.27514 20.625 5.84375V7.21875H1.375V5.84375C1.375 5.27514 1.83764 4.8125 2.40625 4.8125ZM19.5938 17.1875H2.40625C1.83764 17.1875 1.375 16.7249 1.375 16.1562V8.59375H20.625V16.1562C20.625 16.7249 20.1624 17.1875 19.5938 17.1875Z"
        fill="#8C969F"
      />
      <path
        d="M4.8125 15.125H4.125C3.74533 15.125 3.4375 14.8172 3.4375 14.4375V13.75C3.4375 13.3703 3.74533 13.0625 4.125 13.0625H4.8125C5.19217 13.0625 5.5 13.3703 5.5 13.75V14.4375C5.5 14.8172 5.19217 15.125 4.8125 15.125Z"
        fill="#8C969F"
      />
    </svg>
  );
};

export default AccountPaymentIcon;
