const AccountNoticeIcon: React.FC<React.SVGAttributes<{}>> = ({ ...rest }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M14.8457 2.78909L14.7584 2.69156V2.82243V5.60142V5.65142H14.8084H17.2948H17.4066L17.3321 5.56808L14.8457 2.78909ZM13.2983 1.56036V1.51036H13.2483H5.14094C4.34121 1.51036 3.69078 2.16079 3.69078 2.96052V19.0395C3.69078 19.8392 4.34121 20.4896 5.14094 20.4896H16.8592C17.659 20.4896 18.3094 19.8392 18.3096 19.0395V7.16152V7.11152H18.2596H13.2983V1.56036ZM5.14094 0.05H14.3549L19.77 6.10242V19.0395C19.77 20.6445 18.4643 21.95 16.8597 21.95H5.14094C3.53616 21.95 2.23042 20.6443 2.23042 19.0395V2.96052C2.23042 1.35574 3.53616 0.05 5.14094 0.05Z"
        fill="#8C969F"
        stroke="white"
        strokeWidth="0.1"
      />
      <path
        d="M5.46553 17.2828V15.8225H16.5343V17.2828H5.46553Z"
        fill="#8C969F"
        stroke="white"
        strokeWidth="0.1"
      />
      <path
        d="M5.46553 13.8922V12.4318H16.5343V13.8922H5.46553Z"
        fill="#8C969F"
        stroke="white"
        strokeWidth="0.1"
      />
      <path
        d="M5.46553 10.5016V9.04121H16.5343V10.5016H5.46553Z"
        fill="#8C969F"
        stroke="white"
        strokeWidth="0.1"
      />
    </svg>
  );
};

export default AccountNoticeIcon;
