import { ICONS } from './icons';

export default function IconsList() {
  return (
    <div className="px-8">
      <p className="text-2xl px-8">Icons</p>
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-16 mt-8">
        {ICONS.map((icon, idx) => (
          <span
            className="flex flex-col justify-center items-center"
            key={`icons-${idx}`}
          >
            <icon.icon className="w-10 h-10"></icon.icon>
            <span>{icon.title}</span>
          </span>
        ))}
      </div>
    </div>
  );
}
