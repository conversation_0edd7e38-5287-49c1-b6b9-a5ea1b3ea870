const FilterIcon = ({
  color = 'currentColor',
  width = '18px',
  height = '14px',
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 18 14"
    >
      <g
        id="Group_36196"
        data-name="Group 36196"
        transform="translate(-925 -1122.489)"
      >
        <path
          id="Path_22590"
          data-name="Path 22590"
          d="M942.581,1295.564H925.419c-.231,0-.419-.336-.419-.75s.187-.75.419-.75h17.163c.231,0,.419.336.419.75S942.813,1295.564,942.581,1295.564Z"
          transform="translate(0 -169.575)"
          fill={color}
        />
        <path
          id="Path_22591"
          data-name="Path 22591"
          d="M942.581,1951.5H925.419c-.231,0-.419-.336-.419-.75s.187-.75.419-.75h17.163c.231,0,.419.336.419.75S942.813,1951.5,942.581,1951.5Z"
          transform="translate(0 -816.512)"
          fill={color}
        />
        <path
          id="Path_22593"
          data-name="Path 22593"
          d="M1163.713,1122.489a2.5,2.5,0,1,0,1.768.732A2.483,2.483,0,0,0,1163.713,1122.489Z"
          transform="translate(-233.213)"
          fill={color}
        />
        <path
          id="Path_22594"
          data-name="Path 22594"
          d="M2344.886,1779.157a2.5,2.5,0,1,0,.731,1.768A2.488,2.488,0,0,0,2344.886,1779.157Z"
          transform="translate(-1405.617 -646.936)"
          fill={color}
        />
      </g>
    </svg>
  );
};

export default FilterIcon;
