import { useTranslation } from 'next-export-i18n';
import Heading from '@components/ui/heading';
import CategoryFilterMenu from '@components/search/category-filter-menu';
import Scrollbar from '@components/ui/scrollbar';
import { Category } from '@framework/types';
import cn from 'classnames';

interface CategoryFilterProps {
  categories: Category[];
  className?: string;
}

export const CategoryFilter = ({
  categories,
  className,
}: CategoryFilterProps) => {
  const { t } = useTranslation();

  return (
    <>
      {categories.length > 0 && (
        <div className={cn('block', className)}>
          <Heading className="mb-5 -mt-1">{t('text-categories')}</Heading>
          <Scrollbar className="category-filter-scrollbar overflow-hidden rounded border border-skin-base">
            <CategoryFilterMenu items={categories} />
          </Scrollbar>
        </div>
      )}
    </>
  );
};
