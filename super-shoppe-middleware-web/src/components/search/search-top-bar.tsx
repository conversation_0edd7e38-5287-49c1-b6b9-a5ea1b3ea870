import { Drawer } from '@components/common/drawer/drawer';
import FilterIcon from '@components/icons/filter-icon';
import { useUI } from '@contexts/ui.context';
import FilterSidebar from '@components/search/filter-sidebar';
import ListBox from '@components/ui/filter-list-box';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-export-i18n';
import { getDirection } from '@utils/get-direction';
import { productSortingOptions } from '@framework/static/sorting-options';

interface SearchTopBarProps {
  itemsCount?: number;
}

const SearchTopBar = ({ itemsCount = 0 }: SearchTopBarProps) => {
  const { openFilter, displayFilter, closeFilter } = useUI();
  const { t } = useTranslation();
  const { locale } = useRouter();
  const dir = getDirection(locale);
  const contentWrapperCSS = dir === 'ltr' ? { left: 0 } : { right: 0 };
  return (
    <div className="flex justify-between items-center mb-6">
      <button
        className="lg:hidden text-skin-base text-sm px-4 py-2 font-semibold border border-skin-base rounded-md flex items-center transition duration-200 ease-in-out focus:outline-none hover:border-skin-primary hover:text-skin-primary"
        onClick={openFilter}
      >
        <FilterIcon width="14px" height="10px" />
        <span className="text-xs sm:text-sm ps-2.5">{t('text-filters')}</span>
      </button>
      <div className="flex w-full items-center justify-end lg:justify-between">
        <div className="flex-shrink-0 text-skin-base font-medium text-15px leading-4 md:me-6 hidden lg:block mt-0.5">
          {itemsCount} {t('text-items-found')}
        </div>
        <ListBox
          options={productSortingOptions.map(({ label, query_value }) => ({
            name: label,
            value: query_value,
          }))}
        />
      </div>
      <Drawer
        placement={dir === 'rtl' ? 'right' : 'left'}
        open={displayFilter}
        onClose={closeFilter}
        handler={false}
        showMask={true}
        level={null}
        contentWrapperStyle={contentWrapperCSS}
      >
        <FilterSidebar itemsCount={itemsCount} />
      </Drawer>
    </div>
  );
};

export default SearchTopBar;
