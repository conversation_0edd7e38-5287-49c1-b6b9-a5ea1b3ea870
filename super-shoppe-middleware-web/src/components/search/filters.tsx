import isEmpty from 'lodash/isEmpty';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-export-i18n';
import Heading from '@components/ui/heading';
import { FilteredItem } from './filtered-item';
import CategoryListCardLoader from '@components/ui/loaders/category-list-card-loader';
import { productSortingOptions } from '@framework/static/sorting-options';
import { useWindowSize } from 'react-use';
import { useSearchFilterCategoriesQuery } from '@framework/category/get-all-categories';
import { CategoryFilter } from './category-filter';
import { BrandFilter } from '@components/brand/brand-filter';
import Scrollbar from '@components/ui/scrollbar';
import SwitchComponent from '@components/ui/switch';
import { ROUTES } from '@utils/routes';

interface ShopFiltersProps {}

function getSortOptionName(sort_by: string) {
  const sort_option = productSortingOptions.find(
    ({ query_value }) => sort_by === query_value
  );
  return sort_option?.label ?? null;
}

export const ShopFilters: React.FC<ShopFiltersProps> = ({}) => {
  const router = useRouter();
  const { pathname, query } = router;
  const { t } = useTranslation();
  const { width } = useWindowSize();

  const { data, isLoading } = useSearchFilterCategoriesQuery({
    category_slugs: query?.categories,
    ...(query?.text || query?.category || query?.brand
      ? {
          keyword: query?.text as string,
          category_id: query?.category as string,
          brand_id: query?.brand as string,
        }
      : {}),
    limit: 30,
  });

  function handleToggleAvailable() {
    router.push({
      pathname,
      query: {
        ...query,
        show_available:
          !query?.show_available || query?.show_available === '0' ? '1' : '0',
      },
    });
  }

  return (
    <div className="space-y-10">
      {!isEmpty(query) && (
        <div className="block -mb-3">
          <div className="flex items-center justify-between mb-4 -mt-1">
            <Heading>{t('text-filters')}</Heading>
            <button
              className="flex-shrink text-13px transition duration-150 ease-in focus:outline-none hover:text-skin-base"
              aria-label={t('text-clear-all')}
              onClick={() => {
                const { keyword, category, brand, ...rest } = query;
                router.push({
                  pathname,
                  query: rest,
                });
              }}
            >
              {t('text-clear-all')}
            </button>
          </div>
          {}
          <div className="flex flex-wrap -m-1">
            {query.text && (
              <FilteredItem
                itemKey={'text'}
                itemName={'Search'}
                itemValue={query.text as string}
                key={'search'}
              />
            )}
            {query.category &&
              data &&
              data.categories.data.current_category && (
                <FilteredItem
                  itemKey={'category'}
                  itemName={'Category'}
                  itemValue={data.categories.data.current_category.name}
                  key={'category-filter'}
                />
              )}
            {query.brand && data && data.categories.data.current_brand && (
              <FilteredItem
                itemKey={'brand'}
                itemName={'Brand'}
                itemValue={data.categories.data.current_brand.name}
                key={'brand-filter'}
              />
            )}
            {/* {query.sort_by && (
              <FilteredItem
                itemKey={'sort_by'}
                itemName={'Sort By'}
                itemValue={t(getSortOptionName(query.sort_by as string) ?? '')}
                key={'sorting'}
              />
            )} */}
          </div>
        </div>
      )}
      {/* <div className="flex items-center">
        <SwitchComponent
          checked={!!query?.show_available && query.show_available === '1'}
          onChange={handleToggleAvailable}
          srText="Show only available"
        />
        <span
          className="text-sm pl-4 cursor-pointer"
          onClick={handleToggleAvailable}
        >
          Show only available
        </span>
      </div> */}
      <div>
        <div className="max-h-full w-full">
          <div className="flex flex-col gap-10">
            {(pathname != ROUTES.CATEGORY_VIEW ||
              (pathname == ROUTES.CATEGORY_VIEW && width > 1024)) && (
              <>
                {isLoading || !data ? (
                  <div className="hidden xl:block">
                    <div className="w-72 mt-8 px-2">
                      <CategoryListCardLoader uniqueKey="category-list-card-loader" />
                    </div>
                  </div>
                ) : (
                  <CategoryFilter
                    categories={[
                      ...data.categories.data.categories,
                      ...data.categories.data.brands,
                    ]}
                  />
                )}
              </>
            )}

            {/* {isLoading || !data ? (
              <div className="hidden xl:block">
                <div className="w-72 mt-8 px-2">
                  <CategoryListCardLoader uniqueKey="brand-list-card-loader" />
                </div>
              </div>
            ) : (
              <BrandFilter brands={data.categories.data.brands} />
            )} */}
          </div>
        </div>
      </div>
    </div>
  );
};
