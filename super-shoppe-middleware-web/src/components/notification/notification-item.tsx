import { Notification } from "@framework/types";
import cn from "classnames";
import moment from "moment";

interface NotificationItemProps {
  notification: Notification;
  className?: string;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, className = "px-4 py-2" }) => {
  const { data: { title, body }, created_at } = notification;
  return (
    <div className={cn(className)}>
      <div className="text-sm md:text-lg text-skin-base font-bold font-manrope mb-1">{title}</div>
      <div className="text-skin-muted text-xs md:text-sm mb-2">{body}</div>
      <div className="text-gray-400 text-[10px] md:text-xs">{moment(created_at).format('DD MMM YYYY h:mm A')}</div>
    </div>
  );
}

export default NotificationItem;