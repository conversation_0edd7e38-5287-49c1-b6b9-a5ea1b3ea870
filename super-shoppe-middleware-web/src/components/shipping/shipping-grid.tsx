import { useState } from 'react';
import { RadioGroup } from '@headlessui/react';
import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';
import PriceRenderer from '@components/common/price-renderer';
import isNumber from 'lodash/isNumber';
import { parseToFloat } from '@framework/product/use-price';

interface ShippingGridProps {
  shippings?: any;
  onSelected?: any;
  current_selected?: any;
}

const ShippingMethodRadio = ({
  data,
  value,
  onChange,
}: {
  data: any;
  value: any;
  onChange: any;
}) => (
  <div>
    <div
      className={
        `${value && data.id === value.id ? 'border-skin-primary' : 'border-skin-base'}
          border-2 relative shadow-md focus:outline-none  p-5 block cursor-pointer min-h-[60px] h-full group address__box`
      }
      onClick={() => onChange(data)}
    >
      <h3 className="text-skin-base font-semibold mb-2 -mt-1">
        {data.name}
      </h3>
      {data.description ? (
        <div
          className="text-skin-muted leading-6"
        >
          {data.description}
        </div>
      ) : (
        <></>
      )}
      {isNumber(parseToFloat(data.amount)) ? (
        <div
          className="text-skin-muted leading-8"
        >
          <PriceRenderer price={data.amount} />
        </div>
      ) : (
        <></>
      )}
    </div>
  </div>
);

const ShippingGrid: React.FC<ShippingGridProps> = ({
  shippings,
  onSelected,
  current_selected,
}) => {
  function handleShippingSelected(e: any) {
    if (onSelected) {
      onSelected(e);
    }
  }

  return (
    <div className="w-full">
      <div className="w-full mx-auto">
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
          {shippings.map((gateway: any, idx: number) => (
            <div key={idx} className="px-1 mb-2">
              <ShippingMethodRadio
                data={gateway}
                value={current_selected}
                onChange={handleShippingSelected}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ShippingGrid;
