import {
  Swiper,
  SwiperSlide,
  SwiperOptions,
  Navigation,
  Thumbs,
} from '@components/ui/carousel/slider';
import Image from '@components/ui/image';
import { useEffect, useRef, useState } from 'react';
import cn from 'classnames';
import { productGalleryPlaceholder } from '@assets/placeholders';
import { getDirection } from '@utils/get-direction';
import { useRouter } from 'next/router';
import { IoIosArrowBack, IoIosArrowForward } from 'react-icons/io';
import SwiperCore from 'swiper';
import { ProductVariant } from '@framework/types';
import ReactPlayer from 'react-player/youtube';

interface Props {
  id?: any;
  gallery: any[];
  banner?: any;
  currentVariant?: ProductVariant;
}

// product gallery breakpoints
const galleryCarouselBreakpoints = {
  '0': {
    slidesPerView: 4,
  },
};

const swiperParams: SwiperOptions = {
  slidesPerView: 1,
  direction: 'horizontal',
  spaceBetween: 0,
};

SwiperCore.use([Navigation, Thumbs]);

const ThumbnailCarousel: React.FC<Props> = ({
  id,
  gallery,
  banner,
  currentVariant,
}) => {
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperCore>();
  const [swiper, setSwiper] = useState<SwiperCore>();
  const prevRef = useRef<HTMLDivElement>(null);
  const nextRef = useRef<HTMLDivElement>(null);
  const { locale } = useRouter();
  const dir = getDirection(locale);

  useEffect(() => {
    if (currentVariant && gallery && swiper) {
      const variantIndex = gallery.findIndex(
        ({ variant_id }) => variant_id === currentVariant.id
      );
      if (variantIndex > -1) {
        // swiper.activeIndex = variantIndex;
        // swiper.updateSlides();
        swiper.slideTo(variantIndex);
        thumbsSwiper?.updateSlidesClasses();
      }
    }
  }, [currentVariant]);

  return (
    <div className="w-full">
      <div className="w-full border border-skin-base relative">
        <div className="w-full xl:w-2/3 mx-auto overflow-hidden rounded-md relative">
          <Swiper
            id="productGallery"
            thumbs={{ swiper: thumbsSwiper }}
            {...gallery.length > 1 ? {
              navigation: {
                prevEl: prevRef.current!, // Assert non-null
                nextEl: nextRef.current!, // Assert non-null
              }
            } : {}}
            onSwiper={setSwiper}
            {...swiperParams}
          >
            {gallery?.map((item: any, index) => {
              let display_src: any = null;
              switch (item.type) {
                case 'video':
                  display_src = (
                    <video
                      src={item.original}
                      controls
                      muted
                      width={650}
                      height={590}
                      onEnded={() => {
                        swiper && swiper?.slideNext();
                      }}
                    />
                  );
                  break;
                case 'youtube':
                  display_src = (
                    <ReactPlayer
                      id={`youtube-${index}`}
                      className="w-full h-full object-fill"
                      url={item.original}
                      controls={true}
                      muted={true}
                      width="100%"
                      height="100%"
                      stopOnUnmount
                      onEnded={() => {
                        swiper && swiper.slideNext();
                      }}
                    />
                  );
                  break;
                default:
                  display_src = (
                    <img
                      src={item?.original ?? productGalleryPlaceholder.src}
                      alt={`Product gallery ${id}`}
                      width={650}
                      height={590}
                      className="rounded-lg"
                    />
                  );
                  break;
              }
              return (
                <SwiperSlide
                  key={`product-gallery-${id}-${index}`}
                  className="flex items-center justify-center relative"
                >
                  {display_src}
                  {(banner && banner?.original && index === 0) ? (
                    <img
                      src={banner?.original}
                      alt={`Product bottom banner ${index + 1}`}
                      className="absolute w-full h-full start-0 bottom-0"
                    />
                  ) : <></>}
                </SwiperSlide>
              );
            })}
          </Swiper>
        </div>
        {gallery.length > 1 ? (
          <div className="flex items-center justify-between w-full text-skin-primary absolute top-2/4 z-10 px-1">
            <div
              ref={prevRef}
              className="w-7 h-7 md:w-8 md:h-8 text-base lg:text-lg xl:text-xl flex items-center cursor-pointer justify-center rounded-full bg-skin-fill border border-skin-primary transition duration-300 hover:bg-skin-primary hover:text-skin-inverted focus:outline-none transform -translate-y-1/2 shadow-navigation"
            >
              {dir === 'rtl' ? <IoIosArrowForward /> : <IoIosArrowBack />}
            </div>
            <div
              ref={nextRef}
              className="w-7 h-7 md:w-8 md:h-8 text-base lg:text-lg xl:text-xl flex items-center justify-center cursor-pointer rounded-full bg-skin-fill border border-skin-primary transition duration-300 hover:bg-skin-primary hover:text-skin-inverted focus:outline-none transform -translate-y-1/2 shadow-navigation"
            >
              {dir === 'rtl' ? <IoIosArrowBack /> : <IoIosArrowForward />}
            </div>
          </div>
        ) : <></>}
      </div>
      {/* End of product main slider */}

      <div className="w-full mt-2">
        <Swiper
          id="productGalleryThumbs"
          onSwiper={setThumbsSwiper}
          spaceBetween={8}
          watchSlidesProgress={true}
          observer={true}
          observeParents={true}
          breakpoints={galleryCarouselBreakpoints}
          className="w-full"
        >
          {gallery?.map((item: any, index) => {
            let display_src: any = null;
            switch (item.type) {
              case 'video':
                display_src = (
                  <video
                    src={item.original}
                    controls={false}
                    muted
                    width={170}
                    height={170}
                  />
                );
                break;
              case 'youtube':
                display_src = (
                  <img
                    src={
                      item?.thumbnail ??
                      item?.original ??
                      productGalleryPlaceholder.src
                    }
                    alt={`Youtube thumb gallery ${id}`}
                    width={170}
                    height={170}
                  />
                );
                break;
              default:
                display_src = (
                  <img
                    src={
                      item?.thumbnail ??
                      item?.original ??
                      productGalleryPlaceholder.src
                    }
                    alt={`Product thumb gallery ${id}`}
                    width={170}
                    height={170}
                  />
                );
                break;
            }
            return (
              <SwiperSlide
                key={`product-thumb-gallery-${id}-${index}`}
                className="flex items-center justify-center cursor-pointer overflow-hidden border-2 border-skin-base transition hover:opacity-75 relative"
              >
                {display_src}
                {(banner && banner?.original && index === 0) ? (
                  <img
                    src={banner?.original}
                    alt={`Product bottom banner ${index + 1}`}
                    className="absolute w-full h-full start-0 bottom-0"
                  />
                ) : <></>}
              </SwiperSlide>
            );
          })}
        </Swiper>
      </div>
    </div>
  );
};

export default ThumbnailCarousel;
