import ContentLoader from 'react-content-loader';

const StoreListLoader = (props: any) => (
  <ContentLoader
    speed={2}
    height={20}
    markerHeight={30}
    capHeight={30}
    viewBox="0 0 226 40"
    backgroundColor="#F3F6FA"
    foregroundColor="#E7ECF3"
    className="w-full h-auto shadow-card bg-white rounded-md overflow-hidden mb-2"
    {...props}
  >
    <rect x="0" y="0" rx="0" ry="0" width="226" height="50" />
    <rect x="18" y="203" rx="3" ry="3" width="79" height="8" />
  </ContentLoader>
);

export default StoreListLoader;
