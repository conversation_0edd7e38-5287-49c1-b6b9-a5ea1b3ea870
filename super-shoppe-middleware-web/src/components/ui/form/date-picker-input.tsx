import cn from 'classnames';
import React from 'react';
import { Controller } from 'react-hook-form';
import { DatePicker } from './date-picker';

interface DatePickerInputProps {
  control: any;
  name: string;
  className?: string;
  [key: string]: unknown;
}

const classes = {
  root: '', //'py-2 px-4 md:px-5 w-full appearance-none transition duration-150 ease-in-out border text-input text-13px lg:text-sm font-body rounded-md placeholder-[#B3B3B3] transition duration-200 ease-in-out bg-skin-fill border-skin-two focus:border-2  focus:outline-none focus:border-skin-primary h-11 md:h-12',
};

const DatePickerInput = React.forwardRef<
  HTMLInputElement,
  DatePickerInputProps
>(
  (
    {
      control,
      name,
      className,
      datePickerProps,
      ...rest
    }: DatePickerInputProps,
    ref
  ) => {
    const rootClassName = cn(classes.root, className);
    return (
      <Controller
        control={control}
        name={name}
        {...rest}
        render={({ field }) => (
          //@ts-ignore
          <DatePicker
            {...field}
            {...datePickerProps}
            selected={field.value}
            className={rootClassName}
          />
        )}
      />
    );
  }
);

export default DatePickerInput;
