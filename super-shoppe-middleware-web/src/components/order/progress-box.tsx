import { <PERSON><PERSON><PERSON>heck } from 'react-icons/bi';
import Scrollbar from '@components/ui/scrollbar';
import { ImCheckmark } from 'react-icons/im';
import cn from 'classnames';

type ProgressProps = {
  data: any;
  status: any;
};

const ProgressBox: React.FC<ProgressProps> = ({ status, data }) => {
  return (
    <Scrollbar
      className="w-full h-full"
      options={{
        scrollbars: {
          autoHide: 'never',
        },
      }}
    >
      <div className="flex flex-col py-7 md:items-start md:justify-start w-full md:flex-row">
        {data?.data.map(
          (item: any) =>
            ((!!item.default &&
              (!!status?.default ||
                (!status?.default && item.serial < status?.serial!))) ||
              (!item.default && status?.id == item.id)) && (
              <div className="progress_container" key={item.id}>
                <div
                  className={cn(
                    'progress_wrapper',
                    status?.serial && status.serial >= item.serial
                      ? 'checked'
                      : ''
                  )}
                >
                  <div className="status_wrapper">
                    {status?.serial && status.serial >= item.serial ? (
                      <div className="w-3 h-4">
                        <ImCheckmark className="w-full" />
                      </div>
                    ) : (
                      item.serial
                    )}
                  </div>
                  <div className="bar" />
                </div>

                <div className="flex flex-col items-start ms-5 md:items-center md:ms-0">
                  {item && (
                    <span className="text-base text-body-dark capitalize font-semibold text-start md:text-center md:px-2">
                      {item?.name}
                    </span>
                  )}
                </div>
              </div>
            )
        )}
      </div>
    </Scrollbar>
  );
};

export default ProgressBox;
