import { productPlaceholder } from '@assets/placeholders';
import PriceRenderer from '@components/common/price-renderer';
import RedeemUpTo from '@components/product/redeem-up-to';
import { parseToFloat } from '@framework/product/use-price';
import { OrderItem } from '@framework/types';
import cn from 'classnames';
import OrderProductViewVoucher from './order-product-view-voucher';

const OrderItemCard = ({
  product,
  className,
}: {
  product: OrderItem;
  className?: string;
}) => {
  return (
    <tr
      className={cn(
        'border-b font-normal border-skin-base last:border-b-0',
        className
      )}
      key={product.id}
    >
      <td className="p-4 flex flex-col sm:flex-row">
        <div className="flex-shrink-0 relative">
          <img
            src={product.image ?? productPlaceholder.src}
            width={64}
            height={64}
            loading="eager"
            alt={product.name || 'Product Image'}
            className="bg-skin-thumbnail me-2"
            style={{ aspectRatio: '1 / 1' }}
          />
          {product.banner ? (
            <img
              src={product.banner}
              alt={product.name || 'Product Image'}
              className="absolute w-full h-full start-0 bottom-0"
            />
          ) : (
            <></>
          )}
        </div>
        <span className="flex flex-col">
          <span>{product.name}</span>
          <span className="text-xs">Quantity: {product.order_quantity}</span>
        </span>
      </td>
      <td className="p-4">
        <div className="flex flex-col">
          <div className="flex flex-col">
            <PriceRenderer price={parseToFloat(product.unit_price)} />
            <RedeemUpTo points={product.max_redeemable_points} />
          </div>
          <div className="flex flex-col">
            {product.coupons?.map((coupon, index) => (
              <OrderProductViewVoucher coupon={coupon} key={index} className="mt-2" />
            ))}
          </div>
        </div>
      </td>
    </tr>
  );
};

export default OrderItemCard;
