import { Coupon } from "@framework/types"
import { useState } from "react";
import { ImCheckmark, ImPaste } from "react-icons/im";

interface OrderProductViewVoucherProps {
    coupon: Coupon;
    className?: string;
}

const OrderProductViewVoucher: React.FC<OrderProductViewVoucherProps> = ({
    coupon,
    className,
}) => {
    const [isShowCode, setIsShowCode] = useState(false);
    const [showCopied, setShowCopied] = useState(false);
    function toggleShowCode() {
        setIsShowCode((prev) => !prev);
    }
    function handleCopy() {
        setShowCopied(true);
        navigator.clipboard.writeText(coupon.code);
        setTimeout(() => {
            setShowCopied(false);
        }, 2000);
    }
    return (
        isShowCode
            ? (
                <div className={className}>
                    <div className="flex items-center gap-1">
                        <div className="text-xs font-semibold">{coupon.code}</div>
                        <button
                            type="button"
                            className="rounded-md hover:bg-gray-200 transition-colors duration-200 px-2 py-2"
                            onClick={handleCopy}
                        >
                            {showCopied ? <ImCheckmark className="text-green-500" width={12} /> : <ImPaste width={12} />}
                        </button>
                    </div>
                    <div className="text-[10px] text-skin-primary underline" onClick={toggleShowCode}>Hide</div>
                </div>
            )
            : (
                <div className={className}>
                    <div className="text-[10px] text-skin-primary underline" onClick={toggleShowCode}>Tap to show voucher code</div>
                    <div className="w-20 h-4 bg-gray-200 rounded-md">&nbsp;</div>
                </div>
            )
    );
}

export default OrderProductViewVoucher