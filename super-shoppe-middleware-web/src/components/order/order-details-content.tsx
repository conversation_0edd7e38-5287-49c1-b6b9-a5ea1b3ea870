import { productPlaceholder } from '@assets/placeholders';
import PriceRenderer from '@components/common/price-renderer';
import { OrderItem } from '@framework/types';

export const OrderDetailsContent: React.FC<{ item: OrderItem }> = ({ item }) => {
  return (
    <div className="relative grid grid-cols-12 py-2 pb-0 border-b border-solid border-skin-base text-[12px] md:text-[14px]">
      <div className="col-span-2 self-center pb-2">
        <img
          src={item.image ?? productPlaceholder.src}
          alt={item.name || 'Product Image'}
          width="60"
          height="60"
          className="object-cover"
        />
      </div>
      <div className="col-span-5 self-center">
        <h2 className="text-skin-base">{item.name}</h2>
      </div>
      <div className="col-span-3 self-center md:text-start text-center">
        <p>{item.order_quantity}x</p>
      </div>
      <div className="col-span-2 self-center">
        <p>
          <PriceRenderer price={item.subtotal} />
        </p>
      </div>
    </div>
  );
};
