import { useRouter } from 'next/router';
import { useOrderQuery } from '@framework/order/get-order';
import NoResult from '@components/common/no-result';
import PageLoader from '@components/ui/loaders/page-loader';
import OrderPdfPreview from './order-pdf-preview';
import cn from 'classnames';
import Heading from '@components/ui/heading';

const OrderDetailsPrint: React.FC<{ className?: string }> = ({
  className = '',
}) => {
  const {
    query: { order_id: id },
    pathname,
  } = useRouter();
  const {
    data: order,
    isLoading,
    error,
  } = useOrderQuery(id?.toString()!, pathname.indexOf('track-order') > -1);

  if (isLoading || !order) return <PageLoader />;

  if (error) {
    return (
      <div>
        <div>
          <h4>Order Number: {id?.toString()!}</h4>
        </div>
        <NoResult />
      </div>
    );
  }

  return (
    <div className={cn('w-full h-screen', className)}>
      <Heading className="text-center">
        Receipt for Order {order.display_id}
      </Heading>
      <OrderPdfPreview order={order} />
    </div>
  );
};

export default OrderDetailsPrint;
