import { Order, Product } from '@framework/types';
import {
  Page,
  Text,
  View,
  Image,
  Document,
  StyleSheet,
  Font,
} from '@react-pdf/renderer';
import { siteSettings } from '@settings/site-settings';
import { formatAddress } from '@utils/format-address';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import PriceRenderer from '@components/common/price-renderer';
import { getProductVariantName } from '@framework/product/product.utils';
import { parseToFloat } from '@framework/product/use-price';

export default function OrderPdf({ order }: { order: Order }) {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  return (
    <Document>
      <Page size="A4">
        <View style={styles.container}>
          {/* Logo */}
          <View style={[styles.fullWidth]}>
            <View style={[styles.addressTextRight, { marginLeft: 'auto' }]}>
              <Image
                src={`${window.location.origin}/assets/images/logo.png`}
                style={[styles.logo]}
              />
            </View>
          </View>

          {/* Address */}
          <View style={styles.addressWrapper}>
            <View style={styles.section}>
              <Text style={[styles.addressText, { marginBottom: 20 }]}>
                Order Number:{' '}
                <Text style={{ color: '#374151', fontFamily: 'Lato Bold' }}>
                  {order.display_id}
                </Text>
              </Text>
              <Text
                style={[
                  styles.addressText,
                  { color: '#374151', fontFamily: 'Lato Bold', fontSize: 12 },
                ]}
              >
                {order?.shipping_address?.recipient}
              </Text>
              <Text style={styles.addressText}>{order?.customer_email}</Text>
              <Text style={styles.addressText}>
                {order.shipping_address.contact_number
                  ? order.shipping_address.contact_number
                  : order.customer_contact}
              </Text>
              <Text style={styles.addressText}>
                {formatAddress(order?.shipping_address)}
              </Text>
            </View>

            <View style={[styles.section]}>
              <Text style={[styles.addressTextRight, { marginBottom: 20 }]}>
                Order Date:{' '}
                {dayjs(order.created_at)
                  .tz(dayjs.tz.guess())
                  .format('D MMMM, YYYY')}
              </Text>
              {/* <Text style={[styles.addressTextRight, { marginBottom: 20 }]}>
                Printed Date:{' '}
                {dayjs().tz(dayjs.tz.guess()).format('D MMMM, YYYY')}
              </Text> */}
              <Text
                style={[
                  styles.addressTextRight,
                  { color: '#374151', fontFamily: 'Lato Bold', fontSize: 12 },
                ]}
              >
                {siteSettings.author.name}
              </Text>
              <Text style={styles.addressTextRight}>
                {siteSettings.author.supportEmail}
              </Text>
              <Text style={styles.addressTextRight}>
                {siteSettings.author.address}
              </Text>
            </View>
          </View>

          {/* Table */}
          <View style={styles.orderTable}>
            {order.products.map(({ name, order_quantity, subtotal }, index) => {
              return (
                <View style={styles.tbody} key={index}>
                  <View style={styles.tr}>
                    <Text
                      style={[styles.td, { width: 50, textAlign: 'center' }]}
                    >
                      {index + 1}
                    </Text>
                    <Text style={[styles.td, { flex: 1 }]}>
                      {name}
                    </Text>
                    <Text
                      style={[styles.td, { width: 100, textAlign: 'center' }]}
                    >
                      x{order_quantity}
                    </Text>
                    <Text
                      style={[styles.td, { width: 100, textAlign: 'right' }]}
                    >
                      <PriceRenderer price={parseToFloat(subtotal)} />
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>

          {/* Border */}
          <View style={styles.singleBorder} />

          {/* Total */}
          <View style={styles.totalCountWrapper}>
            <View style={styles.totalCountRow}>
              <Text style={styles.totalCountCell}>Sub Total</Text>
              <Text style={styles.totalCountCell}>
                <PriceRenderer price={order.amount} />
              </Text>
            </View>
            <View style={styles.totalCountRow}>
              <Text style={styles.totalCountCell}>Tax</Text>
              <Text style={styles.totalCountCell}>
                <PriceRenderer price={order.sales_tax} />
              </Text>
            </View>
            {order.points_discount && order.points_discount > 0 ? (
              <View style={styles.totalCountRow}>
                <Text style={styles.totalCountCell}>Redemption Discount</Text>
                <Text style={styles.totalCountCell}>
                  -<PriceRenderer price={order.points_discount!} />
                </Text>
              </View>
            ) : (
              <></>
            )}
            {order.points_discount_refunded && order.points_discount_refunded > 0 ? (
              <View style={styles.totalCountRow}>
                <Text style={styles.totalCountCell}>Refunded Redemption</Text>
                <Text style={styles.totalCountCell}>
                  -<PriceRenderer price={order.points_discount_refunded!} />
                </Text>
              </View>
            ) : (
              <></>
            )}
            {order.coupon_discount && order.coupon_discount > 0 && (
              <View style={styles.totalCountRow}>
                <Text style={styles.totalCountCell}>Voucher Discount</Text>
                <Text style={styles.totalCountCell}>
                  -<PriceRenderer price={order.coupon_discount!} />
                </Text>
              </View>
            )}
            {order.discount && order.discount > 0 && (
              <View style={styles.totalCountRow}>
                <Text style={styles.totalCountCell}>Discount</Text>
                <Text style={styles.totalCountCell}>
                  -<PriceRenderer price={order.discount!} />
                </Text>
              </View>
            )}
            <View style={styles.totalCountRow}>
              <Text style={styles.totalCountCell}>Delivery Fee</Text>
              <Text style={styles.totalCountCell}>
                <PriceRenderer price={order.delivery_fee!} />
              </Text>
            </View>
            {order.store_credit && order.store_credit > 0 && (
              <View style={styles.totalCountRow}>
                <Text style={styles.totalCountCell}>Store Credit</Text>
                <Text style={styles.totalCountCell}>
                  <PriceRenderer price={order.store_credit!} />
                </Text>
              </View>
            )}
            <View style={styles.totalCountRow}>
              <Text
                style={[
                  styles.totalCountCell,
                  { fontSize: 12, fontFamily: 'Lato Bold' },
                ]}
              >
                Total
              </Text>
              <Text
                style={[
                  styles.totalCountCell,
                  { fontSize: 12, fontFamily: 'Lato Bold' },
                ]}
              >
                <PriceRenderer price={order.total} />
              </Text>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
}

Font.register({
  family: 'Lato',
  src: `https://fonts.gstatic.com/s/lato/v16/S6uyw4BMUTPHjx4wWw.ttf`,
});

Font.register({
  family: 'Lato Bold',
  src: `https://fonts.gstatic.com/s/lato/v16/S6u9w4BMUTPHh6UVSwiPHA.ttf`,
});

const styles = StyleSheet.create({
  container: {
    maxWidth: 600,
    flex: 1,
    margin: '50pt',
    fontFamily: 'Lato',
  },

  addressWrapper: {
    display: 'flex',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 30,
  },

  section: {
    width: '40%',
    display: 'flex',
    flexDirection: 'column',
  },

  addressText: {
    fontSize: 11,
    color: '#6B7280',
    fontWeight: 400,
    marginBottom: 5,
  },
  addressTextRight: {
    fontSize: 11,
    color: '#6B7280',
    fontWeight: 400,
    marginBottom: 5,
    textAlign: 'right',
  },

  orderTable: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
  },

  thead: {
    width: '100%',
    backgroundColor: '#F3F4F6',
    display: 'flex',
    flexDirection: 'row',
  },

  th: {
    fontSize: 11,
    fontFamily: 'Lato Bold',
    color: '#374151',
    padding: '12pt 16pt',
    borderRightWidth: 1,
    borderRightColor: '#ffffff',
    borderRightStyle: 'solid',
  },

  tbody: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
  },

  tr: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
  },

  td: {
    fontSize: 11,
    color: '#6B7280',
    padding: '12pt 16pt',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    borderTopStyle: 'solid',
    borderRightWidth: 1,
    borderRightColor: '#ffffff',
    borderRightStyle: 'solid',
  },

  fullWidth: {
    width: '100%',
    marginBottom: 10,
  },

  logo: {
    width: '100px',
    textAlign: 'right',
    marginBottom: 2,
  },

  singleBorder: {
    width: '50%',
    display: 'flex',
    marginLeft: 'auto',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    borderTopStyle: 'solid',
    marginBottom: 2,
  },

  totalCountWrapper: {
    width: '50%',
    display: 'flex',
    flexDirection: 'column',
    marginLeft: 'auto',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    borderTopStyle: 'solid',
  },

  totalCountRow: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  totalCountCell: {
    fontSize: 11,
    color: '#6B7280',
    padding: '8pt 16pt 2pt',
  },
});
