import Button from '@components/ui/button';
import Input from '@components/ui/form/input';
import { generateOrderLinkHref } from '@framework/utils/data-mappers';
import { ROUTES } from '@utils/routes';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { IoSearch } from 'react-icons/io5';

export default function OrderTrackForm() {
  const { t } = useTranslation();
  const router = useRouter();

  const {
    register,
    setError,
    handleSubmit,
    formState: { errors },
  } = useForm<{ tracking_number: string }>({
    defaultValues: { tracking_number: '' },
  });

  function onSubmit(values: { tracking_number: string }) {
    if (values.tracking_number.length <= 0) {
      setError(
        'tracking_number',
        {
          message: t('forms.track-number-required'),
        },
        { shouldFocus: true }
      );
      return false;
    }
    router.push(
      generateOrderLinkHref(values.tracking_number, ROUTES.TRACK_ORDER_VIEW)
    );
  }

  return (
    <div className="xl:px-32 2xl:px-44 3xl:px-56 py-16 lg:py-20">
      <form onSubmit={handleSubmit((data) => onSubmit(data))} noValidate>
        <div className="border border-skin-base bg-skin-secondary px-4 lg:px-5 py-4 rounded-md flex items-center justify-start text-skin-base text-sm md:text-base mb-6 lg:mb-8">
          <span className="w-10 h-10 me-3 lg:me-4 rounded-full bg-skin-primary bg-opacity-20 flex items-center justify-center flex-shrink-0">
            <IoSearch className="w-5 h-5 text-skin-primary" />
          </span>
          {t('text-track-orders')}
        </div>

        <ul className="border border-skin-base bg-skin-secondary rounded-md flex flex-col md:flex-row mb-7 lg:mb-8 xl:mb-10">
          <li className="text-skin-base font-semibold text-base lg:text-lg border-b md:border-b-0 md:border-r border-dashed border-skin-two px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
            <span className="text-xs block text-skin-muted font-normal leading-5 mb-4">
              {t('text-track-orders-note')}
            </span>
            <Input
              label="Order Number"
              {...register('tracking_number')}
              variant="outline"
              error={errors.tracking_number?.message}
              className="w-full md:w-96"
            />
          </li>
        </ul>

        <div className="text-center">
          <Button
            type="submit"
            className="h-11 md:h-12 w-18 mt-2 font-15px md:font-15px tracking-normal"
            variant="formButton"
          >
            {t('text-track')}
          </Button>
        </div>
      </form>
    </div>
  );
}
