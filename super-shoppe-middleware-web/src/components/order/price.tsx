import PriceRenderer from '@components/common/price-renderer';

export const TotalPrice: React.FC<{ total?: any }> = ({ total }) => {
  return (
    <span className="total_price">
      <PriceRenderer price={total} />
    </span>
  );
};

export const DiscountPrice: React.FC<{ discount?: any }> = ({ discount }) => {
  return (
    <>
      -<PriceRenderer price={discount} />
    </>
  );
};

export const DeliveryFee: React.FC<{ delivery?: any }> = ({ delivery }) => {
  return <PriceRenderer price={delivery} />;
};

export const SubTotalPrice: React.FC<{ subtotal?: any }> = ({ subtotal }) => {
  return <PriceRenderer price={subtotal} />;
};
