import { PDFDownloadLink } from '@react-pdf/renderer';
import { Order } from '@framework/types';
import InvoicePdf from './invoice-pdf';

const InvoiceDownloadLink = ({
  order,
  text = 'Print',
}: {
  order: Order;
  text?: string;
}) => {
  return (
    <PDFDownloadLink
      document={<InvoicePdf order={order} />}
      fileName={`invoice-${order.display_id}.pdf`}
    >
      {({ loading }: any) => (loading ? 'loading...' : text)}
    </PDFDownloadLink>
  );
};

export default InvoiceDownloadLink;
