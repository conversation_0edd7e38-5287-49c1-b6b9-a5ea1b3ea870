import {
  useModalAction,
  useModalState,
} from '@components/common/modal/modal.context';
import Button from '@components/ui/button';
import { useWithdrawRefundMutation } from '@framework/order/use-withdraw-order-refund.mutation';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { useTranslation } from 'next-export-i18n';
import { useQueryClient } from 'react-query';

const WithdrawCancelConfirm: React.FC = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { data } = useModalState();
  const { closeModal } = useModalAction();
  const { mutate: withdrawRefund, isLoading } = useWithdrawRefundMutation();
  function handleConfirm() {
    withdrawRefund(data.refund.id, {
      onSettled: () => {
        queryClient.invalidateQueries(`${API_ENDPOINTS.ORDERS}/${data.id}`);
        closeModal();
      },
    });
  }
  return (
    <>
      {data !== '' && (
        <>
          <div className="w-full md:w-[508px] mx-auto p-5 sm:p-8 bg-white rounded-xl relative">
            <p className="mb-0">{t('text-withdraw-cancel-title')}</p>
            <p>{t('text-withdraw-cancel-note')}</p>
            <div className="flex justify-center gap-4 mt-8">
              <Button
                onClick={() => closeModal()}
                className="w-1/2 text-skin-primary"
                variant="border"
              >
                {t('button-cancel')}
              </Button>
              <Button
                onClick={handleConfirm}
                className="w-1/2 bg-red-500"
                loading={isLoading}
                disabled={isLoading}
              >
                {t('button-confirm')}
              </Button>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default WithdrawCancelConfirm;
