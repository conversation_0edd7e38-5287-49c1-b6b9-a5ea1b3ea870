import Button from '@components/ui/button';
import { useForm, UseFormRegister } from 'react-hook-form';
import Heading from '@components/ui/heading';
import { useCancelOrderMutation } from '@framework/order/use-cancel-order.mutation';
import { useModalAction } from '@components/common/modal/modal.context';
import { toast } from 'react-toastify';
import { useTranslation } from 'next-export-i18n';
import TextArea from '@components/ui/form/text-area';

const CANCEL_REASON_OPTIONS = [
  'Seller did not do confirmation',
  'Change of delivery address',
  'Change of mind',
];

interface FormValues {
  reason: string;
  description: string;
}

const CancelReasonRadio = ({
  register,
}: {
  register: UseFormRegister<FormValues>;
}) => (
  <div>
    <h3 className="text-xl font-semibold text-center mb-6">
      Select Cancellation Reason
    </h3>
    {CANCEL_REASON_OPTIONS.map((value, index) => (
      <div className="flex gap-2 items-center" key={index}>
        <input
          {...register('reason')}
          id={`cancel-reason-${index}`}
          type="radio"
          value={value}
        />
        <label
          htmlFor={`cancel-reason-${index}`}
          className="text-gray-600 text-lg"
        >
          {value}
        </label>
      </div>
    ))}
  </div>
);

const CancelOrderForm: React.FC<{ className?: string; order: any }> = ({
  className = '',
  order,
}) => {
  const { t } = useTranslation();
  const { closeModal } = useModalAction();
  const { mutate: cancelOrder, isLoading } = useCancelOrderMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      reason: CANCEL_REASON_OPTIONS[0],
    },
  });

  function onSubmit(values: FormValues) {
    cancelOrder(
      {
        order_id: order.id,
        title: values.reason,
        description: values.description,
      },
      {
        onSuccess: () => {
          toast.success(t('text-cancellation-created'));
          closeModal();
        },
      }
    );
  }

  return (
    <div className={className} onSubmit={handleSubmit(onSubmit)}>
      <form noValidate>
        <CancelReasonRadio register={register} />
        <div className="mt-3 md:mt-4 mb-4">
          <TextArea
            variant="solid"
            label="forms.label-cancel-comment-star"
            {...register('description', {
              required: 'Cancellation comment is required',
            })}
            error={errors.description?.message}
          />
        </div>
        <div className="mt-6 md:mt-8 mb-4 text-center">
          <Button className="w-full" loading={isLoading} disabled={isLoading}>
            {t('button-confirm')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CancelOrderForm;
