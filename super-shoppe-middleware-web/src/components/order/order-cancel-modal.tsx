import { useModalState } from '@components/common/modal/modal.context';
import CancelOrderForm from './order-cancel-form';

const CancelOrderModal: React.FC = () => {
  const { data } = useModalState();
  return (
    <>
      {data !== '' && (
        <>
          <div className="w-full md:w-[508px] mx-auto p-5 sm:p-8 bg-white rounded-xl relative">
            <CancelOrderForm order={data} />
          </div>
        </>
      )}
    </>
  );
};

export default CancelOrderModal;
