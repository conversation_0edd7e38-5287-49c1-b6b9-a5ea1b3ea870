import PriceRenderer from '@components/common/price-renderer';
import Button from '@components/ui/button';
import Link from '@components/ui/link';
import { useUI } from '@contexts/ui.context';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';
import { useOrderBuyAgainMutation } from '@framework/cart/cart-buy-again.mutation';
import { Order } from '@framework/types';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { useWindowSize } from 'react-use';
import { productPlaceholder } from '@assets/placeholders';
import { generateOrderLinkHref } from '@framework/utils/data-mappers';
import moment from 'moment';
import RedeemUpTo from '@components/product/redeem-up-to';
import { useCheckout } from '@contexts/checkout.context';
import { useCheckoutPlaceOrder } from '@framework/checkout/use-checkout';
import OrderPayNowButton from './order-pay-now-button';
import { formatNumber } from '@utils/format-number';

export const CreatedAt: React.FC<{ createdAt: string }> = ({ createdAt }) => {
  dayjs.extend(relativeTime);
  dayjs.extend(utc);
  dayjs.extend(timezone);
  return (
    <span className="whitespace-nowrap">
      {dayjs.utc(createdAt).tz(dayjs.tz.guess()).fromNow()}
    </span>
  );
};

export const Status: React.FC<{ order: Order }> = ({ order }) => {
  return (
    <>
      <span
        className="bullet"
        style={{ backgroundColor: order.status.color }}
      />
      {order.status.name}
    </>
  );
};

const OrderLink: React.FC<{ order: Order; className?: string }> = ({
  order,
  className,
  children,
}) => {
  return (
    <Link href={generateOrderLinkHref(order.id)} className={className}>
      {children}
    </Link>
  );
};

const OrderCard = ({ order }: { order: Order }) => {
  const router = useRouter();
  const { setDrawerView, openDrawer } = useUI();
  const { width } = useWindowSize();
  const { mutate: buyAgain, isLoading } = useOrderBuyAgainMutation();
  const product =
    order.products.find((product) => !!product.image) ?? order.products[0]; // pick product with image, pick the first one if all empty images

  function handleCardClicked() {
    if (width <= 768) {
      router.push(generateOrderLinkHref(order.id));
    }
  }

  function handleBuyAgain(e: any) {
    e.stopPropagation();
    e.preventDefault();
    buyAgain(
      { order_id: order.id },
      {
        onSuccess: (data: any) => {
          if (data && data.success) {
            toast.info(data.message);
            setDrawerView('CART_SIDEBAR');
            openDrawer();
          }
        },
      }
    );
  }

  return (
    <OrderLink order={order}>
      <div className="w-full max-w-md mx-auto bg-white  p-4">
        <div className="flex flex-row">
          <div className="flex flex-col ">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-gray-500 text-xs col-span-1">Order ID</div>
              <div className="font-medium text-sm text-gray-900 col-span-2">
                {order.display_id}
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-gray-500 text-xs col-span-1">Order Date</div>
              <div className="font-medium text-sm text-gray-900 col-span-2">
                {moment(order.created_at).format('DD MMM YYYY hh:mm A')}
              </div>
            </div>
          </div>
          <div className="ml-auto w-32 text-right text-xs">
            <Status order={order} />
          </div>
        </div>

        {/* Product */}

        {order.products.map((product) => (
          <div className="flex mt-4 items-center" key={product.id}>
            <img
              src={product.image ?? productPlaceholder.src}
              alt={product.name}
              width={80}
              height={80}
              className="border overflow-hidden"
            />
            <div className="ml-4 flex-1">
              <div className="font-medium text-gray-900">{product.name}</div>

              <div className="flex items-center">
                <div className="flex flex-col">
                  <div className="text-sm text-skin-primary mt-1 font-bold">
                    <PriceRenderer price={product.subtotal} />
                  </div>
                  <div className="text-sm text-gray-500 text-[10px]">
                    <RedeemUpTo points={product.max_redeemable_points} />
                  </div>
                </div>

                <div className="text-sm text-gray-500 ml-auto">
                  Qty: {product.order_quantity}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Pricing */}
        <div className="mt-4 pt-4">
          <div className="flex justify-end text-xs">
            ({formatNumber(order.points_used)} point{parseInt(order.points_used) > 1 ? "s" : ""})
          </div>
          <div className="flex justify-end items-center text-sm text-gray-500">
            <span className="text-xs text-skin-muteds text-[11px] mr-2">
              Total ({order.products.length} item):
            </span>
            <span className="text-skin-primary font-bold">
              <PriceRenderer price={order.total} />
            </span>
          </div>
          <div className="flex justify-end mt-2">
            <OrderPayNowButton order={order} />
          </div>
        </div>
      </div>
    </OrderLink>
    // <div
    //   className="bg-skin-fill rounded-md shadow-card border border-[#E3E8EC] hover:bg-skin-secondary px-5 py-3"
    //   key={order.id}
    //   onClick={handleCardClicked}
    // >
    //   <div className="flex justify-between items-center gap-2 text-xs md:text-sm py-2">
    //     <OrderLink
    //       className="text-skin-primary text-sm md:text-base"
    //       order={order}
    //     >
    //       {order.display_id}
    //     </OrderLink>
    //     <div className="text-end">
    //       <Status order={order} />
    //     </div>
    //   </div>
    //   {product ? (
    //     <OrderLink
    //       className="w-full flex justify-between border-t py-4 gap-3"
    //       order={order}
    //     >
    //       <div className="flex-shrink-0 relative">
    //         <img
    //           src={product.image ?? productPlaceholder.src}
    //           alt={product.name}
    //           width={80}
    //           height={80}
    //           className="border overflow-hidden"
    //         />
    //         {product.banner ? (
    //           <img
    //             src={product.banner}
    //             alt={`${product.name} Bottom Banner`}
    //             width={80}
    //             height={80}
    //             className="absolute w-full h-full start-0 bottom-0"
    //           />
    //         ) : (
    //           <></>
    //         )}
    //       </div>
    //       <span className="flex-auto flex flex-col overflow-hidden">
    //         <span className="text-start text-xs md:text-sm truncate">
    //           {product.name}
    //         </span>
    //         <span className="text-end md:text-start text-xs">
    //           x{product.order_quantity}
    //         </span>
    //         <span className="text-end text-xs">
    //           <PriceRenderer price={product.subtotal} />
    //         </span>
    //       </span>
    //     </OrderLink>
    //   ) : (
    //     <></>
    //   )}
    //   <div className="text-sm md:text-base text-skin-primary border-t text-end py-3">
    //     Order Total: <PriceRenderer price={order.total} />
    //   </div>
    //   <div className="flex justify-between items-center text-xs md:text-sm pt-3">
    //     <CreatedAt createdAt={order.created_at} />
    //     <Button onClick={handleBuyAgain} loading={isLoading}>
    //       Buy Again
    //     </Button>
    //   </div>
    // </div>
  );
};

export default OrderCard;
