import { Order } from '@framework/types';
import { siteSettings } from '@settings/site-settings';
import { formatAddress } from '@utils/format-address';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import PriceRenderer from '@components/common/price-renderer';
import { parseToFloat } from '@framework/product/use-price';

export default function OrderPdfPreview({ order }: { order: Order }) {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  return (
    <div className="w-full md:w-[700px] lg:w-[800px] p-4 md:p-8 mx-auto leading-normal">
      <div className="flex-1 font-lato">
        {/* Logo */}
        <div className="flex justify-end w-full mb-4">
          <div className="flex-none">
            <img
              src={`${window.location.origin}/assets/images/logo.png`}
              width={100}
            />
          </div>
        </div>

        {/* Order Top */}
        <div className="flex justify-between w-full mb-4">
          <div>
            <span className="text-[10px] md:text-[11px] text-skin-base font-semibold">
              Order Number:{' '}
              <span className="font-lato-bold text-skin-muted">
                {order.display_id}
              </span>
            </span>
          </div>
          <div>
            <span className="text-[10px] md:text-[11px] text-skin-base font-semibold">
              Order Date:{' '}
              <span className="font-lato-bold text-skin-muted">
                {dayjs(order.created_at)
                  .tz(dayjs.tz.guess())
                  .format('D MMMM, YYYY')}
              </span>
            </span>
          </div>
        </div>

        {/* Address */}
        <div className="flex justify-between gap-4 w-full  mb-4">
          <div className="flex flex-col flex-1">
            <span className="text-[11px] md:text-[12px] text-skin-base font-lato-bold font-semibold">
              {order?.shipping_address?.recipient}
            </span>
            <span className="text-[10px] md:text-[11px] text-skin-muted">
              {order?.customer_email}
            </span>
            <span className="text-[10px] md:text-[11px] text-skin-muted">
              {order.shipping_address.contact_number
                ? order.shipping_address.contact_number
                : order.customer_contact}
            </span>
            <span className="text-[10px] md:text-[11px] text-skin-muted">
              {formatAddress(order?.shipping_address)}
            </span>
          </div>
          <div className="flex flex-col text-end flex-1">
            <span className="text-[11px] md:text-[12px] text-skin-base font-lato-bold font-semibold">
              {siteSettings.author.name}
            </span>
            <span className="text-[10px] md:text-[11px] text-skin-muted">
              {siteSettings.author.supportEmail}
            </span>
            <span className="text-[10px] md:text-[11px] text-skin-muted">
              {siteSettings.author.address}
            </span>
          </div>
        </div>

        {/* Table */}
        <div className="w-full flex flex-col">
          {order.products.map(({ name, order_quantity, subtotal }, index) => {
            return (
              <div className="w-full flex flex-col" key={index}>
                <div className="w-full flex">
                  <span className="w-[50px] text-[10px] md:text-[11px] text-skin-base py-3 md:py-4 px-3 md:px-4 border-t border-skin-base text-center">
                    {index + 1}
                  </span>
                  <span className="flex-1 w-[50px] text-[10px] md:text-[11px] text-skin-base py-3 md:py-4 px-3 md:px-4 border-t border-skin-base">
                    {name}
                  </span>
                  <span className="w-[100px] text-[10px] md:text-[11px] text-skin-base py-3 md:py-4 px-3 md:px-4 border-t border-skin-base text-center">
                    x{order_quantity}
                  </span>
                  <span className="w-[100px] text-[10px] md:text-[11px] text-skin-base py-3 md:py-4 px-3 md:px-4 border-t border-skin-base text-end">
                    <PriceRenderer price={parseToFloat(subtotal)} />
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Border */}
        <hr className="w-full sm:w-1/2 ml-auto mb-2" />

        {/* Total */}
        <div className="w-full sm:w-1/2 ml-auto">
          <div style={styles.totalCountRow}>
            <span style={styles.totalCountCell}>Sub Total</span>
            <span style={styles.totalCountCell}>
              <PriceRenderer price={order.amount} />
            </span>
          </div>
          <div style={styles.totalCountRow}>
            <span style={styles.totalCountCell}>Tax</span>
            <span style={styles.totalCountCell}>
              <PriceRenderer price={order.sales_tax} />
            </span>
          </div>
          {order.points_discount && order.points_discount > 0 ? (
            <div style={styles.totalCountRow}>
              <span style={styles.totalCountCell}>Redemption Discount</span>
              <span style={styles.totalCountCell}>
                -<PriceRenderer price={order.points_discount!} />
              </span>
            </div>
          ) : (
            <></>
          )}
          {order.points_discount_refunded && order.points_discount_refunded > 0 ? (
            <div style={styles.totalCountRow}>
              <span style={styles.totalCountCell}>Refunded Redemption</span>
              <span style={styles.totalCountCell}>
                -<PriceRenderer price={order.points_discount_refunded!} />
              </span>
            </div>
          ) : (
            <></>
          )}
          {order.coupon_discount && order.coupon_discount > 0 && (
            <div style={styles.totalCountRow}>
              <span style={styles.totalCountCell}>Voucher Discount</span>
              <span style={styles.totalCountCell}>
                -<PriceRenderer price={order.coupon_discount!} />
              </span>
            </div>
          )}
          {order.discount && order.discount > 0 && (
            <div style={styles.totalCountRow}>
              <span style={styles.totalCountCell}>Discount</span>
              <span style={styles.totalCountCell}>
                -<PriceRenderer price={order.discount!} />
              </span>
            </div>
          )}
          <div style={styles.totalCountRow}>
            <span style={styles.totalCountCell}>Delivery Fee</span>
            <span style={styles.totalCountCell}>
              <PriceRenderer price={order.delivery_fee!} />
            </span>
          </div>
          {order.store_credit && order.store_credit > 0 && (
            <div style={styles.totalCountRow}>
              <span style={styles.totalCountCell}>Store Credit</span>
              <span style={styles.totalCountCell}>
                <PriceRenderer price={order.store_credit!} />
              </span>
            </div>
          )}
          <div style={styles.totalCountRow}>
            <span
              style={{
                ...styles.totalCountCell,
                ...{ fontSize: 12, fontFamily: 'Lato Bold' },
              }}
            >
              Total
            </span>
            <span
              style={{
                ...styles.totalCountCell,
                ...{ fontSize: 12, fontFamily: 'Lato Bold' },
              }}
            >
              <PriceRenderer price={order.total} />
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

const styles = {
  totalCountWrapper: {
    width: '50%',
    display: 'flex',
    flexDirection: 'column',
    marginLeft: 'auto',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    borderTopStyle: 'solid',
  },

  totalCountRow: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  totalCountCell: {
    fontSize: 11,
    color: '#6B7280',
    padding: '8pt 16pt 2pt',
  },
};
