import Button from '@components/ui/button';
import { useCheckout } from '@contexts/checkout.context';
import { useCheckoutPlaceOrder } from '@framework/checkout/use-checkout';
import { Order } from '@framework/types';
import { useRouter } from 'next/router';
import React from 'react';

const OrderPayNowButton = ({ order }: { order: Order }) => {
  const router = useRouter();
  const { setOrder } = useCheckout();

  function handlePayNow() {
    setOrder(order.id);
    router.push('/checkout/payment');
  }
  return ['pending'].find((status) => order.status.slug === status) ? (
    <Button
      variant="small"
      onClick={(e) => {
        e.preventDefault();
        handlePayNow();
      }}
    >
      Pay Now
    </Button>
  ) : (
    <></>
  );
};

export default OrderPayNowButton;
