import PageLoader from '@components/ui/loaders/page-loader';
import { useOrderStatusQuery } from '@framework/order/order-status';
import ProgressBox from './progress-box';

interface Props {
  status: number;
}

const OrderStatus = ({ status }: Props) => {
  const { data, isLoading } = useOrderStatusQuery();
  return !isLoading ? (
    <ProgressBox data={data} status={status} />
  ) : (
    <PageLoader />
  );
};

export default OrderStatus;
