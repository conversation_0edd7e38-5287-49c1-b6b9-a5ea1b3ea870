import Link from '@components/ui/link';
import Scrollbar from '@components/ui/scrollbar';
import { useRouter } from 'next/router';
import cn from 'classnames';
import Tabs from '@components/ui/tabs';

const ORDER_STATUS_TAB_OPTIONS = [
  {
    id: 1,
    label: 'All',
    value: '',
  },
  {
    id: 2,
    label: 'To Pay',
    value: 'pending',
  },
  {
    id: 3,
    label: 'Processing',
    value: 'processing',
  },
  {
    id: 4,
    label: 'To Ship',
    value: 'packed',
  },
  {
    id: 5,
    label: 'To Receive',
    value: 'delivery',
  },
  {
    id: 6,
    label: 'Completed',
    value: 'complete',
  },
  {
    id: 7,
    label: 'Cancelled',
    value: 'canceled',
  },
];

const OrderStatusTabs: React.FC = () => {
  const router = useRouter();

  return (
    <div className="w-full bg-white">
      <Scrollbar
        options={{
          paddingAbsolute: true,
          overflowBehavior: {
            y: 'visible-hidden',
          },
          scrollbars: {
            visibility: 'auto',
          },
        }}
      >
        <div className="flex items-end w-full mb-[0.3rem]">
          <Tabs
            tabHeadings={ORDER_STATUS_TAB_OPTIONS}
            selectedValue={router.query.view as string}
            onTabChange={(value) =>
              router.replace({
                pathname: router.pathname,
                query: { ...router.query, view: value },
              })
            }
          />
        </div>
      </Scrollbar>
    </div>
  );
};

export default OrderStatusTabs;
