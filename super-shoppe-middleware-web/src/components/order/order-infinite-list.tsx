import Input from '@components/ui/form/input';
import { useState } from 'react';
import { BsSearch } from 'react-icons/bs';
import { Order } from '@framework/types';
import OrderStatusTabs from './order-status-tabs';
import NoResult from '@components/common/no-result';
import OrderCard from './order-card';
import { UseInfiniteQueryResult } from 'react-query';
import { PaginatedOrder } from '@framework/order/get-all-orders';
import Alert from '@components/ui/alert';
import PageLoader from '@components/ui/loaders/page-loader';
import Button from '@components/ui/button';

const OrderInfiniteList: React.FC<{
  data: UseInfiniteQueryResult<PaginatedOrder, Error>;
  onSearchChange?: any;
}> = ({ data, onSearchChange }) => {
  const [value, setValue] = useState('');
  const {
    isFetching: isLoading,
    isFetchingNextPage: loadingMore,
    fetchNextPage,
    hasNextPage,
    data: ordersData,
    error,
  } = data;

  const onChangeSearch = (e: any) => {
    setValue(e.target.value);
  };
  const onSubmitHandle = (e: any) => {
    e.preventDefault();
    onSearchChange && onSearchChange(value);
  };

  function loadMoreItems() {
    if (hasNextPage) {
      fetchNextPage();
    }
  }

  // if (!ordersData) return <PageLoader />;

  return (
    <>
      <div className="">
        <OrderStatusTabs />
      </div>
      <form onSubmit={onSubmitHandle} className="relative p-1">
        <span className="absolute end-3 top-[80%] transform -translate-y-1/2 order-icon-color">
          <BsSearch size={19} />
        </span>
        <Input
          name="search"
          type="search"
          value={value}
          onChange={onChangeSearch}
          placeholder="Search Order list"
          inputClassName=" h-[46px] w-full placeholder-[rgba(0, 0, 0, .3)] bg-white border border-[#E3E8EC] rounded-md order-search focus:border-2 focus:outline-none focus:border-skin-primary"
        />
      </form>

      <div>
        {error && (
          <div className="col-span-full">
            <Alert message={error?.message} />
          </div>
        )}
        {isLoading ? (
          <div className="col-span-full py-6 px-4 lg:p-8">
            <PageLoader />
          </div>
        ) : ordersData?.pages.length <= 0 ||
          ordersData.pages[0].data?.length <= 0 ? (
          <div className="col-span-full py-6 px-4 lg:p-8">
            <NoResult />
          </div>
        ) : (
          <div className="flex flex-col gap-2 bg-skin-two">
            {ordersData?.pages?.map((page: any) => {
              return page?.data?.map((order: Order) => (
                <OrderCard key={`order--key-${order.id}`} order={order} />
              ));
            })}
            {!isLoading && !loadingMore && hasNextPage && (
              <div className="text-center">
                <Button onClick={loadMoreItems}>Load More</Button>
              </div>
            )}
            {loadingMore && (
              <div className="flex relative">
                <div className="page_loader"></div>
                <h3 className="text-sm font-semibold text-body italic absolute top-1/2 -mt-2 w-full text-center">
                  Loading
                </h3>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default OrderInfiniteList;
