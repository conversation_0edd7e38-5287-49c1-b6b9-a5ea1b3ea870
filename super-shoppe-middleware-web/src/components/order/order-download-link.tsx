import { PDFDownloadLink } from '@react-pdf/renderer';
import { Order } from '@framework/types';
import OrderPdf from './order-pdf';

const OrderDownloadLink = ({
  order,
  text = 'Print',
}: {
  order: Order;
  text?: string;
}) => {
  return (
    <PDFDownloadLink
      document={<OrderPdf order={order} />}
      fileName={`${order.display_id}.pdf`}
    >
      {({ loading }: any) => (loading ? 'loading...' : text)}
    </PDFDownloadLink>
  );
};

export default OrderDownloadLink;
