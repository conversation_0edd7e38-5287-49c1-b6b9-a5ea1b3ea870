import { useModalAction } from '@components/common/modal/modal.context';
import NoResult from '@components/common/no-result';
import Button from '@components/ui/button';
import Heading from '@components/ui/heading';
import PageLoader from '@components/ui/loaders/page-loader';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { useOrderQuery } from '@framework/order/get-order';
import { useTranslation } from 'next-export-i18n';
import { useRouter } from 'next/router';
import PriceRenderer from '@components/common/price-renderer';
import { OrderItem } from '@framework/types';
import dynamic from 'next/dynamic';

const OrderItemCard = dynamic(() => import('@components/order/order-item-card'));

const OrderCancelDetails: React.FC<{ className?: string }> = ({
  className = 'pt-10 lg:pt-12',
}) => {
  const { t } = useTranslation();
  const { openModal } = useModalAction();
  const {
    query: { order_id: id, track },
  } = useRouter();
  const {
    data: order,
    isLoading,
    error,
  } = useOrderQuery(id?.toString()!, !!track);

  function handleWithdrawRefund() {
    return openModal('WITHDRAW_CANCEL_CONFIRM', order);
  }

  if (error) return <NoResult />;
  if (isLoading || !order) return <PageLoader />;

  dayjs.extend(utc);
  dayjs.extend(timezone);

  return (
    <div className={className}>
      <Heading className="text-center mb-6">
        {t('text-cancellation-details')}
      </Heading>
      <ul className="border border-skin-base bg-skin-secondary rounded-md flex flex-col mt-6 xl:mt-7 md:flex-row mb-7 lg:mb-8 xl:mb-10">
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-skin-two px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-xs block text-skin-muted font-normal leading-5">
            {t('text-order-number')}:
          </span>
          {order?.display_id}
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-refund-status')}:
          </span>
          <span
            className={`capitalize ${
              order.refund.status === 'approved'
                ? 'text-skin-tree'
                : order.refund.status === 'rejected'
                ? 'text-skin-red'
                : 'text-skin-primary'
            }`}
          >
            {order.refund.status}
          </span>
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-refund-amount')}:
          </span>
          <PriceRenderer price={order.refund.amount} />
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-requested-at')}:
          </span>
          {dayjs
            .utc(order.refund.created_at)
            .tz(dayjs.tz.guess())
            .format('DD MMM YYYY HH:mm:ss')}
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('text-reason')}:
          </span>
          {order.refund.title}
        </li>
        <li className="text-skin-base font-semibold text-sm lg:text-base border-b md:border-b-0 md:border-r border-dashed border-gray-300 px-4 lg:px-6 xl:px-8 py-4 md:py-5 lg:py-6 last:border-0">
          <span className="uppercase text-[11px] block text-skin-muted font-normal leading-5">
            {t('forms.label-cancel-comment')}:
          </span>
          {order.refund.description ?? '-'}
        </li>
      </ul>

      <table className="w-full text-skin-base font-semibold text-sm lg:text-base">
        <thead>
          <tr>
            <th className="bg-skin-secondary p-4 text-start first:rounded-ts-md w-1/2">
              {t('text-product')}
            </th>
            <th className="bg-skin-secondary p-4 text-start last:rounded-te-md w-1/2">
              {t('text-total')}
            </th>
          </tr>
        </thead>
        <tbody>
          {order?.products.map((product: OrderItem, index: number) => (
            <OrderItemCard
              key={index}
              product={product}
            />
          ))}
        </tbody>
      </table>
      {order.refund.status == 'pending' && !track && (
        <div className="text-center mb-4">
          <Button onClick={handleWithdrawRefund}>
            {t('button-withdraw-cancellation-request')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default OrderCancelDetails;
