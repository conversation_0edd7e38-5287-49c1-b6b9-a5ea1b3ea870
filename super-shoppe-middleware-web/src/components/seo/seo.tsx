import { NextSeo, NextSeoProps } from 'next-seo';
import { OpenGraphMedia } from 'next-seo/lib/types';

interface SeoProps extends NextSeoProps {
  path: string;
  images?: OpenGraphMedia[];
}

const Seo = ({ title, description, path }: SeoProps) => {
  return (
    <NextSeo
      title={title}
      description={description}
      openGraph={{
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/${path}`,
        title,
        description,
        images: [
          // {
          //   url: '/assets/images/og-image-01.png',
          //   alt: 'Og Image Alt',
          // },
          // {
          //   url: '/assets/images/og-image-02.png',
          //   width: 900,
          //   height: 800,
          //   alt: 'Og Image Alt Second',
          // },
        ],
      }}
    />
  );
};

export default Seo;
