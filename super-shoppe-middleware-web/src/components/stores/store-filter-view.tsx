import React from 'react';
import { useEffect } from 'react';
import {
  useModalAction,
  useModalState,
} from '@components/common/modal/modal.context';
import Button from '@components/ui/button';
import { useRouter } from 'next/router';
import { useCart } from '@contexts/cart/cart.context';
import Input from '@components/ui/form/input';

import { FormProvider, useForm } from 'react-hook-form';
import SelectState from '@components/common/form/select-state';

export type FormValues = {
  name: string;
  state: {
    id: string;
    name: string;
  } | null;
};
const defaultValues = {
  name: '',
  state: null,
};

const StoreFilterView = () => {
  const { closeModal } = useModalAction();
  const { data, onConfirm } = useModalState();
  const methods = useForm<FormValues>({
    defaultValues: {
      ...data,
    },
  });
  const { handleSubmit, register } = methods;
  function submitFilter(data: FormValues) {
    onConfirm(data);
    closeModal();
  }

  return (
    <div className="md:w-[600px]  mx-auto py-6 px-5 sm:p-8 bg-skin-fill rounded-md">
      <h2 className="text-lg font-semibold text-heading mb-6">Filter Stores</h2>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(submitFilter)} noValidate>
          <div className="flex flex-col text-sm sm:text-base text-body">
            <Input
              label="Search by Location"
              {...register('name')}
              className="mb-4"
              placeholder="eg. Jalan bintang"
              variant="outline"
            />
            <div className="mb-4">
              <SelectState keyName="state" error="" />
            </div>

            <div className="mt-8 flex flex-row gap-4">
              <Button
                type="button"
                variant="border"
                onClick={() => {
                  methods.reset(defaultValues);
                  onConfirm(defaultValues);
                  closeModal();
                }}
              >
                Reset
              </Button>
              <Button type="submit" className="w-full h-11 sm:h-12">
                Apply Filter
              </Button>
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default StoreFilterView;
