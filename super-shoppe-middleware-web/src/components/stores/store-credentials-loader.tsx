import ErrorInformation from "@components/404/error-information";
import PageLoader from "@components/ui/loaders/page-loader";
import { useAdditionalSettingsQuery } from "@framework/settings/use-api-settings.query";
import { API_SETTINGS_ENUM } from "@framework/types";
import { useTranslation } from "next-i18next";

interface StoreInitCredentialsLoaderProps {
}

const StoreInitCredentialsLoader: React.FC<StoreInitCredentialsLoaderProps> = ({ children }) => {
  const { t } = useTranslation();
  const { data: additionalSettingsData, isLoading, error } = useAdditionalSettingsQuery({
    key: API_SETTINGS_ENUM.GOOGLE
  });
  if (isLoading || !additionalSettingsData) return <PageLoader />;
  if (error) return <ErrorInformation />;
  return children({ google_api_settings: additionalSettingsData.data });
}

export default StoreInitCredentialsLoader;