import { ControlPosition, MapControl } from '@vis.gl/react-google-maps';
import { IoMdLocate } from "react-icons/io";
import React from 'react';
import { LatLng } from '@framework/types';

type LocateToUserLocationProps = {
  map: google.maps.Map;
  userPosition: LatLng;
  onSetCenter: (center: LatLng) => void;
};

export const LocateToUserLocation = ({
  map,
  userPosition,
  onSetCenter
}: LocateToUserLocationProps) => {
  function handleLocateClicked() {
    map.setCenter(userPosition);
    onSetCenter(userPosition);
  }
  return (
    <MapControl position={ControlPosition.RIGHT_BOTTOM}>
      <div className="m-2">
        <div className="rounded-full drop-shadow bg-white cursor-pointer p-3" onClick={handleLocateClicked}>
          <IoMdLocate className="text-lg" />
        </div>
      </div>
    </MapControl>
  );
};