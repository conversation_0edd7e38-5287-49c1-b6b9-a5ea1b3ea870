import React from 'react';
import FilterIcon from '@components/icons/filter-icon';
import Label from '@components/ui/label';
import { useModalAction } from '@components/common/modal/modal.context';
import { FormValues as StoreSearchValues } from '@components/stores/store-filter-view';

const StoreFilterHead = ({ filters, onFilterChange }: { filters: StoreSearchValues | undefined; onFilterChange: any }) => {
  const { openModal } = useModalAction();
  const openFilterModal = () => {
    openModal('STORE_FILTER_VIEW', filters, true, {
      confirmCallback: (data: any) => {
        onFilterChange(data);
      },
    });
  };
  return (
    <div className="flex items-center gap-4 p-2 m-2 bg-white rounded-lg">
      <Label className="ml-2 !mb-0 text-lg">Store List</Label>
      <button
        className="bg-white hover:text-white border border-gray-200 hover:bg-skin-primary focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 ml-auto relative"
        onClick={openFilterModal}
      >
        <FilterIcon />
        {Object.keys(filters ?? {}).filter((key) => !!filters[key]).length ? (
          <div className="absolute inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-skin-primary border-2 border-white rounded-full -top-2 -end-2">
            {Object.keys(filters!).filter((key) => !!filters[key]).length}
          </div>
        ) : (
          <></>
        )}
      </button>
    </div>
  );
};

export default StoreFilterHead;
