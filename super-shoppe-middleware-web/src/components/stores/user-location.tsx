import React, { useEffect, useState } from 'react';
import { AdvancedMarker, useMap } from '@vis.gl/react-google-maps';
import { LatLng } from '@framework/types';
import { Capacitor } from '@capacitor/core';
import { Geolocation } from '@capacitor/geolocation';
import { LocateToUserLocation } from './locate-to-user-location';
import { DEFAULT_POSITION } from './locations-map';

type UserLocationProps = {
  setCurrentPosition: (location: LatLng) => void;
};

export const UserLocation = ({
  setCurrentPosition
}: UserLocationProps) => {
  const [userPosition, setUserPosition] = useState<LatLng | null>(null);
  const map = useMap();
  useEffect(() => {
    const getCurrentPosition = async () => {
      try {
        if (Capacitor.isNativePlatform()) {
          // Request permissions if not granted
          const permission = await Geolocation.requestPermissions();
          if (permission.location === 'granted') {
            // Use Capacitor Geolocation plugin for native platforms
            const coordinates = await Geolocation.getCurrentPosition();
            map!.setCenter({ lat: coordinates.coords.latitude, lng: coordinates.coords.longitude });
            setUserPosition({
              lat: coordinates.coords.latitude,
              lng: coordinates.coords.longitude,
            });
            setCurrentPosition({
              lat: coordinates.coords.latitude,
              lng: coordinates.coords.longitude,
            });
          } else {
            setCurrentPosition(DEFAULT_POSITION);
            console.error('Location permission not granted.');
          }
        } else if (navigator.geolocation) {
          // Use browser's geolocation API for web
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude } = position.coords;
              map!.setCenter({ lat: latitude, lng: longitude });
              setUserPosition({ lat: latitude, lng: longitude });
              setCurrentPosition({ lat: latitude, lng: longitude });
            },
            (error) => {
              setCurrentPosition(DEFAULT_POSITION);
              console.error('Error getting current location:', error);
            }
          );
        } else {
          setCurrentPosition(DEFAULT_POSITION);
          console.error('Geolocation is not supported by this browser.');
        }
      } catch (error) {
        setCurrentPosition(DEFAULT_POSITION);
        console.error('Error getting current location:', error);
      }
    };

    getCurrentPosition();
  }, []);
  return (
    <>
      {userPosition && (
        <>
          <AdvancedMarker
            position={userPosition}
            title="Your current location"
            style={{zIndex: 10}}
          >
            <div className="relative w-8 h-8">
              <div className="absolute animate-ping duration-[2s] w-full h-full rounded-full bg-blue-200 z-0" />
              <div className="absolute top-2 left-2 w-4 h-4 rounded-full border border-white bg-blue-500 z-1" />
            </div>
          </AdvancedMarker>
          <LocateToUserLocation map={map!} userPosition={userPosition} onSetCenter={setCurrentPosition} />
        </>
      )}
    </>
  );
};