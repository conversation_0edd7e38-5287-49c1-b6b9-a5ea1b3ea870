import React, { useCallback, useState } from 'react';
import {
  InfoWindow,
  <PERSON><PERSON>,
  useMarkerRef,
} from '@vis.gl/react-google-maps';
import { Store } from '@framework/types';

type StoreMarkerProps = {
  loc: Store;
  selectedLocation: Store | null;
  setSelectedLocation: (loc: Store | null) => void;
};

const StoreMarker = ({
  loc,
  selectedLocation,
  setSelectedLocation,
}: StoreMarkerProps) => {
  const [markerRef, marker] = useMarkerRef();
  const [infoWindowShown, setInfoWindowShown] = useState(false);
  // clicking the marker will toggle the infowindow
  const handleMarkerClick = useCallback(() => setSelectedLocation(loc), []);

  // if the maps api closes the infowindow, we have to synchronize our state
  const handleClose = useCallback(() => setSelectedLocation(null), []);
  return (
    <>
      <Marker
        ref={markerRef}
        position={{
          lat: loc.latitude,
          lng: loc.longitude
        }}
        title={loc.name}
        onClick={handleMarkerClick}
      />
      {selectedLocation?.id == loc?.id && (
        <InfoWindow
          onCloseClick={handleClose}
          anchor={marker}
          style={{
            display: 'flex',
            flexDirection: 'row',
          }}
          shouldFocus
          headerContent={
            <h1
              style={{
                fontWeight: 'bold',
                marginBottom: '0.5rem',
              }}
            >
              {loc.name}
            </h1>
          }
        >
          <div>
            <p
              style={{
                marginBottom: '0.5rem',
              }}
            >
              {loc.address}
            </p>
            {/* Phone */}
            {loc.phone && (
              <div
                style={{
                  marginBottom: '0.5rem',
                }}
              >
                <span>Phone: </span>

                <a style={{ fontWeight: 'bold' }} href={`tel:${loc.phone}`}>
                  {loc.phone}
                </a>
              </div>
            )}
            {/* Get Directions */}
            <a
              href={loc.place_id ? `https://www.google.com/maps/search/?api=1&query=${loc.address}&query_place_id=${loc.place_id}` : `https://www.google.com/maps/dir/?api=1&destination=${loc.latitude},${loc.longitude}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: 'blue',
                textDecoration: 'underline',
              }}
            >
              Get Directions
            </a>
          </div>
        </InfoWindow>
      )}
    </>
  );
};

export default StoreMarker;
