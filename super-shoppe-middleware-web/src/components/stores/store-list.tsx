import React, { useEffect, useRef } from 'react';
import { PaginationType, Store } from '@framework/types';
import { InfiniteData } from 'react-query';
import { Waypoint } from 'react-waypoint';
import StoreListLoader from '@components/ui/loaders/store-list-loader';
import { PaginatedStore } from '@framework/gql/store/get-all-stores';
import { FormValues as StoreSearchValues } from '@components/stores/store-filter-view';

const StoreList = ({
  stores,
  selectedLocation,
  setSelectedLocation,
  isLoading,
  filters,
  loadMoreItems,
  isLoadMoreLoading,
}: {
  stores?: InfiniteData<PaginatedStore>;
  selectedLocation: Store | null;
  setSelectedLocation: (store: Store | null) => void;
  isLoading: boolean;
  filters?: StoreSearchValues;
  loadMoreItems: () => void;
  isLoadMoreLoading: boolean;
}) => {
  const scrollableDivRef = useRef(null);
  useEffect(() => {
    if (filters && !isLoading && stores?.pages?.at(0)?.data.stores.length) {
      setSelectedLocation(stores.pages.at(0)!.data.stores[0]);
    }
  }, [filters, isLoading, stores]);

  function handleSelectLocation(location: Store) {
    setSelectedLocation(selectedLocation?.id == location.id ? null : location);
  }
  return (
    <div className="overflow-auto h-full mx-2" ref={scrollableDivRef}>
      {!stores?.pages?.at(0)?.data.stores.length ? (
        <div className="flex items-center justify-center ">
          <p className="text-sm text-gray-500">No stores found</p>
        </div>
      ) : (
        <>
          {stores?.pages.map((page) =>
            (page.data.stores).map((store) => (
              <div
                key={store.id}
                className={`bg-white overflow-hidden rounded-lg shadow-card mb-2 hover:shadow-cardHover ${
                  selectedLocation?.id === store.id
                    ? 'border border-skin-primary'
                    : ''
                }`}
                onClick={() => handleSelectLocation(store)}
                style={{ cursor: 'pointer' }}
              >
                <div className="px-3 py-4 sm:p-4">
                  <h3
                    className={`font-normal text-base text-skin-primary ${
                      selectedLocation?.id === store.id ? 'text-skin-primary' : ''
                    }`}
                  >
                    {store.name}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">{store.address}</p>
                </div>
              </div>
            ))
          )}
        </>
      )}

      {!isLoadMoreLoading ? (
        <Waypoint
          onEnter={loadMoreItems}
          scrollableAncestor={scrollableDivRef.current}
          bottomOffset="-50px"
        />
      ) : (
        <div>
          {Array.from({ length: 15 }).map((_, idx) => (
            <StoreListLoader
              key={`product--key-${idx}`}
              uniqueKey={`product--key-${idx}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default StoreList;
