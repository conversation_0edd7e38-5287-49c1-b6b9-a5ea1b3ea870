import React from 'react';
import { Map } from '@vis.gl/react-google-maps';
import { LatLng, Store } from '@framework/types';
import StoreMarker from '@components/stores/store-marker';
import { InfiniteData } from 'react-query';
import { PaginatedStore } from '@framework/gql/store/get-all-stores';
import { UserLocation } from './user-location';
import { ZoomControl } from './zoom-control';
import { FocusLocation } from './focus-location';

export const DEFAULT_POSITION = { lat: 3.139, lng: 101.6869 };

const LocationsMap = ({
  mapId,
  stores,
  selectedLocation,
  setSelectedLocation,
  setCurrentPosition,
  defaultZoom,
  setZoom,
}: {
  mapId: string;
  stores: InfiniteData<PaginatedStore> | undefined;
  selectedLocation: Store | null;
  setSelectedLocation: (store: Store | null) => void;
  setCurrentPosition: (position: LatLng) => void,
  defaultZoom: number;
  setZoom: (zoom: number) => void,
}) => {
  return (
    <>
      <Map
        mapId={mapId}
        className="g-map w-screen lg:h-full h-[500px]"
        styles={
          [
            {
              featureType: 'poi',
              elementType: 'labels',
              stylers: [{ visibility: 'off' }],
            },
          ] as any
        }
        defaultZoom={defaultZoom}
        gestureHandling={'greedy'}
        defaultCenter={DEFAULT_POSITION} // Center map around Kuala Lumpur
      >
        {stores?.pages.map((page) =>
          page.data.stores.map((loc, i) => (
            <StoreMarker
              key={i}
              loc={loc}
              selectedLocation={selectedLocation}
              setSelectedLocation={setSelectedLocation}
            />
          ))
        )}
        <ZoomControl setCurrentZoom={setZoom} />
        <UserLocation setCurrentPosition={setCurrentPosition} />
        <FocusLocation selectedLocation={selectedLocation} />
      </Map>
    </>
  );
};

export default LocationsMap;
