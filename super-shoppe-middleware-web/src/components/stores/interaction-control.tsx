import { LatLng } from "@framework/types";
import { useMap } from "@vis.gl/react-google-maps";

type InteractionControlProps = {
  setCurrentPosition: (location: LatLng) => void;
};

export const InteractionControl = ({
  setCurrentPosition
}: InteractionControlProps) => {
  const map = useMap();

  // useEffect(() => {

  //   return () => {
  //     google.maps.event.clearListeners(map!, "dragend");
  //   }
  // });

  map?.addListener("dragend", function () {
    const current = map.getCenter();
    console.log("dragend");
    if (current) {
      setCurrentPosition({
        lat: current.lat(),
        lng: current.lng(),
      });
    }
  });

  return <></>;
};