import React, { useEffect } from 'react';
import { useMap } from '@vis.gl/react-google-maps';
import { Store } from '@framework/types';

type FocusLocationProps = {
  selectedLocation: Store | null;
};

export const FocusLocation = ({
  selectedLocation
}: FocusLocationProps) => {
  const map = useMap();
  useEffect(() => {
    if (selectedLocation) {
      map!.setCenter({ lat: selectedLocation.latitude, lng: selectedLocation.longitude });
    }
  }, [selectedLocation]);
  return (
    <></>
  );
};