import { useEffect } from 'react';
import { useBannersQuery } from '@framework/banner/get-banners';
import { useModalAction } from '@components/common/modal/modal.context';
import useWindowSize from '@utils/use-window-size';
import { Capacitor } from '@capacitor/core';

const PopupBannerContainer: React.FC<any> = () => {
  const platform = Capacitor.getPlatform();
  const { width } = useWindowSize();
  const { openModal } = useModalAction();
  const { data: popupBannerData } = useBannersQuery({
    banner_type: 'popup',
  });

  useEffect(() => {
    if (popupBannerData) {
      const popupBanners = popupBannerData
        .flatMap(
          (banner) => {
            if(banner.image.web) {
              if(banner.image.android || banner.image.ios) {
                //@ts-ignore
                if(banner.image[platform]) {
                  //@ts-ignore
                  const bannerData = banner.image[platform];
                  if((bannerData.desktop.length > 0 || bannerData.mobile.length > 0)) {
                    return bannerData[
                      width! < 768 && bannerData.is_same_desktop === false
                        ? 'mobile'
                        : 'desktop'
                    ];
                  }
                }
              }
              return banner.image.web[
                width! < 768 && banner.image.web.is_same_desktop === false
                  ? 'mobile'
                  : 'desktop'
              ];
            }
            return banner.image[
              width! < 768 && banner.image.is_same_desktop === false
                ? 'mobile'
                : 'desktop'
            ];
          }
        )
        .filter((banner) => !!banner);
      if (popupBanners.length > 0 && !!popupBanners[0].url) {
        openModal('POPUP_BANNER_VIEW', popupBanners[0]);
      }
    }
  }, [popupBannerData]);

  return <></>;
};

export default PopupBannerContainer;
