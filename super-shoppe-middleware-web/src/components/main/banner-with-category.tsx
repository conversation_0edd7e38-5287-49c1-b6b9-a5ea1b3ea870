import { useNavCategoriesQuery } from '@framework/category/get-all-categories';
import { TopProductsCategory } from '@framework/static/reserved-categories';
import BannerContainer from './banner-container';
import CategoryList from './category-list';
import BrandList from '@components/brand/brand-list';

interface Props {
  className?: string;
}

const BannerWithCategory: React.FC<Props> = ({
  className = 'mb-6 lg:mb-8 xl:mb-10 2xl:mb-12',
}) => {
  const { data } = useNavCategoriesQuery({
    orderType: 'brand_order',
  });
  const categoryData = {
    categories: {
      data: [
        // { ...TopProductsCategory },
        // { ...HotDealsCategory },
        ...(data ? data.categories.data : []),
      ],
    },
  };

  return (
    <div
      className={`xl:flex bg-transparent xl:border-t xl:border-b xl:bg-white xl:py-2.5 ${className}`}
    >
      <CategoryList type="standard" data={categoryData} className="bg-white" />
      <BannerContainer className="" />
      {/* <CategoryList
        type="horizontal"
        hideTitle
        data={categoryData}
        className="bg-white"
      /> */}
      <BrandList brandData={data?.categories.data} />
    </div>
  );
};

export default BannerWithCategory;
