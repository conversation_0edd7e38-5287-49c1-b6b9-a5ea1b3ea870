import Scrollbar from '@components/ui/scrollbar';
import { Category } from '@framework/types';
import { generateCategoryLinkHref } from '@framework/utils/data-mappers';
import { ROUTES } from '@utils/routes';
import useWindowSize from '@utils/use-window-size';
import { useTranslation } from 'next-export-i18n';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { SwiperSlide } from 'swiper/react';
const CategoryListCard = dynamic(
  () => import('@components/cards/category-list-card'),
  { ssr: false }
);
const Carousel = dynamic(() => import('@components/ui/carousel/carousel'), {
  ssr: false,
});

const mobileCategorySliderBreakpoints = {
  300: {
    slidesPerView: 4,
    slidesPerColumn: 2,
    slidesPerGroup: 4,
    spaceBetween: 12,
  },
  500: {
    slidesPerView: 5,
    slidesPerColumn: 2,
    slidesPerGroup: 5,
    spaceBetween: 12,
  },
  768: {
    slidesPerView: 6,
    slidesPerColumn: 2,
    slidesPerGroup: 6,
    spaceBetween: 24,
  },
  1024: {
    slidesPerView: 8,
    slidesPerColumn: 2,
    slidesPerGroup: 8,
    spaceBetween: 24,
  },
};

interface CategoryListProps {
  type: 'standard' | 'horizontal';
  data: { categories: { data: any[] } };
  hideTitle?: boolean;
  className?: string;
  breakpoint?: string;
}

const CategoryList: React.FC<CategoryListProps> = ({
  type,
  data,
  hideTitle = false,
  className = 'mt-4 xl:mt-0',
  breakpoint = 'xl',
}) => {
  const { t } = useTranslation();
  const { query } = useRouter();
  const { width } = useWindowSize();

  return (
    <>
      {data.categories.data.length > 0 && (
        <>
          {type === 'standard' && (
            <div
              className={`hidden rounded-md border border-skin-base ${breakpoint}:block me-8 ${breakpoint}:w-[300px] pt-[1px]`}
              style={{
                maxHeight: `${300 + ((width ?? 1280) - 1280) * 0.25}px`,
              }}
            >
              <Scrollbar
                className="flex flex-col"
                style={{
                  height: `${300 + ((width ?? 1280) - 1280) * 0.25 - 4}px`,
                }}
              >
                {data?.categories?.data?.map((category) => (
                  <CategoryListCard
                    key={`category--key-${category.id}`}
                    href={
                      category.products_page
                        ? {
                            pathname: ROUTES.PRODUCTS,
                            query: category.query,
                          }
                        : generateCategoryLinkHref(category, query)
                    }
                    category={category}
                    className="border-b border-skin-base last:border-b-0 transition"
                    variant="small"
                  />
                ))}
              </Scrollbar>
            </div>
          )}
          {type === 'horizontal' && (
            <div
              className={`block ${breakpoint}:hidden flex-shrink-0 ${className}`}
            >
              <div className="flex flex-col h-full justify-between">
                {!hideTitle && (
                  <div className="border-b border-base-300 px-2 pb-2">
                    <span className="text-15px capitalize font-semibold text-skin-primary">
                      {t('text-categories')}
                    </span>
                  </div>
                )}
                <Carousel
                  breakpoints={mobileCategorySliderBreakpoints}
                  buttonSize="small"
                  slidesPerColumnFill="row"
                  prevActivateId="category-carousel-button-prev"
                  nextActivateId="category-carousel-button-next"
                  prevButtonClassName="-start-2"
                  nextButtonClassName="-end-2"
                  className="mx-2"
                >
                  {data?.categories?.data?.map((category) => (
                    <SwiperSlide key={`category--key-${category.id}`}>
                      <CategoryListCard
                        key={`category--key-${category.id}`}
                        category={category}
                        href={
                          category.products_page
                            ? {
                                pathname: ROUTES.PRODUCTS,
                                query: category.query,
                              }
                            : generateCategoryLinkHref(category, query)
                        }
                        iconWidth={100}
                        iconHeight={100}
                        className="border-[1px] border-skin-base last:border-b-0 transition"
                        variant="box"
                        showTitle={false}
                        showArrow={false}
                      />
                    </SwiperSlide>
                  ))}
                </Carousel>
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default CategoryList;
