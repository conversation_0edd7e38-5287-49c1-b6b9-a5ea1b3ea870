import { Capacitor } from '@capacitor/core';
import { useBannersQuery } from '@framework/banner/get-banners';
import useWindowSize from '@utils/use-window-size';
import cn from 'classnames';
import dynamic from 'next/dynamic';

const CategoryListCardLoader = dynamic(
  () => import('@components/ui/loaders/category-list-card-loader'),
  {
    ssr: false,
  }
);

const BannerCarousel = dynamic(
  () => import('@components/banner/banner-carousel'),
  {
    ssr: false,
  }
);

const SideBanner = dynamic(
  () => import('@components/banner/side-banner'),
  {
    ssr: false,
  }
);

interface BannerContainerProps {
  className?: string;
}

const BannerContainer: React.FC<BannerContainerProps> = ({
  className = '',
}) => {
  const platform = Capacitor.getPlatform();
  const { width } = useWindowSize();
  const { data: mainBannerData, isLoading: mainBannerLoading } =
    useBannersQuery({});
  const { data: sideBannerData, isLoading: sideBannerLoading } =
    useBannersQuery({
      banner_type: 'side',
    });
  if (
    mainBannerLoading ||
    sideBannerLoading ||
    !mainBannerData ||
    !sideBannerData
  ) {
    return <CategoryListCardLoader />;
  }
  const mainBanners = mainBannerData
    .flatMap((banner) => {
      if (banner.banner_type == 'main') {
        if(banner.image.web) {
          return banner.image.web['desktop'];
        }
        return banner.image['desktop'];
      }
    })
    .filter((banner) => !!banner);
  const sideBanners = sideBannerData
    .flatMap((banner) => {
      if (banner.banner_type == 'side') {
        if(banner.image.web) {
          if(banner.image.android || banner.image.ios) {
            //@ts-ignore
            if(banner.image[platform] && banner.image[platform].length > 0) {
              //@ts-ignore
              const bannerData = banner.image[platform];
              return bannerData[
                width! < 768 && bannerData.is_same_desktop === false
                  ? 'mobile'
                  : 'desktop'
              ];
            }
          }
          return banner.image.web[
            width! < 768 && banner.image.web.is_same_desktop === false
              ? 'mobile'
              : 'desktop'
          ];
        }
        return banner.image[
          width! < 768 && banner.image.is_same_desktop === false
            ? 'mobile'
            : 'desktop'
        ];
      }
    })
    .filter((banner) => !!banner);
  return (
    <div
      className={cn(
        'trendy-main-content w-full flex flex-col md:flex-row gap-2 bg-white',
        className
      )}
    >
      <div className="w-full md:w-2/3">
        <BannerCarousel
          mainBanners={mainBanners}
          sideBanners={sideBanners}
          buttonSize="small"
          className="w-full h-full"
        />
      </div>
      <div className="hidden md:flex md:flex-col md:w-1/3">
        <SideBanner banners={sideBanners} />
      </div>
    </div>
  );
};

export default BannerContainer;
