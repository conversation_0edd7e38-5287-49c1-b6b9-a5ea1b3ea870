import Heading from '@components/ui/heading';
import ChevronRightIcon from '@components/icons/chevron-right';
import WalletTransactionsInfiniteList from '@components/wallet/wallet-transaction-infinite-list';
import { useCustomer } from '@contexts/customer.context';
import { useSettings } from '@contexts/settings.context';
import { parseToFloat } from '@framework/product/use-price';
import Link from '@components/ui/link';
import { UserWallet, WalletGroup, WALLET_GROUP_TYPE } from '@framework/types';
import { ROUTES } from '@utils/routes';
import { useTranslation } from 'next-export-i18n';
import WalletTransactionStatusTabs from '@components/wallet/wallet-transaction-status-tabs';
import { siteSettings } from '@settings/site-settings';
import BackButton from '@components/common/back-button';
import { useIsWalletExpiring } from '@framework/wallet/wallet.utils';

export default function AccountWalletActivityContainer() {
  const { t } = useTranslation();
  const { customer } = useCustomer();
  const point_wallet =
    customer && customer.wallets
      ? customer.wallets.find(
          (wallet: UserWallet) =>
            wallet.wallet_group.type === WALLET_GROUP_TYPE.POINT
        )
      : null;

  const { wallets_info } = useSettings();
  const point_wallet_info = wallets_info
    ? wallets_info.find(
        (wallet_group: WalletGroup) =>
          wallet_group.type === WALLET_GROUP_TYPE.POINT
      )
    : null;

  const {
    expire_date
  } = useIsWalletExpiring(point_wallet!, point_wallet_info);

  return (
    <div>
      <div className='px-2 pb-5 md:pb-6 lg:pb-7 pt-4 lg:pt-1'>
        <BackButton text="Back" className="mb-4" />
        <Heading
          variant="titleLarge"
          className="bg-white"
        >
          {t('text-account-details-wallets-activity')}
        </Heading>
      </div>
      <div className="flex justify-between items-center gap-2 text-white bg-gradient-to-r from-yellow-500 to-red-500 rounded-none sm:rounded px-2 py-3 mb-3 md:mb-4 lg:mb-5">
        {point_wallet && point_wallet_info && (
          <>
            <div className="flex items-center gap-2">
              <img src={siteSettings.reward_point_icon} className="w-8 h-8" />
              <div>
                <div>
                  <span className="font-semibold">
                    {parseToFloat(point_wallet.balance).toFixed(0)}{' '}
                    {point_wallet_info.name}
                    {point_wallet.balance > 1 ? 's' : ''}
                  </span>
                </div>
                {expire_date ? (
                  <span className="text-xs text-white font-semibold">
                    {parseToFloat(point_wallet.balance).toFixed(0)}{' '}
                    {point_wallet_info.name}
                    {point_wallet.balance > 1 ? 's ' : ' '}
                    expiring on{' '}
                    {expire_date}
                  </span>
                ) : <></>}
              </div>
            </div>
            <Link
              href={ROUTES.KK_COINS}
              className="flex items-center text-end gap-1 text-xs sm:text-sm md:text-base bg-transparent text-white rounded-md p-1.5"
            >
              Earn More{' '}
              <ChevronRightIcon
                className="block w-[22px] h-[22px]"
                stroke="#FFFFFF"
              />
            </Link>
          </>
        )}
      </div>
      <WalletTransactionStatusTabs />
      <WalletTransactionsInfiniteList />
    </div>
  );
}
