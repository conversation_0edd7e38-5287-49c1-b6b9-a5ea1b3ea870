import { useTranslation } from 'next-export-i18n';
import MapIcon from '@components/icons/account-address';
import { ROUTES } from '@utils/routes';
import { Me } from '@framework/types';
import Link from '@components/ui/link';
import TruckIcon from '@components/icons/truck-icon';
import { BiChevronRight } from 'react-icons/bi';
import { useStaticBlocksQuery } from '@framework/static-block/get-static-blocks.query';

interface AccountDashboardProps {
  data: Me;
}

const accountMenu = [
  // {
  //   slug: ROUTES.WISHLIST,
  //   name: 'text-wishlist',
  //   icon: <WishlistIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // }
  // {
  //   slug: ROUTES.WISHLIST,
  //   name: 'text-wishlist',
  //   icon: <WishlistIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // },
  // {
  //   slug: ROUTES.ORDERS,
  //   name: 'text-orders',
  //   icon: <OrdersIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // },
  // {
  //   slug: ROUTES.REFERRAL,
  //   name: 'text-refer-to-friend',
  //   icon: (
  //     <BsShare className="w-5 md:w-[22px] h-5 md:h-[22px] text-[#8C969F]" />
  //   ),
  //   hideInWebView: true,
  // },
  // {
  //   slug: ROUTES.ACCOUNT_SETTING,
  //   name: 'account-settings',
  //   icon: <EditIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // },
  // {
  //   slug: ROUTES.ADDRESS,
  //   name: 'text-address',
  //   icon: <MapIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // },
  // {
  //   slug: ROUTES.CHANGE_PASSWORD,
  //   name: 'text-change-password',
  //   icon: (
  //     <IoSettingsOutline className="w-5 md:w-[22px] h-5 md:h-[22px] text-[#8C969F]" />
  //   ),
  //   hideInWebView: true,
  // },
  // {
  //   slug: ROUTES.LOGOUT,
  //   name: 'text-logout',
  //   icon: <LogoutIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  //   hideInWebView: true,
  // },
];

const AccountDashboard: React.FC<AccountDashboardProps> = ({ data }) => {
  const { t } = useTranslation();
  const { data: staticBlockData } = useStaticBlocksQuery({
    show_in_sidemenu: true,
  });
  return (
    <div className="w-full flex flex-col bg-white">
      {/* <Heading
        variant="titleLarge"
        className="px-2 pb-5 md:pb-6 lg:pb-7 pt-4 lg:pt-1 bg-white"
      >
        {t('text-account-details-dashboard')}
      </Heading>

      <AccountLinkCard data={data} />

      <AccountNotificationSection /> */}

      {/* <AccountPurchasesSection /> */}

      {/* <AccountWalletSection data={data} /> */}

      <div className="bg-white mb-2 sm:mb-4 lg:mb-5 border-0 sm:border rounded-none sm:rounded">
        <div className="flex justify-around text-sm sm:text-base py-3">
          <Link
            href={ROUTES.ORDERS}
            className="flex flex-col gap-2 items-center"
          >
            <TruckIcon
              className="w-5 md:w-[22px] h-5 md:h-[22px]"
              stroke="#595959"
            />
            My Orders
          </Link>
          <Link
            href={ROUTES.ADDRESS}
            className="flex flex-col gap-2 items-center"
          >
            <MapIcon
              className="w-5 md:w-[22px] h-5 md:h-[22px]"
              stroke="#595959"
            />
            My Addresses
          </Link>
        </div>
      </div>

      {/* <AccountReorderSection />

      <AccountRecentlyViewedSection /> */}

      {/* <div className="block lg:hidden bg-white">
        <AccountNav options={accountMenu} />
      </div> */}
      <div className="border-t-8 border-skin-base">
        {staticBlockData?.map((block) => (
          <Link href={`/p?_s=${block.key}`}>
            <div className="px-4 py-3 flex items-center">
              <div>{block.name}</div>
              <BiChevronRight className="ml-auto text-skin-primary" size={24} />
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default AccountDashboard;
