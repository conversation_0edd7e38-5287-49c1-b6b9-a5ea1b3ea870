import ChevronRightIcon from "@components/icons/chevron-right";
import NotificationItem from "@components/notification/notification-item";
import Link from "@components/ui/link";
import Text from "@components/ui/text";
import { useNotificationsQuery } from "@framework/notification/use-notifications-query";
import { SORT_TYPE } from "@framework/types";
import { ROUTES } from "@utils/routes";
import { BiBell } from "react-icons/bi";
import { ImSpinner2 } from "react-icons/im";

interface AccountNotificationProps {}

const AccountNotificationSection: React.FC<AccountNotificationProps> = () => {
  const { data, isLoading } = useNotificationsQuery({
    limit: 1,
    order_by: 'created_at',
    sorted_by: SORT_TYPE.DESC
  });

  return (
    <div className="bg-white mb-2 sm:mb-4 lg:mb-5 border-0 sm:border rounded-none sm:rounded">
      <div className="px-2 py-3 border-t border-b sm:border-t-0">
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-sm sm:text-base">
            <BiBell className="text-skin-primary w-5 h-5" /> My Notifications
          </div>
          <Link
            href={ROUTES.NOTIFICATION}
            className="flex items-center gap-1 text-xs sm:text-sm sm:text-base"
          >
            View Notifications{' '}
            <ChevronRightIcon className="w-4 sm:w-[22px] h-4 sm:h-[22px]" />
          </Link>
        </div>
      </div>
      <div className="px-2 py-3 border-b sm:border-0">
        {(isLoading || !data) ? (
          <div className="py-2">
            <ImSpinner2 className="animate-spin text-skin-primary w-4 h-4 mx-auto" />
          </div>
        ) : (
          !data.pages[0].data.length ? (
            <div className="px-4">
              <Text variant="small">No notification found</Text>
            </div>
          ) : (
            <NotificationItem notification={data.pages[0].data[0]} className="px-4" />
          )
        )}
      </div>
    </div>
  );
}

export default AccountNotificationSection;