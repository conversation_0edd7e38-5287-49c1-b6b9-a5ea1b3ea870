import Container from '@components/ui/container';
import AccountNav from '@components/my-account/account-nav';
import AccountNavMobile from './account-nav-mobile';
import { ROUTES } from '@utils/routes';
import SettingsIcon from '@components/icons/account-settings';
import OrdersIcon from '@components/icons/account-order';
import WishlistIcon from '@components/icons/account-wishlist';
import MapIcon from '@components/icons/account-address';
import NotificationIcon from '@components/icons/account-notification';
import HelpIcon from '@components/icons/account-help';
import NoticeIcon from '@components/icons/account-notice';
import { IoSettingsOutline } from 'react-icons/io5';
import LogoutIcon from '@components/icons/account-logout';
import { getIsTNG } from '@utils/use-tng';
import EditIcon from '@components/icons/edit-icon';
import { ImCoinDollar } from 'react-icons/im';
import { BsShare } from 'react-icons/bs';
import CouponIcon from '@components/icons/featured/coupon-icon';

const accountMenu = [
  {
    slug: ROUTES.ACCOUNT,
    name: 'text-account',
    icon: <SettingsIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  },
  {
    slug: ROUTES.ACCOUNT_SETTING,
    name: 'account-settings',
    icon: <EditIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  },
  {
    slug: ROUTES.ORDERS,
    name: 'text-orders',
    icon: <OrdersIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  },
  {
    slug: ROUTES.VOUCHERS,
    name: 'text-vouchers',
    icon: (
      <CouponIcon className="w-5 md:w-[22px] h-5 md:h-[22px] text-[#8C969F]" />
    ),
  },
  {
    slug: ROUTES.COIN,
    name: 'text-wallets-activity',
    icon: (
      <ImCoinDollar className="w-5 md:w-[22px] h-5 md:h-[22px] text-[#8C969F]" />
    ),
  },
  {
    slug: ROUTES.REFERRAL,
    name: 'text-refer-to-friend',
    icon: (
      <BsShare className="w-5 md:w-[22px] h-5 md:h-[22px] text-[#8C969F]" />
    ),
    hideInWebView: true,
  },
  {
    slug: ROUTES.WISHLIST,
    name: 'text-wishlist',
    icon: <WishlistIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  },
  {
    slug: ROUTES.ADDRESS,
    name: 'text-address',
    icon: <MapIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  },
  {
    slug: ROUTES.NOTIFICATION,
    name: 'text-notifications',
    icon: <NotificationIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  },
  // {
  //   slug: ROUTES.LEGAL_NOTICE,
  //   name: 'text-account-details-notice',
  //   icon: <NoticeIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // },
  // {
  //   slug: ROUTES.HELP_CENTER,
  //   name: 'text-account-details-help',
  //   icon: <HelpIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
  // },
  {
    slug: ROUTES.CHANGE_PASSWORD,
    name: 'text-change-password',
    icon: (
      <IoSettingsOutline className="w-5 md:w-[22px] h-5 md:h-[22px] text-[#8C969F]" />
    ),
    hideInWebView: true,
  },
  {
    slug: ROUTES.LOGOUT,
    name: 'text-logout',
    icon: <LogoutIcon className="w-5 md:w-[22px] h-5 md:h-[22px]" />,
    hideInWebView: true,
  },
];

const AccountLayout: React.FunctionComponent<{}> = ({ children }) => {
  return (
    <div className="border-t border-b border-skin-base">
      <Container
        className="mx-auto max-w-[1920px] px-0 lg:px-8 2xl:px-10"
        clean
      >
        {/* <div className="hidden md:block pt-10 2xl:pt-12 pb-12 xl:pb-16 2xl:pb-20 xl:max-w-screen-xl 2xl:max-w-[1300px] mx-auto">
          <div className="flex flex-col xl:flex-row w-full">
            <div className="xl:hidden">
              <AccountNavMobile
                options={accountMenu.filter(
                  ({ hideInWebView }) =>
                    !hideInWebView || (hideInWebView && !getIsTNG())
                )}
              />
            </div>
            <div className="hidden xl:block flex-shrink-0 w-80 xl:w-1/5 me-7 xl:me-8">
              <AccountNav
                options={accountMenu.filter(
                  ({ hideInWebView }) =>
                    !hideInWebView || (hideInWebView && !getIsTNG())
                )}
              />
            </div>

            <div className="w-full xl:w-4/5 mt-4 xl:mt-0 border-0 border-t sm:border sm:border-skin-base p-0 sm:p-5 xl:py-8 2xl:py-10 xl:px-9 2xl:px-12 rounded-md">
              {children}
            </div>
          </div>
        </div> */}
        <div className="lg:pt-10 2xl:pt-12 lg:pb-12 xl:pb-16 2xl:pb-20 xl:max-w-screen-xl 2xl:max-w-[1300px] mx-auto">
          <div className="flex flex-row gap-4 w-full">
            <div className="hidden lg:block flex-shrink-0 w-1/5">
              <AccountNav
                options={accountMenu.filter(
                  ({ hideInWebView }) =>
                    !hideInWebView || (hideInWebView && !getIsTNG())
                )}
              />
            </div>

            <div className="w-full lg:w-4/5 border-0 lg:border lg:border-skin-base p-0 sm:p-5 xl:py-8 2xl:py-10 xl:px-9 2xl:px-12 rounded-md lg:rounded-lg">
              {children}
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default AccountLayout;
