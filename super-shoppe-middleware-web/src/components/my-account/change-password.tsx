import PasswordInput from '@components/ui/form/password-input';
import Button from '@components/ui/button';
import Heading from '@components/ui/heading';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'next-export-i18n';

import {
  useChangePasswordMutation,
  ChangePasswordInputType,
} from '@framework/customer/use-change-password';
import BackButton from '@components/common/back-button';

const defaultValues = {
  old_password: '',
  password: '',
  password_confirmation: '',
};

const ChangePassword: React.FC = () => {
  const { t } = useTranslation();
  const { mutate: changePassword, isLoading } = useChangePasswordMutation();

  const {
    register,
    handleSubmit,
    getValues,
    reset,
    setError,
    formState: { errors },
  } = useForm<ChangePasswordInputType>({
    defaultValues,
  });
  function onSubmit(input: ChangePasswordInputType) {
    changePassword(input, {
      onSuccess() {
        reset();
      },
      onError(error: any) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          Object.keys(errorData).forEach(function eachKey(key: string) {
            // @ts-ignore
            setError(key, {
              message: errorData[key][0],
            });
          });
        }
      },
    });
  }
  return (
    <div className="p-5 sm:p-0">
      <BackButton text="Back" className="flex mb-4" />
      <Heading variant="titleLarge">
        {t('text-account-details-password')}
      </Heading>
      <div className="w-full flex  h-full lg:w-10/12 2xl:w-9/12 flex-col mt-6 lg:mt-7">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="w-full mx-auto flex flex-col justify-center "
        >
          <div className="flex flex-col space-y-5 lg:space-y-7">
            <PasswordInput
              label={t('forms.label-old-password')}
              error={errors.old_password?.message}
              {...register('old_password', {
                required: `${t('forms.password-old-required')}`,
              })}
            />
            <PasswordInput
              label={t('forms.label-new-password')}
              error={errors.password?.message}
              {...register('password', {
                required: `${t('forms.password-new-required')}`,
              })}
            />
            <PasswordInput
              label={t('forms.label-confirm-password')}
              error={errors.password_confirmation?.message}
              {...register('password_confirmation', {
                validate: {
                  passwordEqual: (value) =>
                    value === getValues().password || 'Password does not match',
                },
              })}
            />

            <div className="relative mt-3">
              <Button
                type="submit"
                loading={isLoading}
                disabled={isLoading}
                variant="formButton"
                className="w-full sm:w-auto"
              >
                {t('text-change-password')}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChangePassword;
