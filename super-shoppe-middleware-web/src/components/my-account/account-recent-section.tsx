import NoResult from '@components/common/no-result';
import ChevronRightIcon from '@components/icons/chevron-right';
import ProductCard from '@components/product/product-cards/product-card';
import Carousel from '@components/ui/carousel/carousel';
import { SwiperSlide } from '@components/ui/carousel/slider';
import Link from '@components/ui/link';
import ProductCardGridLoader from '@components/ui/loaders/product-card-grid-loader';
import { useRecentProductsQuery } from '@framework/order/get-recent-products';
import { ROUTES } from '@utils/routes';
import { BsClockHistory } from 'react-icons/bs';

const breakpoints = {
  0: {
    slidesPerView: 1,
    slidesPerColumn: 1,
    slidesPerGroup: 1,
  },
  320: {
    slidesPerView: 2,
    slidesPerColumn: 2,
    slidesPerGroup: 2,
  },
  640: {
    slidesPerView: 3,
    slidesPerColumn: 2,
    slidesPerGroup: 3,
  },
  1024: {
    slidesPerView: 4,
    slidesPerColumn: 2,
    slidesPerGroup: 4,
  },
  1280: {
    slidesPerView: 5,
    slidesPerColumn: 2,
    slidesPerGroup: 5,
  },
};

interface AccountRecentlyViewedProps {}

const AccountRecentlyViewedSection: React.FC<
  AccountRecentlyViewedProps
> = () => {
  const { data } = useRecentProductsQuery({
    limit: 20,
  });

  if (!data) {
    return <ProductCardGridLoader cardCount={12} />;
  }

  const children = data.pages.map((page, pageIndex) =>
    page.data.map((product, index) => (
      <SwiperSlide
        key={`${pageIndex}-${index}`}
        className="px-1.5 md:px-2 xl:px-2.5 pb-2"
      >
        <ProductCard product={product} />
      </SwiperSlide>
    ))
  );

  return (
    <div className="bg-white mb-2 sm:mb-4 lg:mb-5 border-0 sm:border rounded-none sm:rounded">
      <div className="px-2 py-3 border-t border-b sm:border-t-0">
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-sm sm:text-base">
            <BsClockHistory className="w-5 h-5 text-skin-primary" /> Recently
            Viewed Products
          </div>
          <Link
            href={ROUTES.RECENT_PRODUCTS}
            className="flex items-center gap-1 text-xs sm:text-sm sm:text-base"
          >
            See All{' '}
            <ChevronRightIcon className="w-4 sm:w-[22px] h-4 sm:h-[22px]" />
          </Link>
        </div>
      </div>
      <div className="px-2 py-3 border-b sm:border-0">
        {children.length > 0 && children[0].length > 0 ? (
          <Carousel
            slidesPerColumnFill="row"
            breakpoints={breakpoints}
            prevButtonClassName="start-0.5"
            nextButtonClassName="end-0.5"
          >
            {children}
          </Carousel>
        ) : (
          <NoResult />
        )}
      </div>
    </div>
  );
};

export default AccountRecentlyViewedSection;
