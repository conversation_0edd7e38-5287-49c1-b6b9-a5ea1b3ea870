import CopyToClipboard from '@components/common/copy-to-clipboard';
import Heading from '@components/ui/heading';
import Input from '@components/ui/input';
import TextLoader from '@components/ui/loaders/text-loader';
import { useSettings } from '@contexts/settings.context';
import { useReferralCodeQuery } from '@framework/auth/use-referral-code';
import { useSocialShareMutation } from '@framework/auth/use-social-share.mutation';
import { parseToFloat } from '@framework/product/use-price';
import { ROUTES } from '@utils/routes';
import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';
import React from 'react';
import {
  FacebookIcon,
  FacebookShareButton,
  TwitterIcon,
  TwitterShareButton,
  WhatsappIcon,
  WhatsappShareButton,
} from 'react-share';

interface ReferralCodeShareLinksProps {
  className?: string;
}

const ReferralCodeShareLinks: React.FC<ReferralCodeShareLinksProps> = ({
  className,
}) => {
  const { t } = useTranslation();
  const { referral_info } = useSettings();
  const { data, isLoading } = useReferralCodeQuery();
  const { mutate: triggerSocialShare } = useSocialShareMutation();
  if (isLoading || !data) {
    return <TextLoader />;
  }

  if (!data.referral_code || !referral_info) {
    return <></>;
  }

  const referralUrl = `${process.env.NEXT_PUBLIC_SITE_URL}${ROUTES.REFER}?code=${data.referral_code}`;

  return (
    <div className={cn('', className)}>
      <div>
        <div className="mb-4">
          <Heading className="mb-2">Your referral link to share</Heading>
          <div>
            <Input
              type="text"
              variant="solid"
              name="referral_code"
              className="w-full"
              value={referralUrl}
              disabled
              inputClassName="px-4 border-skin-base rounded-none focus:outline focus:border-skin-primary"
            />
          </div>
          <div className="w-full bg-skin-primary py-2 relative">
            <CopyToClipboard
              absolute={false}
              text={referralUrl}
              textClassName="text-white"
              fillClassName="bg-skin-primary"
              className="justify-center"
            />
          </div>
        </div>
        <div>
          <Heading className="mb-2">{t('text-share-social-network')}</Heading>
          <div className="flex justify-center gap-2">
            <FacebookShareButton
              url={referralUrl}
              quote={`Register KK Mart account now and get ${parseToFloat(
                referral_info.amount
              ).toFixed(0)} KK Coins after completed your first order.`}
              windowWidth={800}
              windowHeight={800}
              onClick={() => triggerSocialShare()}
            >
              <FacebookIcon
                size={40}
                round
                className="transition-all hover:opacity-90"
              />
            </FacebookShareButton>
            <TwitterShareButton
              url={referralUrl}
              title={`Register KK Mart account now and get ${parseToFloat(
                referral_info.amount
              ).toFixed(0)} KK Coins after completed your first order.`}
              windowWidth={800}
              windowHeight={800}
              onClick={() => triggerSocialShare()}
            >
              <TwitterIcon
                size={40}
                round
                className="transition-all hover:opacity-90"
              />
            </TwitterShareButton>
            <WhatsappShareButton
              url={referralUrl}
              title={`Register KK Mart account now and get ${parseToFloat(
                referral_info.amount
              ).toFixed(0)} KK Coins after completed your first order.`}
              windowWidth={800}
              windowHeight={800}
              separator=""
              onClick={() => triggerSocialShare()}
            >
              <WhatsappIcon
                size={40}
                round
                className="transition-all hover:opacity-90"
              />
            </WhatsappShareButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralCodeShareLinks;
