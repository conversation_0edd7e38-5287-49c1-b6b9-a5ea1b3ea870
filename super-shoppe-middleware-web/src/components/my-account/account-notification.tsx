import { useModalAction } from '@components/common/modal/modal.context';
import Checkbox from '@components/ui/checkbox/checkbox';
import Heading from '@components/ui/heading';
import { useCustomer } from '@contexts/customer.context';
import { useUpdateUserNotificationSettingsMutation } from '@framework/customer/use-update-user-notification-settings.mutation';
import { useTranslation } from 'next-export-i18n';
import { useState } from 'react';
import { toast } from 'react-toastify';

const AccountNotification: React.FC<{}> = ({}) => {
  const { t } = useTranslation();
  const { customer } = useCustomer();
  const { mutate } = useUpdateUserNotificationSettingsMutation();
  const is_push_notification_enabled = customer?.is_push_notification_enabled;

  const [checked, setChecked] = useState(is_push_notification_enabled);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(e.target.checked);
    mutate(
      {
        is_push_notification_enabled: e.target.checked,
      },
      {
        onSuccess: () => {
          toast.success(t('text-notification-settings-updated'));
        },
      }
    );
  };
  return (
    <>
      <Heading variant="titleLarge" className="mb-5 md:mb-6 lg:mb-7">
        {t('text-account-notification-manage')}
      </Heading>
      <div className="w-full mx-auto flex flex-col justify-center mt-4">
        <div>
          <span>{t('text-account-notification-description')}</span>
        </div>
        <div className="relative flex mt-5 pb-2 lg:pb-0">
          <Checkbox
            name="is_notification"
            label={t('text-push-notification')}
            checked={checked}
            onChange={handleChange}
          />
        </div>
      </div>
    </>
  );
};

export default AccountNotification;
