import Heading from '@components/ui/heading';
import PageLoader from '@components/ui/loaders/page-loader';
import Pagination from '@components/ui/pagination';
import { useReferralsQuery } from '@framework/referral/use-referrals-query';
import { SORT_TYPE, User } from '@framework/types';
import cn from 'classnames';
import Table from 'rc-table';
import React, { useState } from 'react';
import { GrNext, GrPrevious } from 'react-icons/gr';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import relativeTime from 'dayjs/plugin/relativeTime';
import timezone from 'dayjs/plugin/timezone';

const PAGE_LIMIT = 10;

interface ReferralsListProps {
  className?: string;
}

const CreatedAt: React.FC<{ createdAt?: any }> = ({ createdAt }) => {
  dayjs.extend(relativeTime);
  dayjs.extend(utc);
  dayjs.extend(timezone);
  return (
    <span className="whitespace-nowrap">
      {dayjs.utc(createdAt).tz(dayjs.tz.guess()).fromNow()}
    </span>
  );
};

export const Status: React.FC<{ status: string }> = ({ status }) => {
  return (
    <span
      className={cn(
        'capitalize rounded-md px-4 py-2',
        status == 'success'
          ? 'bg-green-300 bg-opacity-10 text-green-300'
          : 'bg-skin-primary bg-opacity-10 text-skin-primary'
      )}
    >
      {status == 'pending'
        ? 'Pending Order Placement'
        : status == 'success'
        ? 'Rewarded'
        : status}
    </span>
  );
};

const columns = [
  {
    title: 'Referee',
    dataIndex: 'referee',
    key: 'referee',
    render: (referee: User) => (
      <span className="text-skin-primary">{referee.name}</span>
    ),
  },
  {
    title: 'Date Referred',
    dataIndex: 'created_at',
    key: 'created_at',
    render: (created_at: string) => <CreatedAt createdAt={created_at} />,
  },
  {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => <Status status={status} />,
  },
];

const ReferralsList: React.FC<ReferralsListProps> = ({ className }) => {
  const [page, setPage] = useState(1);
  const { data, isLoading } = useReferralsQuery({
    limit: PAGE_LIMIT,
    page,
    order_by: 'created_at',
    sorted_by: SORT_TYPE.DESC,
  });

  if (isLoading || !data) {
    return <PageLoader />;
  }

  const { data: referrals, current_page, total } = data;

  const updatePage = (p: any) => {
    setPage(p);
  };

  return (
    <div className={cn(className)}>
      <>
        <Heading variant="title" className="mb-3">
          My referrals list
        </Heading>
        <div className="">
          <div className="order-list-table-wraper">
            <Table
              className="order-list-table"
              columns={columns}
              data={referrals}
              scroll={{
                x: 'max-content',
              }}
              rowKey="id"
              emptyText="No Record Found"
            />
          </div>
          <div className="text-center mt-5">
            <Pagination
              current={current_page}
              onChange={updatePage}
              pageSize={PAGE_LIMIT}
              total={total}
              prevIcon={<GrPrevious size={12} style={{ color: '#090B17' }} />}
              nextIcon={<GrNext size={12} style={{ color: '#090B17' }} />}
              className="order-table-pagination"
            />
          </div>
        </div>
      </>
    </div>
  );
};

export default ReferralsList;
