import NoResult from '@components/common/no-result';
import ChevronRightIcon from '@components/icons/chevron-right';
import ShoppingBagIcon from '@components/icons/shopping-bag-icon';
import Carousel from '@components/ui/carousel/carousel';
import { SwiperSlide } from '@components/ui/carousel/slider';
import Link from '@components/ui/link';
import { useOrderProductsQuery } from '@framework/order/get-order-products';
import { ROUTES } from '@utils/routes';
import BuyAgainProductCard from './buy-again-product-card';
import ProductCardGridLoader from '@components/ui/loaders/product-card-grid-loader';

const breakpoints = {
  0: {
    slidesPerView: 1,
    slidesPerColumn: 1,
    slidesPerGroup: 1,
  },
  320: {
    slidesPerView: 2,
    slidesPerColumn: 2,
    slidesPerGroup: 2,
  },
  640: {
    slidesPerView: 3,
    slidesPerColumn: 2,
    slidesPerGroup: 3,
  },
  1024: {
    slidesPerView: 4,
    slidesPerColumn: 2,
    slidesPerGroup: 4,
  },
  1280: {
    slidesPerView: 5,
    slidesPerColumn: 2,
    slidesPerGroup: 5,
  },
};

interface AccountReorderProps {}

const AccountReorderSection: React.FC<AccountReorderProps> = () => {
  const { data } = useOrderProductsQuery({
    limit: 20,
  });

  if (!data) {
    return <ProductCardGridLoader cardCount={12} />;
  }

  const children = data.pages.map((page, pageIndex) =>
    page.data.map((product, index) => (
      <SwiperSlide
        key={`${pageIndex}-${index}`}
        className="px-1.5 md:px-2 xl:px-2.5 pb-2"
      >
        <BuyAgainProductCard product={product} />
      </SwiperSlide>
    ))
  );

  return (
    <div className="bg-white mb-2 sm:mb-4 lg:mb-5 border-0 sm:border rounded-none sm:rounded">
      <div className="px-2 py-3 border-t border-b sm:border-t-0">
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-sm sm:text-base">
            <ShoppingBagIcon className="w-5 h-5 text-skin-primary" /> Buy Again
          </div>
          <Link
            href={ROUTES.ORDERS_PRODUCTS}
            className="flex items-center gap-1 text-xs sm:text-sm sm:text-base"
          >
            See More Items{' '}
            <ChevronRightIcon className="w-4 sm:w-[22px] h-4 sm:h-[22px]" />
          </Link>
        </div>
      </div>
      <div className="px-2 py-3 border-b sm:border-0">
        {children.length > 0 && children[0].length > 0 ? (
          <Carousel
            slidesPerColumnFill="row"
            breakpoints={breakpoints}
            prevButtonClassName="start-0.5"
            nextButtonClassName="end-0.5"
          >
            {children}
          </Carousel>
        ) : (
          <NoResult />
        )}
      </div>
    </div>
  );
};

export default AccountReorderSection;
