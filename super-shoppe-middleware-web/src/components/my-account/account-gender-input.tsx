import React from "react";
import { useTranslation } from "next-export-i18n";

interface GenderRadioProps {
  data: string;
};

const GenderRadio = React.forwardRef<HTMLInputElement, GenderRadioProps>(({
  data,
  ...rest
}, ref) => (
  <div className="flex items-center">
    <input
      type="radio"
      id={data}
      value={data}
      className="me-2"
      ref={ref}
      {...rest}
    />
    <label htmlFor={data} className="text-body text-sm capitalize">
      {data}
    </label>
  </div>
));

interface GenderProps {
  className?: string;
  register: any;
  error?: string;
};

const GENDER_OPTIONS = [
  "male", "female"
];
  
const GenderInput: React.FC<GenderProps> = ({
    className,
    register,
    error,
}) => {
    const { t } = useTranslation();
    const genderInputProps = register("gender", { required: t('forms.gender-required') });

    return (
      <div className={className}>
        <label className="block text-skin-base opacity-70 font-normal text-sm leading-none mb-3 cursor-pointer">
          {t('forms.label-gender')}
        </label>
        {GENDER_OPTIONS.map((gender: string, idx: number) => (
          <div key={idx} className="px-1 mb-1">
            <GenderRadio
              data={gender}
              {...genderInputProps}
            />
          </div>
        ))}
        {error && (
          <p className="my-2 text-13px text-skin-red text-opacity-70">
            {t(error)}
          </p>
        )}
      </div>
    );
};

export default GenderInput;