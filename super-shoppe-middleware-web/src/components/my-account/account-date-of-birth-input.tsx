import DatePickerInput from "@components/ui/form/date-picker-input";
import { useSettings } from "@contexts/settings.context";
import moment from "moment";
import { useTranslation } from "next-export-i18n";

interface DateOfBirthProps {
    className?: string;
    error?: string;
    control: any;
    register: any;
    datePickerProps?: any;
}
  
const DateOfBirth: React.FC<DateOfBirthProps> = ({
    className,
    control,
    error,
    register,
    datePickerProps,
}) => {
    const { t } = useTranslation();
    const { dob_min_year } = useSettings();
    return (
      <div className={className}>
        <label className="block text-skin-base opacity-70 font-normal text-sm leading-none mb-3 cursor-pointer">
          {t('forms.label-date-of-birth')}
        </label>
        <DatePickerInput
          control={control}
          {...register('dob', {
            required: t('forms.dob-required'),
          })}
          datePickerProps={{
            dateFormat: 'dd/MM/yyyy',
            showTimeInput: false,
            showMonthDropdown: true,
            showYearDropdown: true,
            maxDate: dob_min_year
              ? moment().subtract(dob_min_year, 'years').toDate()
              : new Date(),
            dropdownMode: 'select',
            ...datePickerProps,
          }}
        />
        {error && (
          <p className="my-2 text-13px text-skin-red text-opacity-70">
            {t(error)}
          </p>
        )}
      </div>
    );
};

export default DateOfBirth;