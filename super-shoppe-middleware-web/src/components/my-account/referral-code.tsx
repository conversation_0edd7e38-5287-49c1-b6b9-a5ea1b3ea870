import CopyToClipboard from '@components/common/copy-to-clipboard';
import Input from '@components/ui/form/input';
import Heading from '@components/ui/heading';
import TextLoader from '@components/ui/loaders/text-loader';
import { useReferralCodeQuery } from '@framework/auth/use-referral-code';
import cn from 'classnames';
import React from 'react';
import ReferralCodeShareLinks from './referral-code-share-links';

interface ReferralCodeProps {
  className?: string;
}

const ReferralCode: React.FC<ReferralCodeProps> = ({ className }) => {
  const { data, isLoading } = useReferralCodeQuery();

  if (isLoading || !data) {
    return <TextLoader />;
  }

  if (!data.referral_code) {
    return <></>;
  }

  return (
    <div className={cn('text-center', className)}>
      <Heading className="">Your referral code</Heading>
      <div className="relative mt-2.5 mb-1.5">
        <Input
          type="text"
          variant="solid"
          name="referral_code"
          className="w-full"
          value={data.referral_code}
          disabled
          inputClassName="px-4 border-skin-base rounded-md focus:outline focus:border-skin-primary"
        />
        <CopyToClipboard text={data.referral_code} />
      </div>
      <ReferralCodeShareLinks className="mt-4" />
    </div>
  );
};

export default ReferralCode;
