import AccountSettingsSolidIcon from '@components/icons/account-settings-solid';
import EditIcon from '@components/icons/edit-icon';
import Link from '@components/ui/link';
import { Me } from '@framework/types';
import { ROUTES } from '@utils/routes';
import { getIsTNG } from '@utils/use-tng';

interface AccountDashboardProps {
  data: Me;
  className?: string;
}

const AccountLinkCard: React.FC<AccountDashboardProps> = ({
  data,
  className = 'flex justify-between items-center gap-2 text-white bg-gradient-to-r from-yellow-500 to-red-500 rounded-none sm:rounded px-2 py-3 mb-3 md:mb-4 lg:mb-5',
}) => {
  const { me } = data;
  return (
    <>
      <Link href={ROUTES.ACCOUNT_SETTING} className={className}>
        <div className="flex items-center gap-2">
          <AccountSettingsSolidIcon
            className="w-8 md:w-[32px] h-8 md:h-[32px]"
            fill={'#FFFFFF'}
            stroke={'#FFFFFF'}
          />
          <span className="text-lg">
            {me ? me.name : 'User'}
          </span>
        </div>
        <div className="flex items-center gap-1 text-xs sm:text-sm md:text-base">
          Edit <span className="hidden sm:block">Profile</span>{' '}
          <EditIcon
            className="w-4 sm:w-[22px] h-4 sm:h-[22px]"
            stroke="#FFFFFF"
          />
        </div>
      </Link>
    </>
  );
};

export default AccountLinkCard;
