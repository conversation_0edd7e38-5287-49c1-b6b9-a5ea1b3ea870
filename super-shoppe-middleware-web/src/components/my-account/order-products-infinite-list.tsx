import NoResult from '@components/common/no-result';
import { useOrderProductsQuery } from '@framework/order/get-order-products';
import { Waypoint } from 'react-waypoint';
import BuyAgainProductCard from './buy-again-product-card';
import ProductCardGridLoader from '@components/ui/loaders/product-card-grid-loader';
import ProductCardLoader from '@components/ui/loaders/product-card-loader';

const OrderProductsInfiniteList = () => {
  const {
    isFetching: isLoading,
    isFetchingNextPage: loadingMore,
    fetchNextPage,
    hasNextPage,
    data,
  } = useOrderProductsQuery({
    limit: 20,
  });

  if (!data) return <ProductCardGridLoader />;

  function loadMoreItems() {
    if (hasNextPage) {
      fetchNextPage();
    }
  }

  const children = data.pages.map((page, pageIndex) =>
    page.data.map((product, index) => (
      <BuyAgainProductCard key={`${pageIndex}-${index}`} product={product} />
    ))
  );

  return (
    <>
      {children.length > 0 && children[0].length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-3 xl:grid-cols-4 gap-2 2xl:gap-4 mt-2.5">
          {children}
          {!isLoading && !loadingMore ? (
            <Waypoint onEnter={loadMoreItems} />
          ) : (
            Array.from({ length: 16 }).map((_, idx) => (
              <ProductCardLoader
                key={`order--key-${idx}`}
                uniqueKey={`order--key-${idx}`}
              />
            ))
          )}
        </div>
      ) : (
        <NoResult />
      )}
    </>
  );
};

export default OrderProductsInfiniteList;
