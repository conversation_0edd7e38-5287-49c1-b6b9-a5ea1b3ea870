import CouponIcon from '@components/icons/featured/coupon-icon';
import Link from '@components/ui/link';
import { useSettings } from '@contexts/settings.context';
import { parseToFloat } from '@framework/product/use-price';
import {
  Me,
  UserWallet,
  WalletGroup,
  WALLET_GROUP_TYPE,
} from '@framework/types';
import { ROUTES } from '@utils/routes';
import { useWindowSize } from 'react-use';
import { BiWallet } from 'react-icons/bi';
import { siteSettings } from '@settings/site-settings';
import { useIsWalletExpiring } from '@framework/wallet/wallet.utils';

interface AccountWalletProps {
  data: Me;
}

const AccountWalletSection: React.FC<AccountWalletProps> = ({ data }) => {
  const { width } = useWindowSize();
  const { me } = data;
  const point_wallet =
    me && me.wallets
      ? me.wallets.find(
          (wallet: UserWallet) =>
            wallet.wallet_group.type === WALLET_GROUP_TYPE.POINT
        )
      : null;

  const { wallets_info } = useSettings();
  const point_wallet_info = wallets_info
    ? wallets_info.find(
        (wallet_group: WalletGroup) =>
          wallet_group.type === WALLET_GROUP_TYPE.POINT
      )
    : null;

  const {
    expire_date,
    expire_in_months
  } = useIsWalletExpiring(point_wallet!, point_wallet_info);

  return (
    <div className="bg-white mb-2 sm:mb-4 lg:mb-5 border-0 sm:border rounded-none sm:rounded">
      <div className="px-2 py-3 border-t border-b sm:border-t-0">
        <div className="flex items-center gap-2 text-sm sm:text-base">
          <BiWallet className="w-5 h-5 text-skin-primary" /> My Wallet
        </div>
      </div>
      <div className="px-2 py-3 border-b sm:border-0">
        <div className="flex justify-around text-sm sm:text-base py-3">
          {point_wallet && point_wallet_info && (
            <Link
              href={ROUTES.COIN}
              className="flex flex-col gap-2 items-center"
            >
              <img src={siteSettings.reward_point_icon} className="w-8 h-8" />
              {parseToFloat(point_wallet.balance).toFixed(0)}{' '}
              {point_wallet_info.name}
              {point_wallet.balance > 1 ? 's' : ''}
              {(expire_in_months && expire_date && expire_in_months >= 0 && expire_in_months <= 3) ? <span className="text-xs text-skin-primary font-semibold">Use before {expire_date}!</span> : ""}
            </Link>
          )}
          <Link
            href={ROUTES.VOUCHERS}
            className="flex flex-col gap-2 items-center"
          >
            <CouponIcon className="w-8 h-8" />
            My Vouchers
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AccountWalletSection;
