import { useModalAction } from "@components/common/modal/modal.context";
import Button from "@components/ui/button";
import Heading from "@components/ui/heading";
import { useTranslation } from "next-export-i18n";

const AccountDeleteSection: React.FC<{}> = ({}) => {
    const { t } = useTranslation();
    const { openModal } = useModalAction();
    function handleDeleteAccount() {
        openModal("DELETE_ACCOUNT");
    }
    return (
        <>
            <Heading variant="titleLarge" className="mb-5 md:mb-6 lg:mb-7">
                {t('text-account-details-removal')}
            </Heading>
            <div className="w-full mx-auto flex flex-col justify-center mt-4">
                <div className="pb-7 md:pb-8 lg:pb-10">
                <span>{t('text-account-details-removal-description')}</span>
                </div>
                <div className="relative flex sm:ms-auto mt-5 pb-2 lg:pb-0">
                <Button
                    type="button"
                    variant="error"
                    className="w-full sm:w-auto"
                    onClick={handleDeleteAccount}
                >
                    {t('button-delete-account')}
                </Button>
                </div>
            </div>
        </>
    );
}

export default AccountDeleteSection;