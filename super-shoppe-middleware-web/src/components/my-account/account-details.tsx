import Input from '@components/ui/form/input';
import PasswordInput from '@components/ui/form/password-input';
import Button from '@components/ui/button';
import Heading from '@components/ui/heading';
import { useForm } from 'react-hook-form';
import {
  useUpdateUserMutation,
  UpdateUserType,
} from '@framework/customer/use-update-customer';
import { useTranslation } from 'next-export-i18n';
import moment from 'moment';
import { getIsTNG } from '@utils/use-tng';
import BackButton from '@components/common/back-button';
import AccountDeleteSection from './account-delete';
import dynamic from 'next/dynamic';
import { remapEmail, remapPhoneNumber } from '@framework/utils/data-mappers';
import { useState } from 'react';
import { useModalAction } from '@components/common/modal/modal.context';
import { BsCheckCircleFill } from 'react-icons/bs';
import DateOfBirth from './account-date-of-birth-input';
import GenderInput from './account-gender-input';
import { VERIFY_ACTION_TYPE } from '@framework/types';
import AccountNotification from './account-notification';

const PhoneNumberInput = dynamic(
  () => import('@components/ui/phone-number-input')
);

interface AccountDetailsProps {
  data: any;
}

const AccountDetails: React.FC<AccountDetailsProps> = ({ data }) => {
  const { t } = useTranslation();
  const { openModal } = useModalAction();
  const [emailCode, setEmailCode] = useState<string | null>(null);
  const [phoneCode, setPhoneCode] = useState<string | null>(null);

  const initialValues = {
    name: data.me ? data.me.name : '',
    email: data.me ? data.me.email : '',
    phone: data.me ? data.me.phone : '',
    dob: data.me ? (data.me.dob ? moment(data.me.dob).toDate() : null) : null,
    gender: data.me ? data.me.gender : '',
    password: '',
  };

  const { mutate: updateUser, isLoading } = useUpdateUserMutation();
  const {
    register,
    handleSubmit,
    setError,
    control,
    setValue,
    formState: { errors },
  } = useForm<UpdateUserType>({
    defaultValues: initialValues,
  });

  function handleEmailEdit() {
    openModal(
      'VERIFY_EMAIL_VIEW',
      { type: VERIFY_ACTION_TYPE.UPDATE_PROFILE },
      false,
      {
        confirmCallback: ({
          verifiedEmail,
          verifiedToken,
        }: {
          verifiedEmail: string;
          verifiedToken: string;
        }) => {
          setValue('email', verifiedEmail);
          setEmailCode(verifiedToken);
        },
        cancelCallback: () => {},
      }
    );
  }

  function handlePhoneEdit() {
    openModal(
      'VERIFY_PHONE_VIEW',
      { type: VERIFY_ACTION_TYPE.UPDATE_PROFILE },
      false,
      {
        confirmCallback: ({
          verifiedPhone,
          verifiedToken,
        }: {
          verifiedPhone: string;
          verifiedToken: string;
        }) => {
          setValue('phone', verifiedPhone);
          setPhoneCode(verifiedToken);
        },
        cancelCallback: () => {},
      }
    );
  }

  function onSubmit(input: UpdateUserType) {
    // prefill all other details with existing info for TNG
    if (getIsTNG()) {
      input = {
        name: initialValues.name,
        email: initialValues.email,
        phone: initialValues.phone,
        dob: input.dob,
        gender: input.gender,
        password: initialValues.password,
      };
    }
    if (!data.me.dob) {
      if (moment(input.dob).isValid()) {
        if (
          confirm(
            'Please make sure date of birth that you entered is correct. You are not allowed to change this info again once saved.'
          )
        ) {
          input.dob = moment(input.dob)
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss');
        } else {
          return;
        }
      } else {
        input.dob = null;
      }
    }

    if (data.me.dob && input.dob) {
      // do not update dob if already set
      delete input.dob;
    }

    input.phone = remapPhoneNumber(input.phone)!;
    input.email = remapEmail(input.email)!;
    emailCode ? (input.email_change_token = emailCode) : null;
    phoneCode ? (input.phone_change_token = phoneCode) : null;
    handleUpdateUser(input);
  }
  function handleUpdateUser(input: UpdateUserType) {
    updateUser(input, {
      onSuccess() {
        setEmailCode(null);
        setPhoneCode(null);
      },
      onError(error: any) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          Object.keys(errorData).forEach(function eachKey(key: string) {
            if (
              ['email', 'phone'].includes(key) &&
              errorData[key][0] == 'not_verified'
            ) {
              // @ts-ignore
              setError(key, {
                message: `${
                  key === 'phone' ? 'Phone number' : 'Email'
                } not verified. Please verify for the new ${
                  key === 'phone' ? 'phone number' : key
                } before submitting.`,
              });
            } else {
              // @ts-ignore
              setError(key, {
                message: errorData[key][0],
              });
            }
          });
        }
      },
    });
  }
  return (
    <div className="w-full flex flex-col p-5 sm:p-0">
      <BackButton text="Back" className="flex mb-4" />
      <Heading variant="titleLarge" className="mb-5 md:mb-6 lg:mb-7">
        {t('text-account-details-personal')}
      </Heading>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="w-full mx-auto flex flex-col justify-center border-b pb-8 mb-8"
        noValidate
      >
        <div className="pb-7 md:pb-8 lg:pb-10">
          {getIsTNG() ? (
            <div className="flex flex-col sm:flex-row -mx-1.5 md:-mx-2.5 space-y-4 sm:space-y-0">
              <DateOfBirth
                className="w-full sm:w-1/2 px-1.5 md:px-2.5"
                control={control}
                register={register}
                datePickerProps={{
                  isClearable: !data.me.dob,
                  disabled: !!data.me.dob,
                }}
                error={errors.dob?.message}
              />
              <GenderInput
                className="w-full sm:w-1/2 px-1.5 md:px-2.5"
                register={register}
                error={errors.gender?.message}
              />
            </div>
          ) : (
            <div className="flex flex-col space-y-4 sm:space-y-5">
              <div className="-mx-1.5 md:-mx-2.5">
                <Input
                  label={t('forms.label-name')}
                  {...register('name', {
                    required: t('forms.name-required'),
                  })}
                  variant="solid"
                  className="px-1.5 md:px-2.5"
                  error={errors.name?.message}
                />
              </div>
              <div className="flex flex-col sm:flex-row -mx-1.5 md:-mx-2.5 space-y-4 sm:space-y-0">
                <div className="w-full sm:w-1/2 px-1.5 md:px-2.5">
                  <div className="flex items-end gap-2">
                    <Input
                      type="email"
                      label={t('forms.label-email')}
                      placeholder="<EMAIL>"
                      {...register('email', {
                        required: t('forms.email-required'),
                        pattern: {
                          value:
                            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                          message: t('forms.email-error'),
                        },
                      })}
                      disabled
                      className="flex-grow"
                      variant="solid"
                      error={errors.email?.message}
                    />
                    <span
                      onClick={handleEmailEdit}
                      className="text-xs text-skin-primary cursor-pointer hover:opacity-80 pb-4"
                    >
                      Edit
                    </span>
                  </div>
                  {emailCode ? (
                    <span className="flex text-[11px] text-skin-primary leading-tight mt-1">
                      <BsCheckCircleFill className="mr-0.5" />
                      <span>
                        Email Verified. Kindly remember to{' '}
                        <strong>Save Changes</strong> to save your verified
                        email address.
                      </span>
                    </span>
                  ) : (
                    <></>
                  )}
                </div>
                <div className="w-full sm:w-1/2 px-1.5 md:px-2.5">
                  <div className="w-full flex items-end gap-2">
                    <PhoneNumberInput
                      {...register('phone', {
                        required: t('forms.phone-required'),
                        pattern: {
                          value:
                            /^(\+?6?01)[02-46-9][-]?[\s]?[0-9]{3}[\s]?[0-9]{4}$|^(\+?6?01)[1][-]?[\s]?[0-9]{4}[\s]?[0-9]{4}$|^(\+?6?03)[-]?[\s]?[0-9]{4}[\s]?[0-9]{4}$/,
                          message: t('forms.phone-error'),
                        },
                      })}
                      control={control}
                      label={t('forms.label-phone')}
                      variant="outline"
                      error={errors.phone?.message}
                      autoComplete="tel"
                      disabled
                      className="flex-grow"
                      labelClassName="text-skin-muted"
                      onChange={(e: any) => {
                        //@ts-ignore
                        setValue('phone', e.target.value);
                      }}
                    />
                    <span
                      onClick={handlePhoneEdit}
                      className="text-xs text-skin-primary cursor-pointer hover:opacity-80 pb-4"
                    >
                      Edit
                    </span>
                  </div>
                  {phoneCode ? (
                    <span className="flex text-[11px] text-skin-primary leading-tight mt-1">
                      <BsCheckCircleFill className="mr-0.5" />
                      <span>
                        Phone Verified. Kindly remember to{' '}
                        <strong>Save Changes</strong> to save your verified
                        phone number.
                      </span>
                    </span>
                  ) : (
                    <></>
                  )}
                </div>
              </div>
              <div className="flex flex-col sm:flex-row -mx-1.5 md:-mx-2.5 space-y-4 sm:space-y-0">
                <DateOfBirth
                  className="w-full sm:w-1/2 px-1.5 md:px-2.5"
                  control={control}
                  register={register}
                  datePickerProps={{
                    isClearable: !data.me.dob,
                    disabled: !!data.me.dob,
                  }}
                  error={errors.dob?.message}
                />
                <GenderInput
                  className="w-full sm:w-1/2 px-1.5 md:px-2.5"
                  register={register}
                  error={errors.gender?.message}
                />
              </div>
              <div className="flex flex-col sm:flex-row -mx-1.5 md:-mx-2.5 space-y-4 sm:space-y-0">
                <PasswordInput
                  label={t('forms.label-current-password')}
                  {...register('password', {
                    required: t('forms.password-required'),
                  })}
                  className="w-full sm:w-1/2 px-1.5 md:px-2.5"
                  error={errors.password?.message}
                />
              </div>
            </div>
          )}
        </div>

        <div className="relative flex sm:ms-auto mt-5 pb-2 lg:pb-0">
          <Button
            type="submit"
            loading={isLoading}
            disabled={isLoading}
            variant="formButton"
            className="w-full sm:w-auto"
          >
            {t('button-save-changes')}
          </Button>
        </div>
      </form>

      <div className="border-b pb-8 mb-8">
        <AccountNotification />
      </div>
      <AccountDeleteSection />
    </div>
  );
};

export default AccountDetails;
