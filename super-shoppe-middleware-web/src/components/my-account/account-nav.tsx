import { useRouter } from 'next/router';
import { useTranslation } from 'next-export-i18n';
import Link from '@components/ui/link';

type Option = {
  name: string;
  slug: string;
  icon?: JSX.Element;
};

export default function AccountNav({ options }: { options: Option[] }) {
  const { t } = useTranslation();
  const { pathname } = useRouter();
  const newPathname = pathname.split('/').slice(1, 3);
  const mainPath = `/${newPathname.join('/')}`;
  return (
    <nav className="flex flex-col border-t sm:border border-skin-base divide-y rounded-none sm:rounded-md overflow-hidden">
      {options.map((item) => {
        const menuPathname = item.slug.split('/').slice(1, 3);
        const menuPath = `/${menuPathname.join('/')}`;

        return (
          <Link
            key={item.slug}
            href={item.slug}
            className={`flex items-center cursor-pointer text-sm lg:text-15px text-skin-base py-3.5 px-3.5 xl:px-4 2xl:px-5 ${
              mainPath === menuPath ? 'bg-skin-two font-medium' : 'font-normal'
            }`}
          >
            <span className="w-9 xl:w-10 flex-shrink-0 flex justify-center">
              {item.icon}
            </span>
            <span className="ps-1.5">{t(item.name)}</span>
          </Link>
        );
      })}
    </nav>
  );
}
