import ChevronRightIcon from '@components/icons/chevron-right';
import ListIcon from '@components/icons/list-icon';
import TruckIcon from '@components/icons/truck-icon';
import Link from '@components/ui/link';
import { useOrderOverviewQuery } from '@framework/order/get-order-overview';
import { ORDER_STATUSES } from '@framework/types';
import { ROUTES } from '@utils/routes';
import { BiPackage } from 'react-icons/bi';
import { IoWalletOutline } from 'react-icons/io5';
import { MdPendingActions } from 'react-icons/md';

interface AccountPurchasesProps {}

const AccountPurchasesSection: React.FC<AccountPurchasesProps> = () => {
  const { data } = useOrderOverviewQuery();

  if (!data) {
    return <></>;
  }

  const pending_count = (
    data.find(({ slug }) => slug === 'pending') ?? { orders_count: 0 }
  ).orders_count;
  const processing_count = (
    data.find(({ slug }) => slug === 'processing') ?? { orders_count: 0 }
  ).orders_count;
  const packed_count = (
    data.find(({ slug }) => slug === 'packed') ?? { orders_count: 0 }
  ).orders_count;
  const deliver_count = (
    data.find(({ slug }) => slug === 'delivery') ?? { orders_count: 0 }
  ).orders_count;

  return (
    <div className="bg-white mb-2 sm:mb-4 lg:mb-5 border-0 sm:border rounded-none sm:rounded">
      <div className="px-2 py-3 border-t border-b sm:border-t-0">
        <div className="flex justify-between">
          <div className="flex items-center gap-2 text-sm sm:text-base">
            <ListIcon className="text-skin-primary w-5 h-5" /> My Purchases
          </div>
          <Link
            href={ROUTES.ORDERS}
            className="flex items-center gap-1 text-xs sm:text-sm sm:text-base"
          >
            View Purchases History{' '}
            <ChevronRightIcon className="w-4 sm:w-[22px] h-4 sm:h-[22px]" />
          </Link>
        </div>
      </div>
      <div className="px-2 py-3 border-b sm:border-0">
        <div className="flex justify-around text-sm sm:text-base py-3">
          <Link
            href={`${ROUTES.ORDERS}?view=${ORDER_STATUSES.PENDING}`}
            className="flex flex-col gap-2 items-center relative"
          >
            <IoWalletOutline className="w-8 sm:w-[42px] h-8 sm:h-[42px]" />
            Pending
            {pending_count > 0 && (
              <span className="order-counter-badge flex items-center justify-center bg-skin-primary text-xs text-skin-inverted absolute -top-2.5 end-1 rounded-full font-bold">
                {pending_count}
              </span>
            )}
          </Link>
          <Link
            href={`${ROUTES.ORDERS}?view=${ORDER_STATUSES.PROCESSING}`}
            className="flex flex-col gap-2 items-center relative"
          >
            <MdPendingActions className="w-8 sm:w-[42px] h-8 sm:h-[42px]" />
            Processing
            {processing_count > 0 && (
              <span className="order-counter-badge flex items-center justify-center bg-skin-primary text-xs text-skin-inverted absolute -top-2.5 end-1 rounded-full font-bold">
                {processing_count}
              </span>
            )}
          </Link>
          <Link
            href={`${ROUTES.ORDERS}?view=${ORDER_STATUSES.PACKED}`}
            className="flex flex-col gap-2 items-center relative"
          >
            <BiPackage className="w-8 sm:w-[42px] h-8 sm:h-[42px]" />
            Packed
            {packed_count > 0 && (
              <span className="order-counter-badge flex items-center justify-center bg-skin-primary text-xs text-skin-inverted absolute -top-2.5 end-1 rounded-full font-bold">
                {packed_count}
              </span>
            )}
          </Link>
          <Link
            href={`${ROUTES.ORDERS}?view=${ORDER_STATUSES.DELIVERY}`}
            className="flex flex-col gap-2 items-center relative"
          >
            <TruckIcon
              className="w-8 sm:w-[42px] h-8 sm:h-[42px]"
              stroke="#595959"
            />
            Delivery
            {deliver_count > 0 && (
              <span className="order-counter-badge flex items-center justify-center bg-skin-primary text-xs text-skin-inverted absolute -top-2.5 end-1 rounded-full font-bold">
                {deliver_count}
              </span>
            )}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AccountPurchasesSection;
