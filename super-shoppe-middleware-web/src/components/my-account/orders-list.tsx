import BackButton from '@components/common/back-button';
import OrderInfiniteList from '@components/order/order-infinite-list';
import { useOrdersQuery } from '@framework/order/get-all-orders';
import { useRouter } from 'next/router';
import { useState } from 'react';

const PAGE_LIMIT = 10;

export default function OrdersListContainer() {
  const { query } = useRouter();
  const [search_value, setSearchValue] = useState('');
  const data = useOrdersQuery({
    limit: PAGE_LIMIT,
    display_id: search_value,
    status_slug: query.view as string,
  });

  function searchSubmit(page: any) {
    setSearchValue(page);
  }

  return (
    <div className="sm:p-0">
      <OrderInfiniteList data={data} onSearchChange={searchSubmit} />
    </div>
  );
}
