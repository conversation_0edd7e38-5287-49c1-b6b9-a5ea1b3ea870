import { Waypoint } from 'react-waypoint';
import ProductCard from '@components/product/product-cards/product-card';
import ProductCardLoader from '@components/ui/loaders/product-card-loader';
import { useRecentProductsQuery } from '@framework/order/get-recent-products';
import NoResult from '@components/common/no-result';
import ProductCardGridLoader from '@components/ui/loaders/product-card-grid-loader';

const RecentlyViewedProductsInfiniteList = () => {
  const {
    isFetching: isLoading,
    isFetchingNextPage: loadingMore,
    fetchNextPage,
    hasNextPage,
    data,
  } = useRecentProductsQuery({
    limit: 20,
  });

  if (!data) return <ProductCardGridLoader />;

  function loadMoreItems() {
    if (hasNextPage) {
      fetchNextPage();
    }
  }

  const children = data.pages.map((page, pageIndex) =>
    page.data.map((product, index) => (
      <ProductCard key={`${pageIndex}-${index}`} product={product} />
    ))
  );

  return (
    <>
      {children.length > 0 && children[0].length > 0 ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-3 xl:grid-cols-4 gap-2 2xl:gap-4 mt-2.5">
          {children}
          {!isLoading && !loadingMore ? (
            <Waypoint onEnter={loadMoreItems} />
          ) : (
            Array.from({ length: 12 }).map((_, idx) => (
              <ProductCardLoader
                key={`order--key-${idx}`}
                uniqueKey={`order--key-${idx}`}
              />
            ))
          )}
        </div>
      ) : (
        <NoResult />
      )}
    </>
  );
};

export default RecentlyViewedProductsInfiniteList;
