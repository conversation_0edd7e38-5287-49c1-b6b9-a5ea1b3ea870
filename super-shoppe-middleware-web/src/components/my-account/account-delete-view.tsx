import {
  useModalAction,
} from '@components/common/modal/modal.context';
import Button from '@components/ui/button';
import PasswordInput from '@components/ui/form/password-input';
import { useDeleteAccountMutation } from '@framework/customer/use-delete-account';
import { useTranslation } from 'next-export-i18n';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

interface FormValues {
  password: string;
}

const AccountDeleteView = () => {
  const { t } = useTranslation();
  const { closeModal } = useModalAction();
  const { mutate: deleteAccount, isLoading } = useDeleteAccountMutation();
  const {
    register,
    watch,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      password: ""
    }
  });

  const password = watch('password');

  function handleDelete(values: FormValues) {
    deleteAccount({
      password: values.password
    }, {
      onSuccess: () => {
        closeModal();
      },
      onError: (error: any) => {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          if(errorData.message) {
            setError("password", { message: errorData.message });
          }
          else {
            Object.keys(errorData).forEach(function eachKey(key: string) {
              // @ts-ignore
              setError(key, {
                message: errorData[key][0],
              });
            });
          }
        }
        else if(typeof errorData === 'string') {
          setError("password", { message: errorData });
        }
        else {
          toast.error("Failed to delete account");
        }
      }
    });
  }
  return (
    <div className="w-full md:w-[508px] mx-auto p-5 sm:p-8 bg-white rounded-xl">
      <div className="w-full h-full">
        <div className="flex h-full flex-col justify-between">
          <p className="text-heading text-xl font-bold mt-4">{t('button-delete-account')}</p>
          <p className="leading-relaxed py-2 mb-2">
            Please contact our customer support to proceed with your account removal.
          </p>
          <p className='text-sm mb-2'>Whatsapp to <a href='tel:**********'>************</a></p>
          <p className='text-sm'>Email to <a href='mailto:<EMAIL>'><EMAIL></a></p>
          {/* <div className="mt-8">
            <PasswordInput
              label={t('forms.label-password')}
              error={errors.password?.message}
              {...register('password', {
                required: `${t('forms.password-required')}`,
              })}
            />
          </div> */}
          <div className="flex items-center justify-end space-s-4 w-full mt-8">
            {/* <div className="w-1/2">
              <Button
                onClick={closeModal}
                variant="border"
                className='w-full py-2 px-4 focus:outline-none text-light hover:underline transition ease-in duration-200 text-center text-base font-semibold rounded shadow-md'
              >
                {t('button-cancel')}
              </Button>
            </div> */}

            <div className="w-1/2">
              <Button
                onClick={closeModal}
                // loading={isLoading}
                // disabled={isLoading || password.length <= 0}
                variant="border"
                className='w-full py-2 px-4 focus:outline-none text-light transition ease-in duration-200 text-center text-base font-semibold rounded shadow-md'
              >
                {t('button-dismiss')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountDeleteView;
