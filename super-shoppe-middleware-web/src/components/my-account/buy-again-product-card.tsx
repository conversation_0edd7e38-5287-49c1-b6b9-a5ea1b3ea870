import ProductCard from '@components/product/product-cards/product-card';
import { Product } from '@framework/types';

interface BuyAgainProductCardProps {
  product: Product & { orders_count: number };
}

const BuyAgainProductCard: React.FC<BuyAgainProductCardProps> = ({
  product,
}) => {
  return (
    <ProductCard product={product}>
      {product.orders_count > 0 && (
        <div className="text-xs md:text-sm px-3 md:px-4 lg:px-[18px] py-1.5">
          Bought {product.orders_count} time
          {product.orders_count > 1 ? 's' : ''}
        </div>
      )}
    </ProductCard>
  );
};

export default BuyAgainProductCard;
