import Modal from '@components/common/modal/modal';
import dynamic from 'next/dynamic';
import {
  useModalAction,
  useModalState,
} from '@components/common/modal/modal.context';
const ForgetPassword = dynamic(
  () => import('@components/auth/forget-password/forget-password')
);
const VerifyEmailView = dynamic(
  () => import('@components/auth/sign-up/verify-email-view')
);
const VerifyPhoneView = dynamic(
  () => import('@components/auth/sign-up/verify-phone-view')
);
const VerifyAccountView = dynamic(
  () => import('@components/auth/verify-account-view')
);
const ProductPopup = dynamic(() => import('@components/product/product-popup'));
const SimilarProductsPopup = dynamic(
  () => import('@components/product/similar-products-popup')
);
const AddressPopup = dynamic(
  () => import('@components/common/form/address-form')
);
const AddressDeleteView = dynamic(
  () => import('@components/address/address-delete-view')
);
const AccountDeleteView = dynamic(
  () => import('@components/my-account/account-delete-view')
);
const PaymentPopup = dynamic(
  () => import('@components/common/form/add-payment')
);
const PhoneNumberPopup = dynamic(
  () => import('@components/common/form/add-contact')
);
const CategoryPopup = dynamic(
  () => import('@components/category/category-popup')
);
const VoucherPopup = dynamic(() => import('@components/voucher/voucher-popup'));
const VoucherQRCodePopup = dynamic(
  () => import('@components/voucher/voucher-qr-code-popup')
);
const InvalidCheckoutPopup = dynamic(
  () => import('@components/checkout/checkout-popup')
);
const CancelOrder = dynamic(
  () => import('@components/order/order-cancel-modal')
);
const PopupBanner = dynamic(() => import('@components/banner/popup-banner'));
const WithdrawCancelConfirm = dynamic(
  () => import('@components/order/withdraw-cancel-confirm')
);
const StoreFilterView = dynamic(
  () => import('@components/stores/store-filter-view')
);
const ManagedModal: React.FC = () => {
  const { isOpen, view, closeOnOutsideClick } = useModalState();
  const { closeModal } = useModalAction();

  if (view === 'CATEGORY_VIEW') {
    return (
      <Modal
        open={isOpen}
        onClose={closeOnOutsideClick ? closeModal : () => {}}
        variant="bottom"
      >
        {view === 'CATEGORY_VIEW' && <CategoryPopup />}
      </Modal>
    );
  }
  return (
    <Modal open={isOpen} onClose={closeOnOutsideClick ? closeModal : () => {}}>
      {view === 'FORGET_PASSWORD' && <ForgetPassword />}
      {view === 'VERIFY_EMAIL_VIEW' && <VerifyEmailView />}
      {view === 'VERIFY_PHONE_VIEW' && <VerifyPhoneView />}
      {view === 'VERIFY_ACCOUNT_VIEW' && <VerifyAccountView />}
      {view === 'PRODUCT_VIEW' && <ProductPopup />}
      {view === 'VOUCHER_VIEW' && <VoucherPopup />}
      {view === 'VOUCHER_QR_CODE_VIEW' && <VoucherQRCodePopup />}
      {view === 'SIMILAR_PRODUCTS_VIEW' && <SimilarProductsPopup />}
      {view === 'ADDRESS_VIEW_AND_EDIT' && <AddressPopup />}
      {view === 'DELETE_ADDRESS' && <AddressDeleteView />}
      {view === 'DELETE_ACCOUNT' && <AccountDeleteView />}
      {view === 'PAYMENT' && <PaymentPopup />}
      {view === 'PHONE_NUMBER' && <PhoneNumberPopup />}
      {view === 'INVALID_CHECKOUT' && <InvalidCheckoutPopup />}
      {view === 'CANCEL_ORDER' && <CancelOrder />}
      {view === 'POPUP_BANNER_VIEW' && <PopupBanner />}
      {view === 'WITHDRAW_CANCEL_CONFIRM' && <WithdrawCancelConfirm />}
      {view === 'STORE_FILTER_VIEW' && <StoreFilterView />}
    </Modal>
  );
};

export default ManagedModal;
