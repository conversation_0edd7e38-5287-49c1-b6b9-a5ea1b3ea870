import React from 'react';

type MODAL_VIEWS =
  | 'SIGN_UP_VIEW'
  | 'LOGIN_VIEW'
  | 'FORGET_PASSWORD'
  | 'VERIFY_EMAIL_VIEW'
  | 'VERIFY_PHONE_VIEW'
  | 'VERIFY_ACCOUNT_VIEW'
  | 'PAYMENT'
  | 'ADDRESS_VIEW_AND_EDIT'
  | 'DELETE_ADDRESS'
  | 'PHONE_NUMBER'
  | 'DELIVERY_VIEW'
  | 'PRODUCT_VIEW'
  | 'SIMILAR_PRODUCTS_VIEW'
  | 'CATEGORY_VIEW'
  | 'VOUCHER_VIEW'
  | 'VOUCHER_QR_CODE_VIEW'
  | 'INVALID_CHECKOUT'
  | 'CANCEL_ORDER'
  | 'DELETE_ACCOUNT'
  | 'POPUP_BANNER_VIEW'
  | 'WITHDRAW_CANCEL_CONFIRM'
  | 'STORE_FILTER_VIEW';

interface State {
  view?: MODAL_VIEWS;
  data?: any;
  isOpen: boolean;
  closeOnOutsideClick: boolean;
  onConfirm?: any;
  onCancel?: any;
}
type OpenOptions = {
  confirmCallback?: any;
  cancelCallback?: any;
};
type Action =
  | {
      type: 'open';
      view?: MODAL_VIEWS;
      payload?: any;
      closeOnOutsideClick: boolean;
      options?: OpenOptions;
    }
  | { type: 'close' };

const initialState: State = {
  view: undefined,
  isOpen: false,
  data: null,
  closeOnOutsideClick: true,
  onConfirm: null,
  onCancel: null,
};

function modalReducer(state: State, action: Action): State {
  switch (action.type) {
    case 'open':
      return {
        ...state,
        view: action.view,
        data: action.payload,
        isOpen: true,
        closeOnOutsideClick: action.closeOnOutsideClick,
        onConfirm: action.options?.confirmCallback,
        onCancel: action.options?.cancelCallback,
      };
    case 'close':
      return {
        ...state,
        view: undefined,
        data: null,
        isOpen: false,
      };
    default:
      throw new Error('Unknown Modal Action!');
  }
}

const ModalStateContext = React.createContext<State>(initialState);
ModalStateContext.displayName = 'ModalStateContext';
const ModalActionContext = React.createContext<
  React.Dispatch<Action> | undefined
>(undefined);
ModalActionContext.displayName = 'ModalActionContext';

export const ModalProvider: React.FC = ({ children }) => {
  const [state, dispatch] = React.useReducer(modalReducer, initialState);
  return (
    <ModalStateContext.Provider value={state}>
      <ModalActionContext.Provider value={dispatch}>
        {children}
      </ModalActionContext.Provider>
    </ModalStateContext.Provider>
  );
};

export function useModalState() {
  const context = React.useContext(ModalStateContext);
  if (context === undefined) {
    throw new Error(`useModalState must be used within a ModalProvider`);
  }
  return context;
}

export function useModalAction() {
  const dispatch = React.useContext(ModalActionContext);
  if (dispatch === undefined) {
    throw new Error(`useModalAction must be used within a ModalProvider`);
  }
  return {
    openModal(
      view?: MODAL_VIEWS,
      payload?: unknown,
      closeOnOutsideClick: boolean = true,
      options?: OpenOptions
    ) {
      dispatch({ type: 'open', view, payload, closeOnOutsideClick, options });
    },
    closeModal() {
      dispatch({ type: 'close' });
    },
  };
}
