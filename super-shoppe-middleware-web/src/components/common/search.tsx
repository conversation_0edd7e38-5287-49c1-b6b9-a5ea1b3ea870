import React, { useCallback, useState } from 'react';
import cn from 'classnames';
import SearchBox from '@components/common/search-box';
import SearchProduct from '@components/common/search-product';
import SearchResultLoader from '@components/ui/loaders/search-result-loader';
import useFreezeBodyScroll from '@utils/use-freeze-body-scroll';
import Scrollbar from '@components/ui/scrollbar';
import { useUI } from '@contexts/ui.context';
import debounce from 'lodash/debounce';
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { ROUTES } from '@utils/routes';
import useWindowSize from '@utils/use-window-size';
import { useTranslation } from 'next-export-i18n';
import { searchProducts } from '@framework/product/search-products';
import { SearchTerm } from '@framework/types';
import SearchProductTerm from './search-product-term';

type Props = {
  className?: string;
  searchId?: string;
  variant?: 'border' | 'fill';
};

const Search = React.forwardRef<HTMLDivElement, Props>(
  (
    {
      className = 'md:w-[730px] 2xl:w-[800px]',
      searchId = 'search',
      variant = 'border',
    },
    ref
  ) => {
    const {
      displayMobileSearch,
      closeMobileSearch,
      displaySearch,
      closeSearch,
    } = useUI();
    const router = useRouter();
    const { query } = router;
    const { width } = useWindowSize();
    const { t } = useTranslation();
    const searchInput = React.useRef<HTMLInputElement>(null);
    const [searchText, setSearchText] = useState('');
    const [inputFocus, setInputFocus] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [data, setProductsData] = useState<any[]>([]);
    const [searchTermsData, setSearchTermsData] = useState<SearchTerm[]>([]);
    const handleTextSearch = useCallback(
      debounce(async (search) => {
        setIsLoading(true);
        const result = await searchProducts({
          text: search,
          limit: 10,
        });
        setProductsData(result.data);
        setSearchTermsData(result.terms);
        setIsLoading(false);
      }, 600),
      []
    );
    useEffect(() => {
      if (query.text != undefined) {
        setSearchText(decodeURIComponent(query.text as string));
      }
    }, [query.text]);
    // useEffect(() => {
    //   if (searchText && searchText.length > 1) {
    //     handleTextSearch(searchText);
    //   }
    // }, [searchText]);
    // useFreezeBodyScroll(
    //   inputFocus === true || displaySearch || displayMobileSearch
    // );
    function handleSearch(e: React.SyntheticEvent) {
      e.preventDefault();
      router.push(`${ROUTES.SEARCH}?text=${encodeURIComponent(searchText)}`);
      setInputFocus(false);
      closeMobileSearch();
      closeSearch();
    }
    function handleAutoSearch(e: React.FormEvent<HTMLInputElement>) {
      setSearchText(e.currentTarget.value);
    }
    // function productSelected() {
    //   setSearchText('');
    //   setInputFocus(false);
    //   closeMobileSearch();
    //   closeSearch();
    // }
    function clear() {
      setSearchText('');
    }
    function closeOverlay() {
      setInputFocus(false);
      closeMobileSearch();
      closeSearch();
    }
    function enableInputFocus() {
      setInputFocus(true);
    }
    // function termClicked(text: string) {
    //   setSearchText(text);
    //   searchInput.current?.focus();
    // }

    return (
      <div
        ref={ref}
        className={cn(
          'w-full transition-all duration-200 ease-in-out',
          className,
          // {
          //   'absolute start-0 top-0 md:relative': inputFocus === true,
          // }
        )}
      >
        {/* <div
          className={cn('overlay cursor-pointer', {
            open: displayMobileSearch,
            'input-focus-overlay-open': inputFocus === true,
            'open-search-overlay': displaySearch,
          })}
          onClick={() => closeOverlay()}
        /> */}

        <div className="w-full flex flex-col justify-center flex-shrink-0 relative z-30">
          <div className="flex flex-col mx-auto w-full">
            <SearchBox
              searchId={searchId}
              name="search"
              placeholder="Search for brands and products"
              value={searchText}
              onSubmit={handleSearch}
              onChange={handleAutoSearch}
              onClear={clear}
              onFocus={() => enableInputFocus()}
              variant={variant}
              ref={searchInput}
            />
          </div>
          {/* End of searchbox */}

          {/* {searchText && inputFocus && (isLoading || (data && data.length)) ? (
            <div className="w-full absolute top-[56px] start-0 py-2.5 bg-skin-fill rounded-md flex flex-col overflow-hidden shadow-dropDown z-30">
              <Scrollbar className="os-host-flexbox">
                <div className="w-full h-[200px] md:h-[380px]">
                  {isLoading
                    ? Array.from({ length: 15 }).map((_, idx) => (
                        <div
                          key={`search-result-loader-key-${idx}`}
                          className="py-2.5 ps-5 pe-10 scroll-snap-align-start"
                        >
                          <SearchResultLoader
                            key={idx}
                            uniqueKey={`top-search-${idx}`}
                          />
                        </div>
                      ))
                    : data?.map((item, index) => (
                        <div
                          key={`search-result-key-${index}`}
                          className="py-2.5 ps-5 pe-10 scroll-snap-align-start transition-colors duration-200 hover:bg-skin-two"
                          onClick={productSelected}
                        >
                          <SearchProduct item={item} key={index} />
                        </div>
                      ))}
                </div>
              </Scrollbar>
              {!isLoading && searchTermsData && searchTermsData.length > 0 && (
                <div className="flex gap-2 pb-2.5 pt-5 px-5 border-t">
                  {searchTermsData?.map((item, index) => (
                    <SearchProductTerm
                      item={item}
                      onClick={termClicked}
                      key={index}
                    />
                  ))}
                </div>
              )}
            </div>
          ) : (
            <></>
          )} */}
        </div>
      </div>
    );
  }
);

export default Search;
