import { ROUTES } from '@utils/routes';
import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { UseQueryResult } from 'react-query';

export interface TNGCallbackProps {
  result: UseQueryResult<any, Error>;
}

const TNGCallback = ({ result }: TNGCallbackProps) => {
  const router = useRouter();
  const fetch_count = useRef(0);
  const { data, isRefetching, refetch } = result;

  useEffect(() => {
    if (!isRefetching && data) {
      if (data.status == 'pending' && fetch_count.current < 12) {
        setTimeout(() => {
          fetch_count.current++;
          refetch();
        }, 5000);
      } else {
        router.replace(ROUTES.CHECKOUT_RESULT);
      }
    }
  }, [data, isRefetching]);

  return <></>;
};

export default TNGCallback;
