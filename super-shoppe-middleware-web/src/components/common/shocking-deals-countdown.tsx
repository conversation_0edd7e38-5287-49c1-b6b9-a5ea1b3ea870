import { useRef, useState } from 'react';
import { useEffect } from 'react';
import Countdown from 'react-countdown';

type SaleCountdownProps = {
  className?: string;
  initialDate: number;
  onComplete?: () => void;
};

const SaleCountdown: React.FC<SaleCountdownProps> = ({
  className,
  initialDate,
  onComplete,
}) => {
  const sale_date = useRef(initialDate);

  // useEffect(() => {
  //     console.log(initialDate);
  // }, [initialDate]);

  function renderCountdown({ formatted }: any) {
    const { hours, minutes, seconds } = formatted;
    return (
      <>
        <span className="rounded bg-black text-white text-sm font-mono px-1 me-1">
          {hours}
        </span>
        <span className="rounded bg-black text-white text-sm font-mono px-1 me-1">
          {minutes}
        </span>
        <span className="rounded bg-black text-white text-sm font-mono px-1">
          {seconds}
        </span>
      </>
    );
  }

  return (
    <div className={className}>
      <Countdown
        date={sale_date.current}
        precision={0}
        intervalDelay={1000}
        zeroPadTime={2}
        onComplete={onComplete}
        renderer={renderCountdown}
      />
    </div>
  );
};

export default SaleCountdown;
