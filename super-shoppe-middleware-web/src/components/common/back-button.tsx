import cn from 'classnames';
import { useRouter } from 'next/router';
import { IoIosArrowBack } from 'react-icons/io';

interface BackButtonProps {
  className?: string;
  customColorClassName?: string;
  text?: string;
}

const BackButton: React.FC<BackButtonProps> = ({
  className = "px-2 me-6",
  customColorClassName = "text-skin-primary",
  text = null
}) => {
  const router = useRouter();
  return (
    <div
      onClick={() => router.back()}
      className={`flex items-center cursor-pointer ${className}`}
    >
      <IoIosArrowBack className={cn(customColorClassName, "w-6 h-6")} />
      {text && <span className={cn(customColorClassName, "ms-2")}>{text}</span>}
    </div>
  );
};

export default BackButton;
