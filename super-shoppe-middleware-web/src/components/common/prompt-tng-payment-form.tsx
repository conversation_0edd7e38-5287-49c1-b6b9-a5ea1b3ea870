import { useCheckout } from '@contexts/checkout.context';
import { fetchOrderPaymentCancel } from '@framework/order/use-order-payment.query';
import { API_ENDPOINTS } from '@framework/utils/api-endpoints';
import { ROUTES } from '@utils/routes';
import { useTNGMy } from '@utils/use-tng';
import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { useQueryClient } from 'react-query';

export interface PromptTNGPaymentProps {
  redirect_url: string;
}

const PromptTNGPayment = ({ redirect_url }: PromptTNGPaymentProps) => {
  const router = useRouter();
  const is_posted = useRef(false);
  const tngmy = useTNGMy();
  const { current_order_id } = useCheckout();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (redirect_url && !is_posted.current) {
      queryClient.invalidateQueries(API_ENDPOINTS.CART);
      tngmy.postMessage({
        type: 'SS_PAYMENT',
        redirect: redirect_url,
      });

      is_posted.current = true;

      tngmy.onMessage = function (e: any) {
        if (e.type === 'SS_PAYMENT_COMPLETE') {
          if (
            e.resultCode === '6000' ||
            e.resultCode === '9000' ||
            e.resultCode === '8000' ||
            e.resultCode === '6004'
          ) {
            router.replace(ROUTES.CHECKOUT_CALLBACK);
          } else {
            // cancel payment or payment failed
            fetchOrderPaymentCancel(current_order_id)
              .then(() => {
                router.replace(ROUTES.CHECKOUT_CALLBACK);
              })
              .catch(() => {
                router.replace(ROUTES.CHECKOUT_CALLBACK);
              });
          }
        }
      };
    }
  }, [redirect_url]);

  return <></>;
};

export default PromptTNGPayment;
