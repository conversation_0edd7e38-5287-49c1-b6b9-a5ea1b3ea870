import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';
import React, { useEffect, useState } from 'react';
import ReactCopyToClipboard from 'react-copy-to-clipboard';

const CopyToClipboard: React.FC<{
  text: string;
  absolute?: boolean;
  textClassName?: string;
  fillClassName?: string;
  className?: string;
}> = ({
  text,
  absolute = true,
  textClassName = 'text-skin-primary',
  fillClassName = 'bg-skin-fill',
  className = -'end-0.5 top-[6%] h-[90%] pe-1.5 ps-8',
}) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (copied) {
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  }, [copied]);

  return (
    <>
      {!copied ? (
        <ReactCopyToClipboard text={text} onCopy={() => setCopied(true)}>
          <span
            className={cn(
              'end-0.5 top-[6%] h-[90%] px-2 text-sm uppercase font-bold flex items-center cursor-pointer',
              textClassName,
              fillClassName,
              { absolute: absolute },
              className
            )}
            role="button"
          >
            {t('text-copy')}
          </span>
        </ReactCopyToClipboard>
      ) : (
        <span
          className={cn(
            'end-0.5 top-[6%] h-[90%] px-2 text-sm uppercase font-bold flex items-center cursor-pointer',
            textClassName,
            fillClassName,
            { absolute: absolute },
            className
          )}
        >
          {t('text-copied')}
        </span>
      )}
    </>
  );
};

export default CopyToClipboard;
