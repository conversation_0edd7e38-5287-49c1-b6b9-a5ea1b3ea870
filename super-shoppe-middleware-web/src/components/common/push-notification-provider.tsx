import React, { createContext, useContext, useEffect, useState } from 'react';
import { PushNotifications } from '@capacitor/push-notifications';
import { Capacitor } from '@capacitor/core';
import { useSaveFcmMutation } from '@framework/customer/use-save-fcm.mutation';
import { useCustomer } from '@contexts/customer.context';
import { useRouter } from 'next/router';

const PushNotificationContext = createContext({});

const PushNotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const { mutate } = useSaveFcmMutation();
  const { customer } = useCustomer();
  const router = useRouter();
  useEffect(() => {
    const registerPushNotifications = async () => {
      if (Capacitor.isNativePlatform()) {
        let permission = await PushNotifications.checkPermissions();
        if (permission.receive === 'prompt') {
          permission = await PushNotifications.requestPermissions();
        }

        if (permission.receive === 'granted' && customer) {
          await PushNotifications.register();

          PushNotifications.addListener('registration', async (token) => {
            // Save the token to the server if the customer is logged in
            if (customer) {
              mutate(token.value);
            }
          });
        }
      }
    };

    registerPushNotifications();

    // Handle received notifications
    if (Capacitor.isNativePlatform()) {
      PushNotifications.addListener(
        'pushNotificationReceived',
        (notification) => {
          setNotifications((prev) => [...prev, notification]);
        }
      );
    }

    // Handle notification actions
    if (Capacitor.isNativePlatform()) {
      PushNotifications.addListener(
        'pushNotificationActionPerformed',
        (notification) => {
          if (notification.notification.data) {
            router.push(goToPage(notification.notification.data));
          }
        }
      );
    }

    return () => {
      if (Capacitor.isNativePlatform()) {
        PushNotifications.removeAllListeners();
      }
    };
  }, [customer]);

  return (
    <PushNotificationContext.Provider value={{ notifications }}>
      {children}
    </PushNotificationContext.Provider>
  );
};

const usePushNotifications = () => {
  return useContext(PushNotificationContext);
};

type NotificationData = {
  action?: string;
  action_data?: string;
};

const goToPage = (data: NotificationData) => {
  let path = '/';
  switch (data.action) {
    case 'order-placed':
    case 'order-updated':
      path = `/my-account/orders/view?order_id=${data.action_data}`;
      break;
    case 'offline-transaction-reward':
    case 'offline-transaction-redeem':
    case 'points-expiring':
      path = '/my-account/coin';
      break;
    default:
      break;
  }
  return path;
};

export { PushNotificationProvider, usePushNotifications };
