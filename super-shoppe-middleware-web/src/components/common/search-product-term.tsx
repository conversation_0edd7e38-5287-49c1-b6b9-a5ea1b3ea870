import { SearchTerm } from '@framework/types';
import { decodeSearchString } from '@framework/utils/data-mappers';

type SearchProductTermProps = {
  item: SearchTerm;
  onClick: any;
};

const SearchProductTerm: React.FC<SearchProductTermProps> = ({
  item,
  onClick,
}) => {
  return (
    <div
      onClick={() => onClick(item.search_text)}
      className="h-auto text-skin-base rounded-full border border-skin-primary flex justify-start items-center px-2 py-1 hover:text-white hover:bg-skin-primary cursor-pointer"
    >
      <h3 className="truncate text-15px pe-2">
        {decodeSearchString(item.search_text)}
      </h3>
      <h3 className="truncate text-15px">{item.last_result_count}</h3>
    </div>
  );
};

export default SearchProductTerm;
