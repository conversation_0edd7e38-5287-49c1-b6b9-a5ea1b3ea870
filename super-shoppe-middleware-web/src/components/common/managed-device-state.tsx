import { App } from "@capacitor/app";
import { Toast } from "@capacitor/toast";
import { useEffect, useRef } from "react";

const ManagedDeviceState = () => {
    const isPriorExit = useRef(false);

    useEffect(() => {
        App.addListener('backButton', ({ canGoBack }) => {
          if(canGoBack) {
            window.history.back();
          }
          else {
            if(isPriorExit.current) {
              App.removeAllListeners();
              App.exitApp();
            }
            else {
              Toast.show({
                text: 'Press again to exit',
                duration: 'short'
              });
              isPriorExit.current = true;
              setTimeout(() => {
                isPriorExit.current = false;
              }, 2000);
            }
          }
        });
    
        App.addListener('appStateChange', ({ }) => {
          if(isPriorExit.current) {
            isPriorExit.current = false;
          }
        });
    }, []);

    return (
        <></>
    );
}

export default ManagedDeviceState;