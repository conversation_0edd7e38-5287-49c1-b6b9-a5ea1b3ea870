import { useTranslation } from 'next-export-i18n';
import { useEffect, useState } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { toast } from 'react-toastify';

interface CopyTextProps {
  className?: string;
  text: string;
}

const CopyText: React.FC<CopyTextProps> = ({ className, text }) => {
  const { t } = useTranslation();
  return (
    <CopyToClipboard
      text={text}
      onCopy={() => toast.success('Copied successfully')}
    >
      <span
        className="px-2 text-skin-primary text-sm uppercase font-bold flex items-center bg-skin-fill cursor-pointer"
        role="button"
      >
        {t('text-copy')}
      </span>
    </CopyToClipboard>
  );
};

export default CopyText;
