import React from 'react';
import Countdown, { zeroPad, CountdownProps } from 'react-countdown';

type QRCodeCountdownProps = {
} & CountdownProps;

const renderer = ({ minutes, seconds, completed }: any) => {
  if (completed) {
    return null;
  } else {
    return (
      <span className="flex items-center text-skin-primary font-semibold">
        <span className="flex items-center justify-center min-w-[30px] md:min-w-[37px] min-h-[30px] bg-skin-fill rounded p-1 mx-1 md:mx-1.5">
          {zeroPad(minutes)}
        </span>
        :
        <span className="flex items-center justify-center min-w-[30px] md:min-w-[37px] min-h-[30px] bg-skin-fill rounded p-1 mx-1 md:mx-1.5">
          {zeroPad(seconds)}
        </span>
      </span>
    );
  }
};

const QRCodeCountdown: React.FC<QRCodeCountdownProps> = ({ ...props }) => {
  return <Countdown renderer={renderer} {...props} />;
};

export default QRCodeCountdown;
