import Link from '@components/ui/link';
import { searchProductPlaceholder } from '@assets/placeholders';
import { generateProductLinkHref } from '@framework/utils/data-mappers';

type SearchProductProps = {
  item: any;
};

const SearchProduct: React.FC<SearchProductProps> = ({ item }) => {
  return (
    <Link
      href={generateProductLinkHref(item)}
      className="group w-full h-auto flex justify-start items-center"
    >
      <div className="relative flex w-12 h-12 rounded-md overflow-hidden flex-shrink-0 cursor-pointer me-4">
        <img
          src={item?.image?.thumbnail ?? searchProductPlaceholder.src}
          width={48}
          height={48}
          loading="eager"
          alt={item.name || 'Product Image'}
          className="bg-skin-thumbnail object-cover"
        />
        {(item.banner && item.banner?.original) ? (
          <img
            src={item.banner?.original}
            alt={item.name || 'Product Image'}
            className="absolute w-full h-full start-0 bottom-0"
          />
        ) : <></>}
      </div>
      <div className="flex flex-col w-full overflow-hidden">
        <h3 className="truncate text-skin-base text-15px">{item.name}</h3>
      </div>
    </Link>
  );
};

export default SearchProduct;
