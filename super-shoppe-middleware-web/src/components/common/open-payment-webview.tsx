import { ROUTES } from '@utils/routes';
import { useEffect } from 'react';
import { Browser } from '@capacitor/browser';
import { encodeSearchString } from '@framework/utils/data-mappers';
import { useRouter } from 'next/router';
import { useCheckout } from '@contexts/checkout.context';
import { fetchOrderPaymentCancel } from '@framework/order/use-order-payment.query';

export interface OpenPaymentWebviewProps {
  url: string;
  data: object;
  method: string;
  form_submit: boolean;
}

const OpenPaymentWebview: React.FC<OpenPaymentWebviewProps> = ({ url, data, method, form_submit }) => {
  const router = useRouter();
  const { current_order_id } = useCheckout();

  useEffect(() => {
    const openWebview = async () => {
      const webviewURL = `${process.env.NEXT_PUBLIC_SITE_URL!}${ROUTES.CHECKOUT_WEB_PAYMENT}?pay_url=${encodeSearchString(url)}&pay_data=${JSON.stringify(data)}&pay_method=${method}&form=${form_submit ? "1" : "0"}`;
      try {
        if(cordova) {
          var inappBrowser = cordova.InAppBrowser.open(encodeURI(webviewURL), '_blank', 'location=yes,transitionstyle=crossdissolve,hidespinner=yes,toolbarposition=top,closebuttoncaption=Close');
          inappBrowser.addEventListener('loadstop', (e: any) => {
            if(e && e.url && e.url.indexOf(ROUTES.CHECKOUT_CALLBACK) > -1) {
              inappBrowser.close();
              fetchOrderPaymentCancel(current_order_id)
                .then(() => {
                  router.replace(ROUTES.CHECKOUT_RESULT);
                })
                .catch(() => {
                  router.replace(ROUTES.CHECKOUT_RESULT);
                });
            }
          });
          inappBrowser.addEventListener('exit', () => {
            fetchOrderPaymentCancel(current_order_id)
              .then(() => {
                router.replace(ROUTES.CHECKOUT_RESULT);
              })
              .catch(() => {
                router.replace(ROUTES.CHECKOUT_RESULT);
              });
          });
        }
      }
      catch(e) {
        var win = window.open(webviewURL);
        var timer = setInterval(function () {
          if (win?.closed) {
            clearInterval(timer);
            fetchOrderPaymentCancel(current_order_id)
              .then(() => {
                router.replace(ROUTES.CHECKOUT_RESULT);
              })
              .catch(() => {
                router.replace(ROUTES.CHECKOUT_RESULT);
              });
          }
        }, 1000);
      }
    };
    openWebview();
  }, []);
  return <></>;
};

export default OpenPaymentWebview;  
