import { useSettings } from '@contexts/settings.context';
import usePrice from '@framework/product/use-price';

const PriceRenderer: React.FC<{ price: number }> = ({ price }) => {
  const { currency } = useSettings();
  const { price: _price } = usePrice({
    amount:
      typeof price === 'string'
        ? isNaN(parseFloat(price))
          ? 0
          : parseFloat(price)
        : price,
    currencyCode: currency,
  });
  return <>{_price}</>;
};

export default PriceRenderer;
