import Input from '@components/ui/form/input';
import Button from '@components/ui/button';
import TextArea from '@components/ui/form/text-area';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'next-export-i18n';
import PhoneNumberInput from '@components/ui/phone-number-input';

interface ContactFormValues {
  name: string;
  email: string;
  phone: string;
  message: string;
}

const ContactForm: React.FC = () => {
  const {
    register,
    control,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm<ContactFormValues>();

  function onSubmit(values: ContactFormValues) {
    console.log(values, 'Contact');
  }

  const { t } = useTranslation();

  return (
    <form onSubmit={handleSubmit(onSubmit)} noValidate className="space-y-5">
      <Input
        variant="solid"
        label={t('forms.label-name-required')}
        placeholder={t('forms.placeholder-name')}
        {...register('name', { required: t('forms.name-required') })}
        error={errors.name?.message}
      />
      <Input
        type="email"
        variant="solid"
        label={t('forms.label-email-required')}
        placeholder={t('forms.placeholder-email')}
        {...register('email', {
          required: t('forms.email-required'),
          pattern: {
            value:
              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            message: t('forms.email-error'),
          },
        })}
        error={errors.email?.message}
      />
      <PhoneNumberInput
        {...register('phone', {
          required: t('forms.phone-required'),
          pattern: {
            value: /^(\+?6?01)[02-46-9][-]?[\s]?[0-9]{3}[\s]?[0-9]{4}$|^(\+?6?01)[1][-]?[\s]?[0-9]{4}[\s]?[0-9]{4}$|^(\+?6?03)[-]?[\s]?[0-9]{4}[\s]?[0-9]{4}$/,
            message: t('forms.phone-error'),
          },
        })}
        control={control}
        label={t('forms.label-contact-phone')}
        placeholder={t('forms.placeholder-phone')}
        variant="outline"
        error={errors.phone?.message}
        autoComplete="tel"
        labelClassName="text-skin-muted"
        onChange={(e: any) => {
          //@ts-ignore
          setValue('phone', e.target.value);
        }}
      />
      <TextArea
        variant="solid"
        label={t('forms.label-message')}
        {...register('message')}
        placeholder={t('forms.placeholder-briefly-describe')}
      />
      <Button variant="formButton" className="w-full" type="submit">
        {t('button-send-message')}
      </Button>
    </form>
  );
};

export default ContactForm;
