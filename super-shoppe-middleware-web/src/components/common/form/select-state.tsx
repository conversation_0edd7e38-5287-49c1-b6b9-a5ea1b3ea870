import { Control, useFormContext } from 'react-hook-form';
import { useTranslation } from 'next-export-i18n';
// import { useSaveAddressMutation } from "@data/customer/use-address.mutation";
import SelectInput from '@components/ui/select-input';
import { useStatesQuery } from '@framework/states/use-states';
import { ImSpinner2 } from 'react-icons/im';

export default function SelectState({
  keyName,
  error,
}: {
  keyName: string;
  error: any;
}) {
  const { data: states, isLoading } = useStatesQuery();
  const { t } = useTranslation();
  const { control, setValue } = useFormContext();
  if (isLoading || !states) {
    return (
      <div className="p-4 sm:p-8">
        <ImSpinner2 className="animate-spin h-5 w-5 text-skin-primary" />
      </div>
    );
  }
  return (
    <div>
      <label
        htmlFor={keyName}
        className='block font-normal text-sm leading-none mb-3 cursor-pointer text-skin-base text-opacity-70'
      >
        {t('forms.label-state')}
      </label>
      <SelectInput
        name={keyName}
        control={control}
        onChange={(selected: any) => setValue(keyName, selected)}
        isMulti={false}
        isLoading={false}
        isClearable={false}
        getOptionLabel={(option: any) => option.name}
        getOptionValue={(option: any) => option.id}
        options={states.map(({ id, name }) => ({ id, name }))}
        selectProps={{
          menuPlacement: 'auto',
          maxMenuHeight: 160,
          captureMenuScroll: false,
        }}
      />
      {error && <p className="my-2 text-13px text-skin-red">{error}</p>}
    </div>
  );
}
