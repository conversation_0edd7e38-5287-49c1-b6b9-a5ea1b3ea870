import Button from '@components/ui/button';
import Input from '@components/ui/input';
import Label from '@components/ui/label';
import {
  Control,
  FormProvider,
  useForm,
  useFormContext,
} from 'react-hook-form';
import { useTranslation } from 'next-export-i18n';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Checkbox from '@components/ui/checkbox/checkbox';
import { toast } from 'react-toastify';
// import { useSaveAddressMutation } from "@data/customer/use-address.mutation";
import SelectInput from '@components/ui/select-input';
import PhoneNumberInput from '@components/ui/phone-number-input';
import { useCheckout } from '@contexts/checkout.context';
import TextArea from '@components/ui/form/text-area';
import { useModalAction, useModalState } from '../modal/modal.context';
import { useSaveAddressMutation } from '@framework/customer/use-address.mutation';
import CloseButton from '@components/ui/close-button';
import { useStatesQuery } from '@framework/states/use-states';
import { ImSpinner2 } from 'react-icons/im';
import { useCustomer } from '@contexts/customer.context';
import { RadioGroup } from '@headlessui/react';
import { useCart } from '@contexts/cart/cart.context';
import { remapEmail, remapPhoneNumber } from '@framework/utils/data-mappers';

const TITLE_OPTIONS = ['Home', 'Office'];

function SelectState({
  control,
  setValue,
  error,
}: {
  control: Control<FormValues>;
  setValue: any;
  error: any;
}) {
  const { data: states, isLoading } = useStatesQuery();
  const { t } = useTranslation();
  if (isLoading || !states) {
    return (
      <div className="p-4 sm:p-8">
        <ImSpinner2 className="animate-spin h-5 w-5 text-skin-primary" />
      </div>
    );
  }
  return (
    <div>
      <Label>{t('forms.label-state')} *</Label>
      <SelectInput
        name="state"
        control={control}
        isMulti={false}
        isLoading={false}
        isClearable={false}
        onChange={(selected: any) => setValue('state', selected)}
        getOptionLabel={(option: any) => option.name}
        getOptionValue={(option: any) => option.id}
        options={states.map(({ id, name }) => ({ id, name }))}
        selectProps={{
          menuPlacement: 'auto',
          maxMenuHeight: 120,
          captureMenuScroll: false,
        }}
      />
      {error && <p className="my-2 text-13px text-skin-red">{error}</p>}
    </div>
  );
}

function SelectTitle({ error }: { error: any }) {
  const { t } = useTranslation();
  const { setValue, watch } = useFormContext();
  const title = watch('title');
  function handleChange(e: any) {
    setValue('title', e);
  }
  return (
    <>
      <RadioGroup id="title" name="title" value={title} onChange={handleChange}>
        <RadioGroup.Label>{t('forms.label-title')}</RadioGroup.Label>
        <div className="grid gap-4 grid-cols-2 sm:grid-cols-4 mt-2">
          {TITLE_OPTIONS.map((title: any, index: number) => (
            <RadioGroup.Option
              key={index}
              value={title}
              className={({ checked }) =>
                `${checked ? 'border-skin-primary' : 'border-skin-base'}
                  border-2 relative shadow-md focus:outline-none rounded-md p-5 block cursor-pointer min-h-[60px] h-full group address__box`
              }
            >
              <RadioGroup.Label
                as="h3"
                className="text-skin-base font-semibold"
              >
                {title}
              </RadioGroup.Label>
            </RadioGroup.Option>
          ))}
        </div>
      </RadioGroup>
      {error && <p className="text-skin-red">{error}</p>}
    </>
  );
}

type FormValues = {
  title: string;
  address: {
    recipient: string;
    email: string;
    contact_number: string;
    fax: string;
    city: string;
    zip: string;
    street_address: string;
    [key: string]: any;
  };
  state: {
    id: string | number;
    name: string;
  };
  default: boolean;
};

const CreateOrUpdateAddressForm = () => {
  const { t } = useTranslation();
  const { customer } = useCustomer();
  const { data } = useModalState();
  const address = data && data.address ? data.address : null;
  const customerId = data && data.customerId ? data.customerId : null;
  const addressType = data && data.type ? data.type : null;
  const { billing_address, shipping_address } = useCart();
  const { setShipToBillAddress, ship_to_bill_address, checkoutMutate } =
    useCheckout();
  const { tng_user_info } = useCheckout();
  const { closeModal } = useModalAction();
  const { addresses, setAddresses } = useCheckout();
  const { mutate: updateAddress, isLoading } = useSaveAddressMutation();
  const methods = useForm<FormValues>({
    resolver: yupResolver(
      yup.object().shape({
        title: yup.string().required(t('forms.title-required')),
        address: yup.object().shape({
          recipient: yup.string().required(t('forms.name-required')),
          contact_number: yup
            .string()
            .matches(
              /^(\+?6?01)[02-46-9][-]?[\s]?[0-9]{3}[\s]?[0-9]{4}$|^(\+?6?01)[1][-]?[\s]?[0-9]{4}[\s]?[0-9]{4}$|^(\+?6?03)[-]?[\s]?[0-9]{4}[\s]?[0-9]{4}$/,
              t('forms.phone-error')
            )
            .required(t('forms.contact-number-required')),
          email: yup
            .string()
            .email(t('forms.email-error'))
            .required(t('forms.email-required')),
          city: yup
            .string()
            .required(t('forms.city-required'))
            .min(3, t('forms.city-min-length')),
          zip: yup
            .string()
            .required(t('forms.zip-required'))
            .max(5, 'Zip code should have max. 5 digits')
            .test(
              'zip',
              'Zip code should be all digits',
              (value: string | undefined) => !value || /^[\d]{1,5}$/.test(value)
            ),
          street_address: yup
            .string()
            .required(t('forms.street-required'))
            .min(10, t('forms.street-min-length')),
        }),
        state: yup.object().shape({
          id: yup.number().required(t('forms.state-required')),
          name: yup.string().required(t('forms.state-required')),
        }),
      })
    ),
    defaultValues: {
      title:
        address && address?.title
          ? TITLE_OPTIONS.find((title) => title == address.title) ?? ''
          : '',
      ...(address && address?.address
        ? {
            ...address,
            title:
              address && address?.title
                ? TITLE_OPTIONS.find((title) => title == address.title) ?? ''
                : '',
            address: {
              ...address.address,
              ...(!address.address.email
                ? tng_user_info
                  ? { email: tng_user_info.email }
                  : customer && customer.email
                  ? { email: customer.email }
                  : { email: '' }
                : { email: address.address.email }),
            },
          }
        : {
            address: {
              recipient: tng_user_info
                ? tng_user_info.name
                : customer
                ? customer.name
                : '',
              contact_number: tng_user_info
                ? tng_user_info.phone
                : customer && customer.phone
                ? customer.phone
                : '',
              email: tng_user_info
                ? tng_user_info.email
                : customer && customer.email
                ? customer.email
                : '',
            },
          }),
      ...(address && {
        state: {
          id: address?.state_id ? address.state_id : null,
          name:
            address?.address && address.address?.state
              ? address.address.state
              : null,
        },
      }),
      default: (address && address?.default) == true,
    },
  });

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = methods;

  const title = watch('title');

  function setCartAddress(address: any) {
    if (address) {
      if (addressType === 'billing') {
        if (!billing_address || address.id === billing_address.id) {
          checkoutMutate({
            billing_address: address,
            ...(ship_to_bill_address ? { shipping_address: address } : {}),
          });
        }
      } else if (addressType === 'shipping') {
        if (!shipping_address || address.id === shipping_address.id) {
          checkoutMutate({
            shipping_address: address,
            ...(ship_to_bill_address ? { billing_address: address } : {}),
          });
        }
      }
    }
  }

  function onSubmit(values: FormValues) {
    if (customerId) {
      const formattedInput = {
        id: address?.id,
        customer_id: customerId,
        title: values.title,
        address: {
          ...(address?.id ? { id: address.id } : {}),
          ...values.address,
          contact_number: remapPhoneNumber(
            values.address.contact_number.replace(/-|\s/g, '')
          ), // trim spaces and dashes
          email: remapEmail(values.address.email),
          state: values.state.name,
        },
        state_id: values.state.id,
        type: address ? address.type : addressType,
        default: values.default,
      };

      updateAddress(
        {
          ...formattedInput,
        },
        {
          onSuccess(data: any) {
            if (data) {
              if (data.success == false) {
                toast.error('Failed to save address');
              } else {
                setCartAddress(data);
                toast.success('Address saved');
                closeModal();
              }
            }
          },
        }
      );
    } else {
      const addressInput = {
        customer_id: null,
        title: values.title,
        address: {
          ...values.address,
          contact_number: remapPhoneNumber(
            values.address.contact_number.replace(/-|\s/g, '')
          ), // trim spaces and dashes
          email: remapEmail(values.address.email),
          state: values.state.name,
        },
        state_id: values.state.id,
        default: values.default,
      };
      let new_address = null;
      if (address?.id) {
        new_address = {
          ...addressInput,
          id: address?.id,
          type: address?.type,
        };
        const addressIndex = addresses.findIndex(
          (address: any) => address.id == address?.id
        );
        if (addressIndex > -1) {
          addresses[addressIndex] = new_address;
          setAddresses(addresses);
        }
      } else {
        new_address = {
          ...addressInput,
          id: `guest_${addresses.length + 1}`,
          type: addressType,
        };
        addresses.push(new_address);
        setAddresses(addresses);
      }
      setCartAddress(new_address);
      closeModal();
    }
  }
  return (
    <div className="w-full md:w-[508px] mx-auto p-5 sm:p-8 bg-white rounded-xl relative">
      <CloseButton onClick={closeModal} className="block md:hidden absolute" />
      <h1 className="text-heading font-semibold text-lg text-center mb-4 sm:mb-6">
        {address ? t('text-update') : t('text-add-new')} {t('text-address')}
      </h1>
      <FormProvider {...methods}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          noValidate
          className="grid grid-cols-2 gap-5 h-full"
        >
          <Input
            label={`${t('forms.label-name')}`}
            {...register('address.recipient')}
            placeholder={t('forms.placeholder-name')}
            error={errors.address?.recipient?.message!}
            autoComplete="full-name"
            variant="solid"
            className="col-span-2"
          />

          <Input
            label={`${t('forms.label-email')} *`}
            {...register('address.email')}
            placeholder={t('forms.placeholder-email')}
            error={errors.address?.email?.message!}
            variant="solid"
            className="col-span-2"
          />

          <PhoneNumberInput
            {...register('address.contact_number')}
            control={control}
            label={`${t('forms.label-contact-number')} *`}
            className="flex-1 col-span-2"
            variant="outline"
            placeholder={t('forms.placeholder-contact-number')}
            error={errors.address?.contact_number?.message!}
            autoComplete="tel"
            onChange={(e: any) => {
              //@ts-ignore
              setValue('address.contact_number', e.target.value);
            }}
          />

          <TextArea
            label={`${t('forms.label-street-address')} *`}
            {...register('address.street_address')}
            placeholder={t('forms.placeholder-street-address')}
            error={errors.address?.street_address?.message!}
            autoComplete="address"
            variant="solid"
            className="col-span-2"
          />

          <Input
            label={`${t('forms.label-city')} *`}
            {...register('address.city')}
            placeholder={t('forms.placeholder-city')}
            error={errors.address?.city?.message!}
            variant="solid"
          />

          <Input
            label={`${t('forms.label-zip')} *`}
            {...register('address.zip')}
            placeholder={t('forms.placeholder-zip')}
            autoComplete="postal-code"
            error={errors.address?.zip?.message!}
            variant="solid"
          />

          <div className="col-span-2">
            <SelectState
              control={control}
              setValue={setValue}
              error={errors.state?.id?.message!}
            />
          </div>

          <div className="col-span-2">
            <SelectTitle error={errors.title?.message!} />
          </div>

          {title == 'Office' && (
            <>
              <Input
                label={t('forms.label-company')}
                {...register('address.company')}
                placeholder={t('forms.placeholder-company')}
                autoComplete="organization"
                variant="solid"
                className="col-span-2"
              />

              <PhoneNumberInput
                {...register('address.fax')}
                control={control}
                label={t('forms.label-fax-number')}
                className="flex-1 col-span-2"
                variant="outline"
                placeholder={t('forms.placeholder-fax')}
                autoComplete="fax"
                onChange={(e: any) => {
                  //@ts-ignore
                  setValue('address.fax', e.target.value);
                }}
              />
            </>
          )}

          <Checkbox
            label={t('forms.label-set-default')}
            {...register('default')}
            className="col-span-2 my-2"
          />

          <Button className="w-full col-span-2" loading={isLoading}>
            {address ? t('text-update') : t('text-save')} {t('text-address')}
          </Button>
        </form>
      </FormProvider>
    </div>
  );
};

export default CreateOrUpdateAddressForm;
