import { DetailedHTMLProps, FormHTMLAttributes, useEffect } from 'react';
import { useRef } from 'react';

export interface AutoSubmitFormProps
  extends DetailedHTMLProps<
    FormHTMLAttributes<HTMLFormElement>,
    HTMLFormElement
  > {
  children?: any;
}

const AutoSubmitForm = ({
  method,
  action,
  hidden = true,
  children,
}: AutoSubmitFormProps) => {
  const formRef = useRef<HTMLFormElement>(null);
  useEffect(() => {
    if (formRef.current) {
      formRef.current.submit();
    }
  }, []);
  return (
    <form ref={formRef} method={method} action={action} hidden={hidden}>
      {children}
    </form>
  );
};

export default AutoSubmitForm;
