import FileSearch from '@components/icons/file-search';
import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';
interface Props {
  text?: string;
  noImage?: boolean;
  className?: string;
}

const NoResult: React.FC<Props> = ({
  text = 'text-no-result-found',
  noImage = false,
  className = 'w-8 h-8',
}) => {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center py-4">
      <FileSearch className={className} />
      {text && (
        <h3
          className={cn(
            'w-full text-center text-base lg:text-xl font-semibold text-body mt-2',
            !(noImage === true) ? 'h-1/2' : 'h-full'
          )}
        >
          {t(text)}
        </h3>
      )}
    </div>
  );
};

export default NoResult;
