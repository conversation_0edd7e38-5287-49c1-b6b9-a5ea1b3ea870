import { useCheckout } from '@contexts/checkout.context';
import { fetchOrderPaymentCancel } from '@framework/order/use-order-payment.query';
import { ROUTES } from '@utils/routes';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

export interface PaymentPopupProps {
  url: string;
}

const PaymentPopup: React.FC<PaymentPopupProps> = ({ url }) => {
  const router = useRouter();
  const { current_order_id } = useCheckout();

  useEffect(() => {
    try {
      if (cordova) {
        var inappBrowser = cordova.InAppBrowser.open(
          encodeURI(url),
          '_blank',
          'location=yes,transitionstyle=crossdissolve,hidespinner=yes,toolbarposition=top,closebuttoncaption=Close'
        );
        inappBrowser.addEventListener('loadstop', (e: any) => {
          if (e && e.url && e.url.indexOf(ROUTES.CHECKOUT_CALLBACK) > -1) {
            inappBrowser.close();
            fetchOrderPaymentCancel(current_order_id)
              .then(() => {
                router.replace(ROUTES.CHECKOUT_RESULT);
              })
              .catch(() => {
                router.replace(ROUTES.CHECKOUT_RESULT);
              });
          }
        });
        inappBrowser.addEventListener('exit', () => {
          fetchOrderPaymentCancel(current_order_id)
            .then(() => {
              router.replace(ROUTES.CHECKOUT_RESULT);
            })
            .catch(() => {
              router.replace(ROUTES.CHECKOUT_RESULT);
            });
        });
      }
    } catch (e) {
      var win = window.open(url);
      var timer = setInterval(function () {
        if (win?.closed) {
          clearInterval(timer);
          // cancel payment or payment failed
          fetchOrderPaymentCancel(current_order_id)
            .then(() => {
              router.replace(ROUTES.CHECKOUT_RESULT);
            })
            .catch(() => {
              router.replace(ROUTES.CHECKOUT_RESULT);
            });
        }
      }, 1000);
    }
  }, []);
  return <></>;
};

export default PaymentPopup;
