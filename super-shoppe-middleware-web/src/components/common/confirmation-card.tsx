import Button from '@components/ui/button';
import { useTranslation } from 'next-export-i18n';
import cn from 'classnames';
import TrashIcon from '@components/icons/trash-icon';

type ConfirmationCardProps = {
  onCancel: () => void;
  onDelete: () => void;
  title?: string;
  icon?: any;
  description?: string;
  note?: string;
  cancelBtnClassName?: string;
  deleteBtnClassName?: string;
  cancelBtnText?: string;
  deleteBtnText?: string;
  cancelBtnLoading?: boolean;
  deleteBtnLoading?: boolean;
};

const ConfirmationCard: React.FC<ConfirmationCardProps> = ({
  onCancel,
  onDelete,
  icon,
  title = 'button-delete',
  description = 'delete-item-confirm',
  note,
  cancelBtnText = 'button-cancel',
  deleteBtnText = 'button-delete',
  cancelBtnClassName = 'bg-primary hover:bg-primary-hover focus:bg-primary-hover text-light',
  deleteBtnClassName = 'bg-red-600 hover:bg-red-700',
  cancelBtnLoading,
  deleteBtnLoading,
}) => {
  const { t } = useTranslation();
  return (
    <div className="w-full md:w-[508px] mx-auto p-5 sm:p-8 bg-white rounded-xl">
      <div className="w-full h-full text-center">
        <div className="flex h-full flex-col justify-between">
          {icon ? (
            icon
          ) : (
            <TrashIcon className="mt-4 w-12 h-12 m-auto text-skin-primary" />
          )}
          <p className="text-heading text-xl font-bold mt-4">{t(title)}</p>
          <p className="text-body-dark dark:text-muted leading-relaxed py-2 px-6">
            {t(description)}
          </p>
          {note ? (
            <p className="text-sm text-body-dark dark:text-muted font-semibold leading-relaxed pb-2 px-6">
              {t(note)}
            </p>
          ) : <></>}
          <div className="flex items-center justify-between space-s-4 w-full mt-8">
            <div className="w-1/2">
              <Button
                onClick={onCancel}
                loading={cancelBtnLoading}
                disabled={cancelBtnLoading}
                variant="border"
                className={cn(
                  'w-full py-2 px-4 focus:outline-none transition ease-in duration-200 text-center text-base font-semibold rounded shadow-md',
                  cancelBtnClassName
                )}
              >
                {t(cancelBtnText)}
              </Button>
            </div>

            <div className="w-1/2">
              <Button
                onClick={onDelete}
                loading={deleteBtnLoading}
                disabled={deleteBtnLoading}
                variant="primary"
                className={cn(
                  'w-full py-2 px-4 focus:outline-none focus:bg-red-700 text-light transition ease-in duration-200 text-center text-base font-semibold rounded shadow-md',
                  deleteBtnClassName
                )}
              >
                {t(deleteBtnText)}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationCard;
