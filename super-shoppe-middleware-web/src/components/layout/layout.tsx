import Header from '@components/layout/header/header';
import Footer from '@components/layout/footer/footer';
import MobileNavigation from '@components/layout/mobile-navigation/mobile-navigation';
import { useRouter } from 'next/router';
import { useUI } from '@contexts/ui.context';
import { useEffect, useState } from 'react';
import PageLoader from '@components/ui/loaders/page-loader';
import { Waypoint } from 'react-waypoint';
import { motion } from 'framer-motion';
import { animateScroll as scroll } from 'react-scroll';
import { BsChevronDoubleUp } from 'react-icons/bs';

const Layout: React.FC<{ hideFooter?: boolean; isHome?: boolean; withSearchBar?: boolean; withBackButton?: boolean; }> = ({
  hideFooter = false,
  isHome = false,
  withSearchBar = false,
  withBackButton = false,
  children,
}) => {
  const router = useRouter();
  const { displayPageLoader, showPageLoader, hidePageLoader } = useUI();
  const [showTopButton, setShowTopButton] = useState(false);
  const onWaypointPositionChange = ({
    currentPosition,
  }: Waypoint.CallbackArgs) => {
    if (!currentPosition || currentPosition === 'above') {
      setShowTopButton(true);
    } else {
      setShowTopButton(false);
    }
  };

  useEffect(() => {
    const handleStart = (url: string) =>
      url !== router.asPath && showPageLoader();
    const handleComplete = () => hidePageLoader();

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleComplete);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleComplete);
    };
  });

  if (displayPageLoader) {
    return <PageLoader />;
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header isHome={isHome} withSearchBar={withSearchBar} withBackButton={withBackButton} />
      <main
        className="relative flex-grow"
        style={{
          WebkitOverflowScrolling: 'touch',
        }}
      >
        <span className="absolute">
          <Waypoint onPositionChange={onWaypointPositionChange} />
        </span>
        {showTopButton && (
          <motion.button
            type="button"
            className="scrollToTopButton fixed rounded transition-all bg-blue-500 bg-opacity-50 bottom-20 lg:bottom-8 right-4 lg:right-8 text-white p-2 lg:p-4 hover:bg-opacity-75 focus:bg-opacity-75 z-50"
            onClick={() => scroll.scrollToTop()}
          >
            <BsChevronDoubleUp />
          </motion.button>
        )}
        {children}
      </main>
      {/* {!hideFooter ? <Footer /> : <></>} */}
      <MobileNavigation />
    </div>
  );
};

export default Layout;
