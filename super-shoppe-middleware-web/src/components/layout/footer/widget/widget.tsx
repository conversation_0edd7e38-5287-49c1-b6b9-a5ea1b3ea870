import Container from '@components/ui/container';
import WidgetLink from './widget-link';
import WidgetSubscription from './widget-subscription';
import { footer } from '../data';
import dynamic from 'next/dynamic';

const WidgetAbout = dynamic(() => import('./widget-about-us'), {
  ssr: false,
});

interface WidgetsProps {
  widgets: {
    id: number;
    widgetTitle: string;
    lists: any;
    details?: any;
  }[];
}

const Widgets: React.FC<WidgetsProps> = ({ widgets }) => {
  const { social } = footer;
  return (
    <Container className="flex flex-col sm:flex-row gap-5 sm:gap-9 lg:gap-11 xl:gap-7 pb-[50px]">
      <WidgetAbout
        social={social}
        className="w-full sm:w-1/2 md:w-1/3 lg:w-1/5 xl:w-1/3 border-b sm:border-b-0 border-skin-three mb-4 sm:mb-0"
      />
      <div className="flex flex-1 flex-wrap gap-5 sm:gap-9 lg:gap-11 xl:gap-7">
        {widgets?.map((widget) => (
          <WidgetLink
            key={`footer-widget--key${widget.id}`}
            data={widget}
            className="flex-auto sm:flex-1 pb-3.5 sm:pb-0"
          />
        ))}
      </div>
    </Container>
  );
};

export default Widgets;
