import ErrorInformation from "@components/404/error-information";
import TextLoader from "@components/ui/loaders/text-loader";
import Text from "@components/ui/text";
import { useStaticBlockQuery } from "@framework/static-block/get-static-block.query";

interface AboutUsBlockProps {}

const AboutUsBlock = ({}: AboutUsBlockProps) => {
  const {
    data,
    isLoading,
    error,
  } = useStaticBlockQuery("footer");
  if (isLoading || !data) {
    return <TextLoader />;
  }

  if (error) {
    return <ErrorInformation />;
  }

  return (
    <Text>
        <div
            dangerouslySetInnerHTML={{
                __html: data.value ?? '',
            }}
        />
    </Text>
  );
}

export default AboutUsBlock;