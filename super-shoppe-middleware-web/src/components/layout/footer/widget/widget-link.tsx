import { useTranslation } from 'next-export-i18n';
import Heading from '@components/ui/heading';
import Link from '@components/ui/link';
import { getIsTNG } from '@utils/use-tng';
import { useStaticBlocksQuery } from '@framework/static-block/get-static-blocks.query';
import { SORT_TYPE } from '@framework/types';

type WidgetListItemProps = {
  id: string | number;
  path?: any;
  title: string;
  icon?: any;
  hideInWebView?: boolean;
}

interface Props {
  className?: string;
  data: {
    widgetTitle?: string;
    lists: WidgetListItemProps[];
    details?: {
      title: string;
      type?: string;
      description?: string[] | { title: string; description: string }[];
    }[];
    isCustomListing?: boolean;
  };
}

const WidgetLink: React.FC<Props> = ({ className, data }) => {
  const { widgetTitle, lists, isCustomListing, details } = data;
  const { t } = useTranslation();
  return (
    <div className={`${className}`}>
      <Heading variant="mediumHeading" className="mb-4 sm:mb-5 lg:mb-6 pb-0.5">
        {t(widgetTitle)}
      </Heading>
      {isCustomListing ? <CustomWidgetListing /> : <WidgetItemList list={lists} />}
      {lists.length <= 0 && details && (
        <ul className="text-sm lg:text-15px flex flex-col">
          {details.map((item, idx) => (
            <div key={`widget-details--key${idx}`}>
              <li className="flex items-baseline mb-3">
                {!!item.type && item.type === 'html' ? (
                  <p
                    className="font-semibold transition-colors duration-200 hover:text-skin-base"
                    dangerouslySetInnerHTML={{ __html: item.title }}
                  ></p>
                ) : (
                  <p className="font-semibold transition-colors duration-200 hover:text-skin-base">
                    {t(item.title)}
                  </p>
                )}
              </li>
              {item.description &&
                item.description.length > 0 &&
                item.description.map((item, idx) => (
                  <li
                    key={`widget-details-desc--key${idx}`}
                    className="flex text-xs xs:text-sm items-baseline w-full mb-1"
                  >
                    {typeof item === 'string' ? (
                      <span>{item}</span>
                    ) : (
                      <div className="flex w-full xs:w-52 justify-start">
                        <span className="w-20">{item.title}:</span>
                        <span className="">{item.description}</span>
                      </div>
                    )}
                  </li>
                ))}
            </div>
          ))}
        </ul>
      )}
    </div>
  );
};

const CustomWidgetListing: React.FC<{}> = () => {
  const { data } = useStaticBlocksQuery({
    limit: 100,
    show_in_footer: true,
    order_by: "order",
    sorted_by: SORT_TYPE.ASC
  });
  return (
    <WidgetItemList list={
      data
        ? data.map(({ id, name, key }) => ({
            id,
            path: {
              pathname: '/p',
              query: {
                _s: key,
              },
            }, // custom page prefix
            title: name,
          }))
        : []
    } />
  )
}

const WidgetItemList: React.FC<{
  list: WidgetListItemProps[]
}> = ({
  list
}) => {
  const { t } = useTranslation();
  return (
    list.length > 0 ? (
      <ul className="text-sm lg:text-15px flex flex-col space-y-3">
        {list
          .filter(
            ({ hideInWebView }) =>
              !hideInWebView || (hideInWebView && !getIsTNG())
          )
          .map((item) => (
            <li
              key={`widget-list--key${item.id}`}
              className="flex text-xs sm:text-sm items-baseline"
            >
              {item.icon && (
                <span className="me-3 relative top-0.5 lg:top-1 text-sm lg:text-base">
                  {item.icon}
                </span>
              )}

              <Link
                href={item.path ? item.path : '#!'}
                className="transition-colors duration-200 hover:text-skin-base"
              >
                {t(item.title)}
              </Link>
            </li>
          ))}
      </ul>
    ) : <></>
  );
}

export default WidgetLink;
