import { ROUTES } from '@utils/routes';

export const footer = {
  widgets: [
    {
      id: 2,
      widgetTitle: 'footer.widget-title-contact',
      lists: [],
      details: [
        {
          title: `
            <span class="flex mb-4">
              <i class="flex items-center mr-2">
                <svg aria-hidden="true" focusable="false" role="presentation" class="w-5 h-5" viewBox="0 0 24 24"><path d="M16.053 3.083a10.212 10.212 0 0 1 3.174 2.148c.905.905 1.621 1.963 2.148 3.174s.791 2.507.791 3.887-.264 2.676-.791 3.887-1.244 2.269-2.148 3.174c-.905.905-1.963 1.621-3.174 2.148s-2.507.791-3.887.791-2.676-.264-3.887-.791-2.269-1.243-3.174-2.148c-.905-.905-1.621-1.963-2.148-3.174s-.791-2.506-.791-3.887c0-1.38.264-2.676.791-3.887S4.2 6.136 5.105 5.231c.906-.905 1.964-1.621 3.175-2.148s2.506-.791 3.887-.791c1.38 0 2.675.263 3.886.791zm-3.261 17.92a8.722 8.722 0 0 0 5.557-2.529 8.728 8.728 0 0 0 2.529-5.557h-2.461a.598.598 0 0 1-.439-.186.6.6 0 0 1-.186-.439.6.6 0 0 1 .186-.439.6.6 0 0 1 .439-.186h2.461a8.742 8.742 0 0 0-2.529-5.557 8.742 8.742 0 0 0-5.557-2.529v2.461a.598.598 0 0 1-.186.439.601.601 0 0 1-.439.186.598.598 0 0 1-.439-.186.6.6 0 0 1-.186-.439V3.581A8.728 8.728 0 0 0 5.985 6.11a8.722 8.722 0 0 0-2.529 5.557h2.461a.6.6 0 0 1 .439.186.601.601 0 0 1 .186.439.598.598 0 0 1-.186.439.601.601 0 0 1-.439.186H3.456a8.614 8.614 0 0 0 .781 3.047c.442.951 1.025 1.787 1.748 2.51s1.559 1.306 2.51 1.748c.95.443 1.966.703 3.047.781v-2.461a.6.6 0 0 1 .186-.439.6.6 0 0 1 .439-.186.6.6 0 0 1 .439.186.601.601 0 0 1 .186.439v2.461zm3.564-9.151a.601.601 0 0 1 .186.439.598.598 0 0 1-.186.439.601.601 0 0 1-.439.186h-3.75a.58.58 0 0 1-.283-.068.736.736 0 0 1-.225-.186l-3.75-5a.56.56 0 0 1-.117-.458.609.609 0 0 1 .254-.42.57.57 0 0 1 .459-.117.61.61 0 0 1 .419.254l3.555 4.746h3.438c.169 0 .315.062.439.185z"></path></svg>
              </i>
              Mon-Fri: 8:30 am - 6:00 pm
            </span>
          `,
          type: 'html',
          description: [],
        },
        {
          title: 'Menara KK (HQ)',
          description: [
            'T6-01-03, Tower 6, Maju Link,',
            'Jalan Lingkaran Tengah 2,',
            'Bandar Tasik Selatan, 57000 Kuala Lumpur.',
          ],
        },
        {
          title: '',
          description: [
            {
              title: 'Careline',
              description: '+6016 - 228 1822',
            },
          ],
        },
      ],
    },
    {
      id: 1,
      widgetTitle: 'footer.widget-title-account',
      lists: [
        {
          id: 1,
          title: 'footer.link-my-account',
          path: ROUTES.ACCOUNT,
        },
        {
          id: 2,
          title: 'footer.link-my-orders',
          path: ROUTES.ORDERS,
        },
        {
          id: 3,
          title: 'footer.link-address',
          path: ROUTES.ADDRESS,
        },
        {
          id: 4,
          title: 'footer.link-wishlist',
          path: ROUTES.WISHLIST,
        },
      ],
    },

    {
      id: 3,
      widgetTitle: 'footer.widget-title-our-information',
      isCustomListing: true,
      lists: [
        {
          id: 1,
          title: 'footer.link-privacy-policy',
          path: ROUTES.PRIVACY_POLICY,
        },
        {
          id: 2,
          title: 'footer.link-terms',
          path: ROUTES.TERMS,
        },
        {
          id: 3,
          title: 'footer.link-delivery-policy',
          path: ROUTES.DELIVERY_POLICY,
        },
        {
          id: 4,
          title: 'footer.link-return-policy',
          path: ROUTES.RETURN_POLICY,
        },
      ],
    },
  ],
  payment: [
    {
      id: 1,
      path: '/',
      image: '/assets/images/payment/mastercard.png',
      name: 'payment-master-card',
      width: 34,
      height: 20,
    },
    {
      id: 2,
      path: '/',
      image: '/assets/images/payment/visa.png',
      name: 'payment-visa',
      width: 50,
      height: 20,
    },
  ],
  social: [
    {
      id: 1,
      path: 'https://www.facebook.com/kksupermart/',
      image: '/assets/images/social/facebook.svg',
      name: 'facebook',
      width: 20,
      height: 20,
    },
    {
      id: 2,
      path: 'https://twitter.com/kksupermart/',
      image: '/assets/images/social/twitter.svg',
      name: 'twitter',
      width: 20,
      height: 20,
    },
    {
      id: 3,
      path: 'https://www.instagram.com/kksupermart/',
      image: '/assets/images/social/instagram.svg',
      name: 'instagram',
      width: 20,
      height: 20,
    },
    {
      id: 4,
      path: 'https://www.youtube.com/channel/UCx6mBM2lmeODgo61EaUdfGw',
      image: '/assets/images/social/youtube.svg',
      name: 'youtube',
      width: 20,
      height: 20,
    },
    {
      id: 5,
      path: 'https://vt.tiktok.com/ZSRRddTuS/',
      image: '/assets/images/social/tiktok.svg',
      name: 'tiktok',
      width: 20,
      height: 20,
    },
  ],
};
