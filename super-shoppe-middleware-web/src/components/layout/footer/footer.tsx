import Widgets from '@components/layout/footer/widget/widget';
import { footer } from './data';
import dynamic from 'next/dynamic';

const Copyright = dynamic(() => import('@components/layout/footer/copyright'), {
  ssr: false,
});

const { widgets, payment } = footer;

const Footer: React.FC = () => (
  <footer className="mt-[50px] lg:mt-14 2xl:mt-16 safe-area-bottom">
    <Widgets widgets={widgets} />
    <Copyright payment={payment} />
  </footer>
);

export default Footer;
