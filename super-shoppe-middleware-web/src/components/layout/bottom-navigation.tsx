import React from 'react';
import { useRouter } from 'next/router';
import { useCurrentUser } from '../../hooks/use-current-user';

// Icons for bottom navigation
const OrdersIcon = ({ active }: { active: boolean }) => (
  <svg
    className={`w-6 h-6 ${active ? 'text-blue-600' : 'text-gray-400'}`}
    fill={active ? 'currentColor' : 'none'}
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
    />
  </svg>
);

const ScanIcon = ({ active }: { active: boolean }) => (
  <svg
    className={`w-6 h-6 ${active ? 'text-blue-600' : 'text-gray-400'}`}
    fill={active ? 'currentColor' : 'none'}
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
    />
  </svg>
);

const AccountIcon = ({ active }: { active: boolean }) => (
  <svg
    className={`w-6 h-6 ${active ? 'text-blue-600' : 'text-gray-400'}`}
    fill={active ? 'currentColor' : 'none'}
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
    />
  </svg>
);

const BottomNavigation: React.FC = () => {
  const router = useRouter();
  const { user } = useCurrentUser();

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  if (!user) {
    return null; // Don't show bottom nav if user is not logged in
  }

  const navItems = [
    {
      id: 'orders',
      label: 'Orders',
      icon: OrdersIcon,
      path: '/orders',
      onClick: () => handleNavigation('/orders'),
    },
    {
      id: 'scan',
      label: 'Scan',
      icon: ScanIcon,
      path: '/scan',
      onClick: () => handleNavigation('/scan'),
    },
    {
      id: 'account',
      label: 'Account',
      icon: AccountIcon,
      path: '/account',
      onClick: () => handleNavigation('/account'),
    },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2 py-1 z-50">
      <div className="flex justify-around items-center max-w-sm mx-auto">
        {navItems.map((item) => {
          const isActive = router.pathname === item.path;
          const IconComponent = item.icon;

          return (
            <button
              key={item.id}
              onClick={item.onClick}
              className="flex flex-col items-center justify-center py-2 px-2 min-w-0 flex-1 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <IconComponent active={isActive} />
              <span
                className={`text-xs mt-1 font-medium ${
                  isActive ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
      {/* Safe area for devices with home indicator */}
      <div className="h-safe-area-inset-bottom"></div>
    </div>
  );
};

export default BottomNavigation;
