import Link from '@components/ui/link';
import MenuIcon from '@components/icons/menu-icon';
import HomeIcon from '@components/icons/home-icon';
import { useUI } from '@contexts/ui.context';
import { useRouter } from 'next/router';
import { ROUTES } from '@utils/routes';
import dynamic from 'next/dynamic';
import { Drawer } from '@components/common/drawer/drawer';
import { getDirection } from '@utils/get-direction';
import { useTranslation } from 'next-export-i18n';
import CategoryButton from '@components/category/category-button';
import AuthButton from '@components/auth/auth-button';
import { IoQrCode } from 'react-icons/io5';
import { siteSettings } from '@settings/site-settings';
const CartButton = dynamic(() => import('@components/cart/cart-button'), {
  ssr: false,
});
const CatalogueFilterMenu = dynamic(
  () => import('@components/layout/header/catalogue-filter-menu')
);
// const MobileMenu = dynamic(
//   () => import('@components/layout/header/mobile-menu')
// );

const BottomNavigation: React.FC = () => {
  const { t } = useTranslation();
  const { openSidebar, closeSidebar, displaySidebar } = useUI();
  // const { locale } = useRouter();
  const dir = getDirection('en');
  const contentWrapperCSS = dir === 'ltr' ? { left: 0 } : { right: 0 };
  function handleMobileMenu() {
    return openSidebar();
  }

  return (
    <>
      {/* <div className="lg:hidden fixed z-30 bottom-0 bg-skin-fill w-full h-18 safe-area-bottom">
        <div className="flex items-end justify-center shadow-bottomNavigation body-font px-4 md:px-6 lg:px-8 text-skin-muted py-2 pb-3">
          <div className="flex-1 flex items-end justify-between">
            <button
              aria-label="Menu"
              className="flex flex-col items-center justify-center flex-shrink-0 text-xs outline-none focus:outline-none"
              onClick={handleMobileMenu}
            >
              <MenuIcon className="h-6 pb-1" />
              {t('text-menu')}
            </button>
            <CategoryButton
              className="flex-col lg:flex-row"
              iconClassName="w-7 h-7 pb-0.5 text-opacity-100"
              labelClassName="text-skin-muted text-xs"
            />
            <div></div>
          </div>
          <Link
            href={ROUTES.KK_COINS}
            className="flex flex-col gap-0.5 items-center justify-center flex-shrink-0 text-xs outline-none focus:outline-none"
          >
            <span className="sr-only">Coins</span>
            <img src={siteSettings.reward_point_icon} className="w-8 h-8" />
            {t('text-kk-coins')}
          </Link>
          <div className="flex-1 flex items-end justify-between">
            <div></div>
            <CartButton
              className="flex-col lg:flex-row"
              iconClassName="pb-1 text-opacity-100"
              labelClassName="text-skin-muted text-xs"
            />
            <AuthButton />
          </div>
        </div>
      </div> */}
      <Drawer
        placement={'right'}
        open={displaySidebar}
        onClose={closeSidebar}
        handler={false}
        showMask={true}
        level={null}
        contentWrapperStyle={{
          right: 0,
        }}
      >
        <CatalogueFilterMenu />
        {/* <MobileMenu /> */}
      </Drawer>
    </>
  );
};

export default BottomNavigation;
