import { useUI } from '@contexts/ui.context';
import { useEffect, RefObject, useCallback, useRef } from 'react';

export function onSwipeGesture<T extends HTMLElement = HTMLElement>(
  ref: RefObject<T>
) {
  const { displayDrawer, editingCart } = useUI();
  const touchState = useRef({
    touchStartXPosition: 0,
    touchCurrentXPosition: 0,
    isDragging: false,
  });

  const handleSlideItem = useCallback((start, end, isEnded = false) => {
    if (ref.current) {
      if(start > 0 && end > 0) {
        let diff = start - end;
        if (diff > 0) {
          if (!ref.current.classList.contains('cart-editing')) {
            diff = diff > 160 ? 160 : diff;
            if (isEnded) diff = diff >= 60 ? 160 : 0;
            ref.current.style.transform = `translate3d(${-diff}px, 0, 0)`;
            if (isEnded && diff >= 160) {
              ref.current.classList.add('cart-editing');
            }
          } else {
            ref.current.style.transform = 'translate3d(-160px, 0, 0)';
          }
        } else {
          if (ref.current.classList.contains('cart-editing')) {
            diff = diff < -160 ? -160 : diff;
            if (isEnded) diff = diff < -60 ? 0 : -160;
            else diff = -(160 + diff);
            ref.current.style.transform = `translate3d(${diff}px, 0, 0)`;
            if (isEnded && diff == 0) {
              ref.current.classList.remove('cart-editing');
            }
          } else {
            ref.current.style.transform = 'translate3d(0px, 0, 0)';
          }
        }
      }
    }
  }, []);

  const handleTouchStart = useCallback((event) => {
    touchState.current = {
      ...touchState.current,
      isDragging: true,
    };
  }, []);

  const handleTouchMove = useCallback(
    (event) => {
      if (touchState.current.isDragging === true && event.clientX) {
        if (!touchState.current.touchStartXPosition) {
          touchState.current = {
            ...touchState.current,
            touchStartXPosition: event.clientX,
          };
        } else {
          touchState.current = {
            ...touchState.current,
            touchCurrentXPosition: event.clientX,
          };
          if (Math.abs(event.clientX) > 10) {
            handleSlideItem(
              touchState.current.touchStartXPosition,
              event.clientX
            );
          }
        }
      }
    },
    [
      touchState.current.isDragging,
      touchState.current.touchStartXPosition,
      touchState.current.touchCurrentXPosition,
    ]
  );

  const handleTouchMoveOverride = useCallback(
    (event) => {
      handleTouchMove({ clientX: event.touches[0].clientX });
    },
    [
      touchState.current.isDragging,
      touchState.current.touchStartXPosition,
      touchState.current.touchCurrentXPosition,
    ]
  );

  const handleTouchEnd = useCallback(
    (event) => {
      if (touchState.current.isDragging) {
        handleSlideItem(
          touchState.current.touchStartXPosition,
          touchState.current.touchCurrentXPosition,
          true
        );
        touchState.current = {
          ...touchState.current,
          touchStartXPosition: 0,
          touchCurrentXPosition: 0,
          isDragging: false,
        };
      }
    },
    [
      touchState.current.isDragging,
      touchState.current.touchStartXPosition,
      touchState.current.touchCurrentXPosition,
    ]
  );

  useEffect(() => {
    if (ref.current) {
      ref.current.addEventListener('mousedown', handleTouchStart);
      ref.current.addEventListener('mousemove', handleTouchMove);
      ref.current.addEventListener('mouseup', handleTouchEnd);
      ref.current.addEventListener('touchstart', handleTouchStart);
      ref.current.addEventListener('touchmove', handleTouchMoveOverride);
      ref.current.addEventListener('touchend', handleTouchEnd);
    }
    return () => {
      if (ref.current) {
        ref.current.removeEventListener('mousedown', handleTouchStart);
        ref.current.removeEventListener('mousemove', handleTouchMove);
        ref.current.removeEventListener('mouseup', handleTouchEnd);
        ref.current.removeEventListener('touchstart', handleTouchStart);
        ref.current.removeEventListener('touchmove', handleTouchMoveOverride);
        ref.current.removeEventListener('touchend', handleTouchEnd);
      }
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  useEffect(() => {
    if (ref.current) {
      if (displayDrawer && editingCart) {
        ref.current.style.transform = `translate3d(-160px, 0, 0)`;
      } else {
        ref.current.style.transform = `translate3d(0px, 0, 0)`;
      }
    }
  }, [editingCart, displayDrawer]);
}
