function removeFalsy(obj: any) {
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => Boolean(v)));
}

function removeEmpty(obj: any): any {
  return Object.entries(obj)
    .filter(([_, v]) => v != null)
    .reduce(
      (acc, [k, v]) => ({ ...acc, [k]: v === Object(v) ? removeEmpty(v) : v }),
      {}
    );
}

export function formatAddress(address: any) {
  if (!address) return;
  if (address.formatted_address) {
    let formattedAddress = {};
    if (address) {
      formattedAddress = removeEmpty(address);
    }
    return Object.values(formattedAddress).slice(2, 3).reverse().join(', ');
  } else {
    const temp = ['street_address', 'state', 'city', 'zip'].reduce(
      (acc, k) => ({ ...acc, [k]: (address as any)[k] }),
      {}
    );
    const formattedAddress = removeFalsy(temp);
    return Object.values(formattedAddress).join(', ');
  }
}

export function validateAddress(address: any) {
  if(!address.address) return { error: true, message: "Invalid address, please edit address details to proceed." };
  if(!address.address.recipient) return { error: true, message: "Please review name for your address and update to proceed." };
  if(!address.address.email) return { error: true, message: "Please review email for your address and update to proceed." };
  if(!address.address.contact_number) return { error: true, message: "Please review contact details for your address and update to proceed." };
  if(!address.address.street_address) return { error: true, message: "Please edit your street address to proceed." };
  if(!address.address.city) return { error: true, message: "Please review city for your address to proceed." };
  if(!address.address.zip) return { error: true, message: "Please review zip code for your address to proceed." };
  if(!address.state_id) return { error: true, message: "Please review state for your address and update to proceed." };
  return true;
}