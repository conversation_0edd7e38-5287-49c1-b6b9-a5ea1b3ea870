import { useUI } from '@contexts/ui.context';
import { useStaticBlocksQuery } from '@framework/static-block/get-static-blocks.query';
import { SORT_TYPE } from '@framework/types';
import { siteSettings } from '@settings/site-settings';
import { LinkProps } from 'next/link';
import { getIsTNG } from './use-tng';

export interface MenuItemProps {
  id?: string | number;
  path: string | LinkProps['href'];
  label: string;
  subMenu?: {
    id?: string | number;
    path: string | LinkProps['href'];
    label: string;
  };
  requiredAuthorized?: boolean;
  hideInWebView?: boolean;
}

function filterMenuItems(items: MenuItemProps[]) {
  const { isAuthorized } = useUI();
  return items.filter(
    (menu: MenuItemProps) =>
      (menu.requiredAuthorized === true
        ? isAuthorized
        : menu.requiredAuthorized === false
        ? !isAuthorized
        : true) &&
      (!menu.hideInWebView || (menu.hideInWebView && !getIsTNG()))
  );
}

export default function useSideMenu() {
  const { data } = useStaticBlocksQuery({
    limit: 100,
    show_in_sidemenu: true,
    order_by: "order",
    sorted_by: SORT_TYPE.ASC
  });
  return filterMenuItems([
    ...(siteSettings.site_header.menu as MenuItemProps[]),
    ...(data
      ? data.map(({ id, name, key }) => ({
          id,
          path: {
            pathname: '/p',
            query: {
              _s: key,
            },
          }, // custom page prefix
          label: name,
        }))
      : []),
    ...siteSettings.site_header.userAuthLinks,
  ]);
}
