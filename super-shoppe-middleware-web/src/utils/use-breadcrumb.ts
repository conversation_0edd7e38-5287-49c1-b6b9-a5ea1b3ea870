import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Category } from '@framework/types';
import { ROUTES } from './routes';

export function convertBreadcrumbTitle(string: string) {
  return string
    .replace(/-/g, ' ')
    .replace(/oe/g, 'ö')
    .replace(/ae/g, 'ä')
    .replace(/ue/g, 'ü')
    .toLowerCase();
}

export function generateBreadcrumbFromCategory(category: Category) {
  const { name, slug, ancestors } = category;
  const breadcrumbs = [];
  let pageSlug = '';
  if (ancestors && ancestors.length > 0) {
    ancestors.forEach((category) => {
      pageSlug += category.slug;
      breadcrumbs.push({
        href: {
          pathname: ROUTES.CATEGORY_VIEW,
          query: {
            categories: pageSlug,
          },
        },
        name: category.name,
      });
      pageSlug += ',';
    });
  }
  breadcrumbs.push({ href: pageSlug + slug, name });
  return breadcrumbs;
}

export default function useBreadcrumb() {
  const router = useRouter();
  const [breadcrumbs, setBreadcrumbs] = useState<any>(null);
  useEffect(() => {
    if (router) {
      const linkPath =
        router.asPath.indexOf('?') > 0
          ? router.asPath.split('?')[0].split('/')
          : router.asPath.split('/');
      linkPath.shift();

      const pathArray = linkPath.map((path, i) => {
        return {
          breadcrumb: path,
          href: '/' + linkPath.slice(0, i + 1).join('/'),
        };
      });

      setBreadcrumbs(pathArray);
    }
  }, [router]);

  return breadcrumbs;
}
