import isEmpty from 'lodash/isEmpty';
interface Item {
  id: string | number;
  name: string;
  slug: string;
  image: {
    thumbnail: string;
    [key: string]: unknown;
  };
  price: number;
  sale_price?: number;
  quantity?: number;
  inventory?: any;
  [key: string]: unknown;
}
interface Variation {
  id: string | number;
  title: string;
  price: number;
  sale_price?: number;
  quantity: number;
  inventory?: any;
  [key: string]: unknown;
}
export function generateCartItem(item: Item, variation: Variation) {
  const {
    id,
    name,
    slug,
    image,
    price,
    sale_price,
    inventory,
    quantity,
    unit,
  } = item;
  if (!isEmpty(variation)) {
    return {
      id,
      name: `${name} - ${variation.title}`,
      slug,
      unit,
      stock: variation.inventory
        ? variation.inventory.quantity
        : variation.quantity,
      keep_available: variation.inventory
        ? variation.inventory.is_continuous_selling
        : false,
      price: variation.price, // variation.sale_price ? variation.sale_price : variation.price,
      image,
      variation_option_id: variation.id,
    };
  }
  return {
    id,
    name,
    slug,
    unit,
    image,
    stock: inventory ? inventory.quantity : quantity,
    keep_available: inventory ? inventory.is_continuous_selling : false,
    price, //: sale_price ? sale_price : price,
  };
}
