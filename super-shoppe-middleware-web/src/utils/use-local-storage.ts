import { Preferences } from '@capacitor/preferences';
import { useEffect, useState } from 'react';

export enum STORAGE_KEY_ENUM {
  CHECKOUT = 'checkout',
  CART = 'cart',
  AUTH_TOKEN = 'auth_token',
  GUEST_TOKEN = 'guest_token',
}

export const useLocalStorage = (key: string, defaultValue: string|any) => {
    const [value, setValue] = useState<string|any>(defaultValue);

    useEffect(() => {
      if (value || typeof value === 'string') {
        let newValue;
        try {
          newValue = JSON.stringify(value);
        } catch(e) {
          newValue = value;
        };
        setStorage(key, newValue);
      } else {
        getStorage(key, defaultValue).then(v => setValue(v));
      };
    }, [key, value, defaultValue]);

    return [value, setValue] as const;
  };

export const setStorage = (key: string, value: any) => {
    return Preferences.set({
        key,
        value
    });
}

export const getStorage = async(key: string, defaultValue: string|any) => {
  let returnValue = undefined;
  const { value } = await Preferences.get({
    key
  });
  try {
    if (value) {
      returnValue = JSON.parse(value);
    };
  } catch(e) {
    returnValue = value;
  };
  if (returnValue) return returnValue;
  if(typeof window != 'undefined' && localStorage) {
    returnValue = localStorage.getItem(key);
    if(returnValue) {
      try {
        returnValue = JSON.parse(returnValue);
      }
      catch(e) {};
      localStorage.removeItem(key);
      let valueToSet;
      try {
        valueToSet = JSON.stringify(returnValue);
      } catch(e) {
        valueToSet = returnValue;
      };
      await setStorage(key, valueToSet);
      return returnValue;
    }
  }
  return returnValue || defaultValue;
};

export const removeStorageItem = (key: string) => {
    Preferences.remove({ key });
}