import {
    AppTrackingTransparency,
    AppTrackingStatusResponse,
} from 'capacitor-plugin-app-tracking-transparency';

async function getStatus(): Promise<AppTrackingStatusResponse> {
    const response = await AppTrackingTransparency.getStatus();

    return response;
}

async function requestPermission(): Promise<AppTrackingStatusResponse> {
    const response = await AppTrackingTransparency.requestPermission();

    return response;
}

export { getStatus, requestPermission }