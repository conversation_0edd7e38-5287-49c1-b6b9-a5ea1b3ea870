import Link from '@components/ui/link';
import TextLoader from '@components/ui/loaders/text-loader';
import { useCustomer } from '@contexts/customer.context';
import { useSettings } from '@contexts/settings.context';
import { parseToFloat } from '@framework/product/use-price';
import { ROUTES } from '@utils/routes';
import React from 'react';

export function useKKCoinsSettings() {
  const { registration_info, referral_info, birthday_info } = useSettings();
  return [
    ...(registration_info ? [{
      id: 1,
      title: `Free ${parseToFloat(registration_info.amount).toFixed(0)} KK Coins`,
      content: `Register and receive ${parseToFloat(registration_info.amount).toFixed(0)} KK Coins instantly as a welcome gift.`,
    }] : []),
    {
      id: 2,
      title: 'Daily Check-in Reward',
      content: 'Check in everyday to earn more coins.',
    },
    {
      id: 3,
      title: '<PERSON>arn <PERSON> Coin',
      content:
        'Earn <PERSON>K Coin with every purchase and enjoy rebates with KK Coin earned.',
    },
    ...(referral_info ? [{
      id: 4,
      title: 'Referral Reward',
      content: <ReferralRewardContent referral_rule={referral_info} />,
      hideInWebView: true,
    }] : []),
    ...(birthday_info ? [{
      id: 5,
      title: 'Birthday Reward',
      content: <BirthdayRewardContent birthday_rule={birthday_info} />,
    }] : []),
  ]
}

const ReferralRewardContent: React.FC<any> = ({ referral_rule }) => {
  const { customer, customerLoading } = useCustomer();
  return (
    <div>
      <span>Earn {parseToFloat(referral_rule.amount).toFixed(0)} KK Coins for each referral.</span>
      {customerLoading ? (
        <TextLoader />
      ) : (
        <>
          {customer ? (
            <div>
              Click{' '}
              <Link href={ROUTES.REFERRAL} className="text-skin-primary">
                here
              </Link>{' '}
              to refer & share referral code to your friends.
            </div>
          ) : (
            <></>
          )}
        </>
      )}
    </div>
  );
};

const BirthdayRewardContent: React.FC<any> = ({ birthday_rule }) => {
  const { customer, customerLoading } = useCustomer();
  return (
    <div>
      <div>Receive free {parseToFloat(birthday_rule.amount).toFixed(0)} KK Coins on your birthday as a reward.</div>
      {customerLoading ? (
        <TextLoader />
      ) : (
        <>
          {customer && !customer.dob ? (
            <div>
              Click{' '}
              <Link href={ROUTES.ACCOUNT_SETTING} className="text-skin-primary">
                here
              </Link>{' '}
              to edit your date of birth to get rewarded.
            </div>
          ) : (
            <></>
          )}
        </>
      )}
    </div>
  );
};