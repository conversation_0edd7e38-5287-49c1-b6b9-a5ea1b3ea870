import { useCustomer } from '../data-graphql/customer';

/**
 * Custom hook to get the current authenticated user
 * This is a convenience wrapper around the GraphQL customer data
 */
export const useCurrentUser = () => {
  const { customer, customerLoading } = useCustomer();

  return {
    user: customer,
    isLoading: customerLoading,
    isAuthenticated: !!customer,
  };
};

export default useCurrentUser;
